# 測試故障排除指南

本文件提供了解決測試過程中常見問題的指南，幫助開發者快速診斷和修復測試失敗。

## 目錄

1. [常見錯誤類型](#常見錯誤類型)
2. [環境相關問題](#環境相關問題)
3. [前端測試問題](#前端測試問題)
4. [後端測試問題](#後端測試問題)
5. [資料庫測試問題](#資料庫測試問題)
6. [WebSocket 測試問題](#websocket-測試問題)
7. [效能和超時問題](#效能和超時問題)
8. [CI 環境問題](#ci-環境問題)
9. [測試工具問題](#測試工具問題)
10. [進階診斷技巧](#進階診斷技巧)

## 常見錯誤類型

### 語法錯誤

**症狀**：測試無法編譯或執行，顯示語法錯誤。

**解決方案**：
1. 檢查錯誤訊息中指示的行號和檔案
2. 確保所有括號、引號和分號正確配對
3. 檢查 TypeScript 類型定義是否正確
4. 運行 `npm run lint` 檢查程式碼問題

### 斷言失敗

**症狀**：測試執行但斷言失敗，顯示預期值和實際值不符。

**解決方案**：
1. 仔細檢查斷言中的預期值和實際值
2. 使用 `console.log` 輸出實際值進行調試
3. 檢查測試數據和測試環境是否正確設置
4. 確認被測試的功能是否有變更

### 非同步錯誤

**症狀**：測試中的非同步操作失敗或超時。

**解決方案**：
1. 確保正確使用 `async/await` 或 Promise
2. 檢查是否正確處理了 Promise 拒絕
3. 增加超時時間：`it('test', async () => {...}, { timeout: 10000 })`
4. 使用 `try/catch` 捕獲非同步錯誤

### 測試間干擾

**症狀**：單獨運行測試成功，但與其他測試一起運行時失敗。

**解決方案**：
1. 確保每個測試後清理所有狀態
2. 檢查是否有全域狀態被多個測試共享
3. 使用 `beforeEach` 和 `afterEach` 重置測試環境
4. 隔離測試資源，如資料庫表或檔案

## 環境相關問題

### Node.js vs jsdom 環境

**症狀**：出現 "document is not defined" 或 "window is not defined" 錯誤。

**解決方案**：
1. 檢查 `vitest.config.ts` 中的 `environmentMatchGlobs` 配置
2. 確保前端測試使用 jsdom 環境，後端測試使用 node 環境
3. 檢查測試檔案路徑是否符合環境配置
4. 在測試中檢查環境：`if (typeof window !== 'undefined') {...}`

### 環境變數問題

**症狀**：測試無法訪問環境變數或環境變數值不正確。

**解決方案**：
1. 確保 `.env.test` 檔案存在並包含必要的變數
2. 檢查測試設置檔案中是否正確載入環境變數
3. 使用 `console.log(process.env)` 檢查環境變數
4. 在測試中手動設置環境變數：`process.env.VAR_NAME = 'value'`

### 路徑解析問題

**症狀**：測試無法找到模組或檔案。

**解決方案**：
1. 檢查 `tsconfig.json` 和 `vitest.config.ts` 中的路徑別名配置
2. 確保使用正確的相對路徑或絕對路徑
3. 檢查檔案大小寫（在 Linux 和 macOS 上區分大小寫）
4. 確保檔案存在並且有正確的擴展名

## 前端測試問題

### Vue 組件掛載問題

**症狀**：Vue 組件無法正確掛載或渲染。

**解決方案**：
1. 確保使用正確的 Vue Test Utils 版本（與 Vue 版本匹配）
2. 檢查組件是否有未滿足的依賴（如 props、provide/inject）
3. 使用 `console.log(wrapper.html())` 檢查渲染輸出
4. 檢查是否正確模擬了 Pinia store 或 Vue Router

### DOM 事件問題

**症狀**：DOM 事件不觸發或處理程序不執行。

**解決方案**：
1. 確保使用正確的事件觸發方法：`await wrapper.find('button').trigger('click')`
2. 等待 DOM 更新：`await nextTick()`
3. 檢查事件處理程序是否正確綁定
4. 確認元素是否可見和可交互

### Pinia Store 測試問題

**症狀**：Pinia store 測試失敗或無法訪問 store。

**解決方案**：
1. 使用 `createTestingPinia()` 創建測試 store
2. 確保正確模擬 store 的初始狀態
3. 檢查 store 的 actions 是否被正確模擬
4. 在測試後重置 store 狀態

### 路由測試問題

**症狀**：Vue Router 相關測試失敗。

**解決方案**：
1. 使用 `createMemoryHistory()` 創建測試路由
2. 模擬路由參數和查詢參數
3. 等待路由變化完成：`await router.isReady()`
4. 檢查路由守衛是否正確執行

## 後端測試問題

### API 路由測試問題

**症狀**：API 路由測試返回 404 或路由不匹配。

**解決方案**：
1. 確保測試伺服器正確啟動並註冊所有路由
2. 檢查請求 URL 是否正確（包括前導斜線）
3. 確認 HTTP 方法是否正確（GET、POST、PUT、DELETE）
4. 使用 `supertest` 的 `.set()` 方法設置必要的標頭

### 中介軟體測試問題

**症狀**：中介軟體不執行或執行順序不正確。

**解決方案**：
1. 確保中介軟體按正確順序註冊
2. 檢查中介軟體是否調用 `next()`
3. 模擬中介軟體所需的請求和響應對象
4. 檢查錯誤處理中介軟體是否正確捕獲錯誤

### 請求體解析問題

**症狀**：無法訪問請求體或請求體解析錯誤。

**解決方案**：
1. 確保正確設置了 `express.json()` 中介軟體
2. 檢查請求內容類型是否正確設置為 `application/json`
3. 確保請求體是有效的 JSON
4. 使用 `supertest` 的 `.send()` 方法發送請求體

### 認證測試問題

**症狀**：認證失敗或無法訪問受保護的路由。

**解決方案**：
1. 創建測試用的認證令牌
2. 確保在請求中包含認證標頭：`.set('Authorization', 'Bearer token')`
3. 模擬認證中介軟體以跳過實際認證
4. 檢查令牌是否過期或格式不正確

## 資料庫測試問題

### 連接問題

**症狀**：無法連接到資料庫或連接超時。

**解決方案**：
1. 確保測試資料庫 URL 正確設置
2. 檢查資料庫是否運行並可訪問
3. 使用專用的測試資料庫而不是生產資料庫
4. 增加連接超時時間

### 遷移和架構問題

**症狀**：表不存在或架構不匹配。

**解決方案**：
1. 確保在測試前運行遷移：`npx prisma db push`
2. 檢查 Prisma 架構是否與資料庫同步
3. 在測試設置中初始化資料庫架構
4. 使用內存資料庫進行測試以避免遷移問題

### 資料完整性問題

**症狀**：外鍵約束錯誤或唯一性約束錯誤。

**解決方案**：
1. 確保按正確順序創建測試資料（先創建父記錄）
2. 使用 `TestDataFactory` 創建有效的測試資料
3. 在測試後清理所有創建的資料
4. 使用事務包裝測試以自動回滾更改

### 並發測試問題

**症狀**：並發測試導致資料衝突或死鎖。

**解決方案**：
1. 為每個測試使用唯一的資料標識符
2. 使用事務隔離測試
3. 避免在測試中使用共享資源
4. 使用 `test.serial()` 串行執行資料庫測試

## WebSocket 測試問題

### 連接問題

**症狀**：WebSocket 連接失敗或無法建立。

**解決方案**：
1. 使用 `MockWebSocket` 替代真實的 WebSocket 連接
2. 確保 WebSocket 伺服器在測試前啟動
3. 檢查 WebSocket URL 是否正確
4. 增加連接超時時間

### 訊息處理問題

**症狀**：WebSocket 訊息未發送或未接收。

**解決方案**：
1. 使用事件監聽器捕獲訊息：`socket.on('message', handler)`
2. 等待訊息處理完成：`await new Promise(resolve => socket.once('message', resolve))`
3. 檢查訊息格式是否正確（JSON 字符串）
4. 確保訊息處理程序正確註冊

### 重連問題

**症狀**：WebSocket 重連邏輯失敗或無限重連。

**解決方案**：
1. 模擬連接斷開：`mockSocket.triggerClose()`
2. 設置最大重連次數
3. 使用計時器模擬重連延遲
4. 檢查重連事件是否正確觸發

### 心跳問題

**症狀**：WebSocket 心跳機制失敗或連接過早關閉。

**解決方案**：
1. 模擬心跳訊息：`mockSocket.triggerMessage(JSON.stringify({ type: 'ping' }))`
2. 調整心跳間隔和超時時間
3. 確保心跳處理程序正確註冊
4. 檢查心跳失敗後的重連邏輯

## 效能和超時問題

### 測試超時

**症狀**：測試執行時間過長或超出超時限制。

**解決方案**：
1. 增加測試超時設置：`it('test', async () => {...}, { timeout: 10000 })`
2. 在 `vitest.config.ts` 中全局增加超時時間：`testTimeout: 10000`
3. 優化測試執行速度，減少不必要的操作
4. 使用 `PerformanceMonitor` 識別效能瓶頸

### 記憶體洩漏

**症狀**：測試執行時記憶體使用量持續增加。

**解決方案**：
1. 確保在測試後清理所有資源
2. 檢查是否有未關閉的連接或檔案句柄
3. 避免在測試中創建大量對象
4. 使用記憶體分析工具識別洩漏

### 並行執行問題

**症狀**：並行執行測試時出現衝突或失敗。

**解決方案**：
1. 確保測試資源隔離（使用唯一標識符）
2. 使用 `test.concurrent()` 標記可並行執行的測試
3. 調整並行執行的最大進程數
4. 對資源密集型測試使用串行執行

### 測試執行順序問題

**症狀**：測試結果依賴於執行順序。

**解決方案**：
1. 確保測試獨立且不依賴其他測試
2. 在每個測試前重置共享狀態
3. 避免在測試間共享可變資源
4. 使用 `test.serial()` 強制特定測試按順序執行

## CI 環境問題

### 環境差異

**症狀**：測試在本地通過但在 CI 中失敗。

**解決方案**：
1. 確保 CI 環境變數與本地環境一致
2. 檢查 CI 使用的 Node.js 版本
3. 在 CI 配置中設置必要的依賴和服務
4. 使用 Docker 容器確保環境一致性

### 資源限制

**症狀**：CI 環境中的測試因資源限制而失敗。

**解決方案**：
1. 減少並行測試數量
2. 優化測試以使用更少的資源
3. 增加 CI 環境的資源配置
4. 將資源密集型測試標記為可選或在本地運行

### 報告生成問題

**症狀**：CI 中無法生成測試報告或覆蓋率報告。

**解決方案**：
1. 確保報告輸出目錄存在且可寫
2. 檢查報告格式配置是否正確
3. 在 CI 配置中添加報告生成步驟
4. 使用 CI 平台支持的報告格式（如 JUnit XML）

### 超時和節流

**症狀**：CI 中的測試因超時或節流而失敗。

**解決方案**：
1. 增加 CI 作業的超時時間
2. 將大型測試套件拆分為多個小型套件
3. 使用測試分片在多個作業中並行執行
4. 優化測試執行速度

## 測試工具問題

### Vitest 配置問題

**症狀**：Vitest 配置不生效或行為異常。

**解決方案**：
1. 檢查 `vitest.config.ts` 和 `vitest.workspace.ts` 的語法
2. 確保配置檔案位於正確的位置
3. 使用 `--config` 參數指定配置檔案
4. 檢查 Vitest 版本是否支持使用的配置選項

### Vue Test Utils 問題

**症狀**：Vue Test Utils 無法正確掛載組件或訪問 DOM。

**解決方案**：
1. 確保 Vue Test Utils 版本與 Vue 版本匹配
2. 檢查組件掛載選項是否正確
3. 使用 `await nextTick()` 等待 DOM 更新
4. 檢查選擇器是否正確匹配元素

### Mock 和 Stub 問題

**症狀**：Mock 或 Stub 不生效或行為異常。

**解決方案**：
1. 確保 Mock 在正確的作用域中應用
2. 檢查 Mock 實現是否正確
3. 在測試後重置所有 Mock：`vi.resetAllMocks()`
4. 使用 `vi.spyOn()` 而不是直接替換方法

### 覆蓋率報告問題

**症狀**：覆蓋率報告不準確或不生成。

**解決方案**：
1. 確保覆蓋率收集已啟用：`--coverage`
2. 檢查覆蓋率提供者配置：`coverage: { provider: 'v8' }`
3. 調整覆蓋率包含和排除模式
4. 確保測試實際執行了被測代碼

## 進階診斷技巧

### 調試模式

使用 Node.js 調試器執行測試：

```bash
node --inspect-brk node_modules/.bin/vitest run
```

然後在 Chrome 中打開 `chrome://inspect` 連接調試器。

### 測試隔離

隔離單個測試以診斷問題：

```bash
npm run test -- --filter="測試名稱"
```

### 詳細日誌

啟用詳細日誌輸出：

```bash
npm run test -- --reporter=verbose
```

### 測試快照

使用快照測試診斷 UI 變化：

```typescript
expect(wrapper.html()).toMatchSnapshot()
```

### 效能分析

使用效能分析工具識別瓶頸：

```typescript
import { PerformanceMonitor } from '../utils/performance-monitor'

const monitor = new PerformanceMonitor()
monitor.start('test-name')
// 執行測試
const result = monitor.stop('test-name')
console.log(`測試執行時間: ${result.duration}ms`)
```

### 記憶體分析

使用 Node.js 內置的記憶體分析工具：

```bash
node --inspect-brk --expose-gc node_modules/.bin/vitest run
```

然後在 Chrome DevTools 中使用記憶體分析器。