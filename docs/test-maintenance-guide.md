# 測試維護指南

本文件提供了測試套件的維護指南，包括測試最佳實踐、故障排除和長期維護策略。

## 目錄

1. [測試架構概述](#測試架構概述)
2. [測試環境設置](#測試環境設置)
3. [測試類型和組織](#測試類型和組織)
4. [測試工具和輔助函數](#測試工具和輔助函數)
5. [最佳實踐](#最佳實踐)
6. [故障排除指南](#故障排除指南)
7. [CI/CD 整合](#cicd-整合)
8. [效能優化](#效能優化)
9. [長期維護策略](#長期維護策略)

## 測試架構概述

本專案的測試架構基於 Vitest 測試框架，並根據不同的測試類型分為多個環境：

- **前端測試**：使用 jsdom 環境，測試 Vue 組件和前端邏輯
- **後端測試**：使用 Node.js 環境，測試 Express 伺服器和 API
- **整合測試**：使用 Node.js 環境，測試前後端整合和系統功能
- **CI 測試**：針對 CI 環境優化的測試配置

測試架構使用工作區（workspace）配置來管理不同類型的測試，每種測試類型都有專用的配置檔案和設置檔案。

## 測試環境設置

### 環境配置檔案

- `vitest.config.ts`：主要配置檔案
- `vitest.workspace.ts`：工作區配置檔案
- `tests/config/vitest.config.frontend.ts`：前端測試配置
- `tests/config/vitest.config.server.ts`：後端測試配置
- `tests/config/vitest.config.integration.ts`：整合測試配置
- `tests/config/vitest.config.ci.ts`：CI 環境配置
- `tests/config/vitest.config.parallel.ts`：並行測試配置

### 環境設置檔案

- `tests/setup.ts`：主要設置檔案
- `tests/setup/frontend-setup.ts`：前端測試設置
- `tests/setup/server-setup.ts`：後端測試設置
- `tests/setup/integration-setup.ts`：整合測試設置
- `tests/setup/ci-setup.ts`：CI 環境設置
- `tests/setup/parallel-setup.ts`：並行測試設置
- `tests/setup/database-setup.ts`：資料庫測試設置

## 測試類型和組織

### 前端測試

前端測試位於 `src/**/*.test.ts` 和 `tests/unit/**/*.test.ts`，主要測試 Vue 組件、Pinia store 和前端工具函數。

#### 執行前端測試

```bash
npm run test:frontend
```

### 後端測試

後端測試位於 `tests/server/**/*.test.ts` 和 `server/**/*.test.ts`，主要測試 Express 路由、中介軟體和資料庫操作。

#### 執行後端測試

```bash
npm run test:backend
```

### 整合測試

整合測試位於 `tests/integration/**/*.test.ts`，測試前後端整合、API 互動和 WebSocket 通訊。

#### 執行整合測試

```bash
npm run test:integration
```

### CI 測試

CI 測試使用專門的配置，針對 CI 環境優化，支援並行執行和標準報告格式。

#### 執行 CI 測試

```bash
npm run test:ci
```

## 測試工具和輔助函數

### 資料庫測試工具

- `DatabaseTestManager`：管理測試資料庫的初始化和清理
- `TestDataFactory`：生成測試資料
- `DatabaseConstraintValidator`：驗證資料庫約束

### API 測試工具

- `APITestClient`：測試 API 端點的客戶端
- `TestServerManager`：管理測試伺服器的啟動和關閉
- `ApiTestUtils`：API 測試輔助函數

### WebSocket 測試工具

- `MockWebSocket`：模擬 WebSocket 連接
- `WebSocketTestUtils`：WebSocket 測試輔助函數

### 通用測試工具

- `TestReporter`：生成測試報告
- `PerformanceMonitor`：監控測試效能
- `TestHelpers`：通用測試輔助函數
- `MockFactories`：創建各種 Mock 物件

## 最佳實踐

### 測試組織

1. **使用描述性的測試名稱**：測試名稱應該清楚描述被測試的功能和預期行為
2. **遵循 AAA 模式**：Arrange（準備）、Act（執行）、Assert（斷言）
3. **適當分組測試**：使用 `describe` 區塊將相關測試分組
4. **避免測試間的依賴**：每個測試應該獨立運行，不依賴其他測試的結果

### 測試資料

1. **使用工廠函數**：使用 `TestDataFactory` 生成測試資料
2. **避免硬編碼**：避免在測試中硬編碼資料，使用變數和常數
3. **清理測試資料**：在測試後清理所有創建的資料
4. **使用隨機資料**：使用 `@faker-js/faker` 生成隨機測試資料

### 測試隔離

1. **使用 Mock**：適當使用 Mock 隔離被測試的單元
2. **避免外部依賴**：避免測試依賴外部服務或 API
3. **使用測試資料庫**：使用專門的測試資料庫，而不是生產資料庫
4. **重置狀態**：在每個測試後重置全域狀態

## 故障排除指南

### 常見問題

#### "document is not defined" 錯誤

**問題**：在 Node.js 環境中執行前端測試時出現 "document is not defined" 錯誤。

**解決方案**：
1. 確保測試使用正確的環境（jsdom）
2. 檢查 `vitest.config.ts` 中的 `environmentMatchGlobs` 配置
3. 確保前端測試檔案符合 `src/**/*.test.ts` 或 `tests/unit/**/*.test.ts` 模式

#### 資料庫連接錯誤

**問題**：測試無法連接到資料庫或出現資料庫錯誤。

**解決方案**：
1. 確保 `.env.test` 中的 `DATABASE_URL` 設置正確
2. 檢查資料庫是否已初始化（`npx prisma db push`）
3. 使用 `DatabaseTestManager` 管理測試資料庫連接

#### 測試超時

**問題**：測試執行時間過長或超時。

**解決方案**：
1. 增加測試超時設置（`testTimeout` 和 `hookTimeout`）
2. 檢查是否有無限循環或未解決的 Promise
3. 使用 `PerformanceMonitor` 識別效能瓶頸

#### WebSocket 測試失敗

**問題**：WebSocket 測試連接失敗或訊息未收到。

**解決方案**：
1. 使用 `MockWebSocket` 替代真實的 WebSocket 連接
2. 確保測試等待連接建立和訊息處理
3. 檢查 WebSocket 伺服器是否正確啟動和關閉

### 調試技巧

1. **使用 `console.log`**：在測試中添加 `console.log` 語句輸出調試信息
2. **使用 `--inspect`**：使用 Node.js 調試器執行測試
3. **使用 `test.only`**：使用 `it.only` 或 `describe.only` 只執行特定測試
4. **檢查測試環境**：使用 `console.log(process.env)` 檢查環境變數

## CI/CD 整合

### GitHub Actions

本專案使用 GitHub Actions 進行 CI/CD 整合，配置檔案位於 `.github/workflows/ci.yml`。

#### CI 工作流程

1. **安裝依賴**：安裝 Node.js 和專案依賴
2. **執行 Lint**：檢查程式碼風格和格式
3. **執行測試**：使用 CI 配置執行測試
4. **生成報告**：生成測試覆蓋率和測試報告
5. **構建應用**：構建前端和後端應用

### 並行測試執行

CI 環境中使用並行測試執行來加速測試過程：

```bash
npm run test:ci:parallel
```

### 測試報告

CI 環境中生成標準格式的測試報告：

```bash
npm run test:ci:report
```

## 效能優化

### 測試執行時間

使用 `PerformanceMonitor` 監控測試執行時間：

```typescript
import { PerformanceMonitor } from '../utils/performance-monitor'

const monitor = new PerformanceMonitor()
monitor.start('test-name')
// 執行測試
const result = monitor.stop('test-name')
console.log(`測試執行時間: ${result.duration}ms`)
```

### 並行測試

使用並行測試執行加速測試過程：

```bash
npm run test:parallel
```

### 測試過濾

只執行特定的測試以節省時間：

```bash
npm run test -- --filter="測試名稱"
```

## 長期維護策略

### 測試覆蓋率

維持高測試覆蓋率是確保程式碼品質的關鍵：

1. **定期檢查覆蓋率**：使用 `npm run test:coverage` 檢查測試覆蓋率
2. **設置覆蓋率閾值**：在 CI 中設置覆蓋率閾值，確保覆蓋率不會下降
3. **優先測試關鍵路徑**：優先確保核心功能和關鍵路徑有高覆蓋率

### 測試維護

定期維護測試套件以確保其有效性：

1. **定期更新測試**：隨著程式碼變化更新測試
2. **移除過時測試**：移除不再相關的測試
3. **重構重複測試**：使用輔助函數和工廠減少重複程式碼
4. **更新測試依賴**：定期更新測試框架和工具

### 測試文件

保持測試文件的更新：

1. **添加測試註釋**：為複雜測試添加註釋說明測試目的和邏輯
2. **更新本指南**：隨著測試架構變化更新本指南
3. **創建測試示例**：為新開發人員創建測試示例
4. **記錄測試模式**：記錄常用的測試模式和最佳實踐