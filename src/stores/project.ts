// /src/stores/project.ts
import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { api } from '../lib/api';

// 定義專案的 TypeScript 介面
export interface Project {
  id: string; // 專案的唯一標識符
  name: string; // 專案名稱
  description?: string; // 專案描述
  createdAt: string; // 建立時間
  updatedAt: string; // 更新時間
  files?: ProjectFile[]; // 專案檔案列表
}

// 定義專案檔案的 TypeScript 介面
export interface ProjectFile {
  id: string; // 檔案的唯一標識符
  name: string; // 檔案名稱
  content: string; // 檔案內容
  projectId: string; // 所屬專案 ID
  createdAt: string; // 建立時間
  updatedAt: string; // 更新時間
}

/**
 * 專案管理 Store
 *
 * @returns - Pinia Store
 */
export const useProjectStore = defineStore('project', () => {
  // 當前專案
  const currentProject = ref<Project | null>(null);

  // 專案列表
  const projects = ref<Project[]>([]);

  // 載入狀態
  const isLoading = ref(false);

  // 錯誤狀態
  const error = ref<string | null>(null);

  // 計算屬性：當前專案的檔案列表
  const currentProjectFiles = computed(() => {
    return currentProject.value?.files || [];
  });

  // 計算屬性：專案總數
  const projectCount = computed(() => {
    return projects.value.length;
  });

  /**
   * 載入所有專案
   */
  const loadProjects = async () => {
    isLoading.value = true;
    error.value = null;

    try {
      console.log('📂 載入專案列表...');
      const response = await api.get<Project[]>('/api/projects');
      projects.value = response;
      console.log(`✅ 成功載入 ${response.length} 個專案`);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '載入專案失敗';
      error.value = errorMessage;
      console.error('❌ 載入專案失敗:', err);
    } finally {
      isLoading.value = false;
    }
  };

  /**
   * 建立新專案
   *
   * @param projectData - 專案資料
   */
  const createProject = async (projectData: { name: string; description?: string }) => {
    isLoading.value = true;
    error.value = null;

    try {
      console.log('🆕 建立新專案:', projectData.name);
      const newProject = await api.post<Project>('/api/projects', projectData);
      projects.value.push(newProject);
      console.log('✅ 專案建立成功:', newProject.id);
      return newProject;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '建立專案失敗';
      error.value = errorMessage;
      console.error('❌ 建立專案失敗:', err);
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  /**
   * 設定當前專案
   *
   * @param project - 要設定為當前專案的專案物件
   */
  const setCurrentProject = async (project: Project | null) => {
    if (!project) {
      currentProject.value = null;
      console.log('🔄 清除當前專案');
      return;
    }

    isLoading.value = true;
    error.value = null;

    try {
      console.log('🔄 切換到專案:', project.name);
      // 載入專案詳細資訊，包括檔案列表
      const projectWithFiles = await api.get<Project>(`/api/projects/${project.id}`);
      currentProject.value = projectWithFiles;
      console.log('✅ 專案切換成功');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '載入專案詳情失敗';
      error.value = errorMessage;
      console.error('❌ 載入專案詳情失敗:', err);
      // 如果載入詳情失敗，至少設定基本資訊
      currentProject.value = project;
    } finally {
      isLoading.value = false;
    }
  };

  /**
   * 選擇專案（setCurrentProject 的別名，用於向後相容）
   *
   * @param project - 要選擇的專案
   */
  const selectProject = async (project: Project) => {
    await setCurrentProject(project);
  };

  /**
   * 更新專案資訊
   *
   * @param projectId - 專案 ID
   * @param updateData - 要更新的資料
   */
  const updateProject = async (projectId: string, updateData: { name?: string; description?: string }) => {
    isLoading.value = true;
    error.value = null;

    try {
      console.log('📝 更新專案:', projectId);
      const updatedProject = await api.put<Project>(`/api/projects/${projectId}`, updateData);
      
      // 更新專案列表中的專案
      const index = projects.value.findIndex(p => p.id === projectId);
      if (index !== -1) {
        projects.value[index] = updatedProject;
      }

      // 如果是當前專案，也要更新
      if (currentProject.value?.id === projectId) {
        currentProject.value = updatedProject;
      }

      console.log('✅ 專案更新成功');
      return updatedProject;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '更新專案失敗';
      error.value = errorMessage;
      console.error('❌ 更新專案失敗:', err);
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  /**
   * 刪除專案
   *
   * @param projectId - 專案 ID
   */
  const deleteProject = async (projectId: string) => {
    isLoading.value = true;
    error.value = null;

    try {
      console.log('🗑️ 刪除專案:', projectId);
      await api.delete(`/api/projects/${projectId}`);
      
      // 從專案列表中移除
      projects.value = projects.value.filter(p => p.id !== projectId);

      // 如果刪除的是當前專案，清除當前專案
      if (currentProject.value?.id === projectId) {
        currentProject.value = null;
      }

      console.log('✅ 專案刪除成功');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '刪除專案失敗';
      error.value = errorMessage;
      console.error('❌ 刪除專案失敗:', err);
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  /**
   * 清除錯誤狀態
   */
  const clearError = () => {
    error.value = null;
  };

  /**
   * 重新載入當前專案
   */
  const refreshCurrentProject = async () => {
    if (currentProject.value) {
      await setCurrentProject(currentProject.value);
    }
  };

  return {
    // 狀態
    currentProject,
    projects,
    isLoading,
    error,
    
    // 計算屬性
    currentProjectFiles,
    projectCount,
    
    // 方法
    loadProjects,
    createProject,
    setCurrentProject,
    selectProject,
    updateProject,
    deleteProject,
    clearError,
    refreshCurrentProject,
  };
});
