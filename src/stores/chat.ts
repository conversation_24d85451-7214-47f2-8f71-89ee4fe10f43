// /src/stores/chat.ts
import { defineStore } from 'pinia';
import { ref } from 'vue';

// 定義訊息的 TypeScript 介面
export interface ChatMessage {
  id: string;
  type: 'user' | 'ai' | 'system';
  content: string;
  timestamp: Date;
  metadata?: {
    tokens?: number;
    model?: string;
  };
}

// WebSocket 連線狀態
export type ConnectionStatus = 'connected' | 'disconnected' | 'connecting' | 'error';

// WebSocket 訊息類型
export interface WebSocketMessage {
  type: 'chat' | 'ping' | 'pong' | 'welcome' | 'error' | 'chat_response' | 'chat_stream';
  message?: string;
  content?: string;
  timestamp?: number;
  messageId?: string;
  isComplete?: boolean;
}

/**
 * 聊天狀態管理 Store
 */
export const useChatStore = defineStore('chat', () => {
  // 聊天訊息列表
  const messages = ref<ChatMessage[]>([]);

  // 表示 AI 是否正在處理訊息
  const isLoading = ref(false);

  // WebSocket 連線實例
  const socket = ref<WebSocket | null>(null);

  // 連線狀態
  const connectionStatus = ref<ConnectionStatus>('disconnected');

  // 連線錯誤訊息
  const connectionError = ref<string>('');

  // 重連嘗試次數
  const reconnectAttempts = ref(0);
  const maxReconnectAttempts = 5;

  // 當前正在接收的訊息 ID（用於串流）
  const currentStreamingMessageId = ref<string>('');

  /**
   * 連接 WebSocket
   */
  const connectWebSocket = () => {
    if (socket.value?.readyState === WebSocket.OPEN) {
      console.log('🔌 WebSocket 已經連接');
      return;
    }

    connectionStatus.value = 'connecting';
    connectionError.value = '';

    try {
      // 在開發環境中，WebSocket 伺服器運行在 port 3001
      const isDevelopment = import.meta.env.DEV;
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      const wsUrl = isDevelopment
        ? `${protocol}//localhost:3001/ws`
        : `${protocol}//${window.location.host}/ws`;

      console.log('🔌 嘗試連接 WebSocket:', wsUrl);
      socket.value = new WebSocket(wsUrl);

      socket.value.onopen = () => {
        console.log('🔌 WebSocket 連接成功');
        connectionStatus.value = 'connected';
        reconnectAttempts.value = 0;
        connectionError.value = '';
      };

      socket.value.onmessage = (event) => {
        try {
          const data: WebSocketMessage = JSON.parse(event.data);
          handleWebSocketMessage(data);
        } catch (error) {
          console.error('❌ WebSocket 訊息解析錯誤:', error);
        }
      };

      socket.value.onclose = (event) => {
        console.log('🔌 WebSocket 連接關閉:', event.code, event.reason);
        connectionStatus.value = 'disconnected';

        // 如果不是正常關閉，嘗試重連
        if (event.code !== 1000 && reconnectAttempts.value < maxReconnectAttempts) {
          setTimeout(() => {
            reconnectAttempts.value++;
            console.log(`🔄 嘗試重連 WebSocket (${reconnectAttempts.value}/${maxReconnectAttempts})`);
            connectWebSocket();
          }, 2000 * reconnectAttempts.value);
        }
      };

      socket.value.onerror = (error) => {
        console.error('❌ WebSocket 錯誤:', error);
        connectionStatus.value = 'error';
        connectionError.value = 'WebSocket 連接錯誤';
      };

    } catch (error) {
      console.error('❌ WebSocket 連接失敗:', error);
      connectionStatus.value = 'error';
      connectionError.value = error instanceof Error ? error.message : '連接失敗';
    }
  };

  /**
   * 斷開 WebSocket 連接
   */
  const disconnectWebSocket = () => {
    if (socket.value) {
      socket.value.close(1000, '使用者主動斷開連接');
      socket.value = null;
    }
    connectionStatus.value = 'disconnected';
    reconnectAttempts.value = 0;
  };

  /**
   * 處理 WebSocket 訊息
   */
  const handleWebSocketMessage = (data: WebSocketMessage) => {
    switch (data.type) {
      case 'welcome':
        console.log('🎉 收到歡迎訊息:', data.message);
        break;

      case 'pong':
        console.log('🏓 收到 pong 回應');
        break;

      case 'chat_response':
        // 處理完整的聊天回應
        if (data.messageId && data.content) {
          let message = messages.value.find(m => m.id === data.messageId);
          if (!message) {
            // 創建新的 AI 訊息
            message = {
              id: data.messageId,
              type: 'ai',
              content: data.content,
              timestamp: new Date(),
            };
            messages.value.push(message);
          } else {
            message.content = data.content;
          }
        }
        isLoading.value = false;
        break;

      case 'chat_stream':
        // 處理串流聊天回應
        if (data.messageId && data.content !== undefined) {
          let message = messages.value.find(m => m.id === data.messageId);
          if (!message) {
            // 創建新的 AI 訊息
            message = {
              id: data.messageId,
              type: 'ai',
              content: '',
              timestamp: new Date(),
            };
            messages.value.push(message);
          }

          message.content += data.content;
          currentStreamingMessageId.value = data.messageId;

          if (data.isComplete) {
            isLoading.value = false;
            currentStreamingMessageId.value = '';
          }
        }
        break;

      case 'error':
        console.error('❌ 收到錯誤訊息:', data.message);
        connectionError.value = data.message || '未知錯誤';
        isLoading.value = false;
        break;

      default:
        console.warn('⚠️ 未知的 WebSocket 訊息類型:', data.type);
    }
  };

  /**
   * 發送訊息
   */
  const sendMessage = async (content: string) => {
    if (!content.trim()) {
      console.warn('⚠️ 訊息內容不能為空');
      return;
    }

    if (connectionStatus.value !== 'connected') {
      console.warn('⚠️ WebSocket 未連接');
      connectWebSocket();
      return;
    }

    // 建立使用者訊息
    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: content.trim(),
      timestamp: new Date(),
    };
    messages.value.push(userMessage);

    // 設定為載入中狀態
    isLoading.value = true;

    try {
      // 透過 WebSocket 發送聊天訊息
      const messageData: WebSocketMessage = {
        type: 'chat',
        message: content.trim(),
        timestamp: Date.now(),
      };

      socket.value?.send(JSON.stringify(messageData));
      console.log('📤 訊息已透過 WebSocket 發送');

    } catch (error) {
      console.error('❌ 發送訊息失敗:', error);

      // 建立錯誤訊息
      const errorMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        type: 'ai',
        content: `抱歉，發生錯誤：${error instanceof Error ? error.message : '未知錯誤'}`,
        timestamp: new Date(),
      };
      messages.value.push(errorMessage);
      isLoading.value = false;
    }
  };

  /**
   * 重試連接
   */
  const retryConnection = () => {
    reconnectAttempts.value = 0;
    connectWebSocket();
  };

  /**
   * 清空聊天記錄
   */
  const clearMessages = () => {
    messages.value = [];
    console.log('🧹 聊天記錄已清空');
  };

  /**
   * 刪除指定訊息
   */
  const deleteMessage = (messageId: string) => {
    const index = messages.value.findIndex(msg => msg.id === messageId);
    if (index !== -1) {
      messages.value.splice(index, 1);
      console.log(`🗑️ 已刪除訊息: ${messageId}`);
    }
  };

  /**
   * 發送 ping 訊息測試連接
   */
  const ping = () => {
    if (socket.value?.readyState === WebSocket.OPEN) {
      socket.value.send(JSON.stringify({ type: 'ping', timestamp: Date.now() }));
    }
  };

  return {
    // 狀態
    messages,
    isLoading,
    socket,
    connectionStatus,
    connectionError,
    reconnectAttempts,
    currentStreamingMessageId,

    // 方法
    sendMessage,
    clearMessages,
    deleteMessage,
    connectWebSocket,
    disconnectWebSocket,
    retryConnection,
    ping,
  };
});
