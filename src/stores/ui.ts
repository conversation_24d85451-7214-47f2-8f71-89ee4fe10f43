// /src/stores/ui.ts
import { defineStore } from 'pinia';
import { ref, computed } from 'vue';

// 通知訊息類型
export interface NotificationMessage {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message?: string;
  duration?: number; // 自動關閉時間（毫秒），0 表示不自動關閉
  actions?: NotificationAction[];
}

// 通知動作
export interface NotificationAction {
  label: string;
  action: () => void;
  style?: 'primary' | 'secondary' | 'danger';
}

// 確認對話框配置
export interface ConfirmDialogConfig {
  id: string;
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  type?: 'info' | 'warning' | 'danger';
  onConfirm: () => void | Promise<void>;
  onCancel?: () => void;
}

// 載入狀態項目
export interface LoadingState {
  id: string;
  message: string;
  progress?: number; // 0-100 的進度百分比
  canCancel?: boolean;
  onCancel?: () => void;
}

/**
 * UI/UX 狀態管理 Store
 * 管理全域載入狀態、通知訊息、確認對話框等 UX 元素
 */
export const useUIStore = defineStore('ui', () => {
  // 通知訊息列表
  const notifications = ref<NotificationMessage[]>([]);

  // 確認對話框列表
  const confirmDialogs = ref<ConfirmDialogConfig[]>([]);

  // 載入狀態列表
  const loadingStates = ref<LoadingState[]>([]);

  // 計算屬性：是否有任何載入中的狀態
  const isLoading = computed(() => loadingStates.value.length > 0);

  // 計算屬性：主要載入狀態（最新的一個）
  const primaryLoadingState = computed(() => 
    loadingStates.value[loadingStates.value.length - 1]
  );

  /**
   * 顯示通知訊息
   */
  const showNotification = (config: Omit<NotificationMessage, 'id'>) => {
    const notification: NotificationMessage = {
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      duration: config.type === 'error' ? 0 : 5000, // 錯誤訊息不自動關閉
      ...config,
    };

    notifications.value.push(notification);

    // 自動關閉通知
    if (notification.duration && notification.duration > 0) {
      setTimeout(() => {
        dismissNotification(notification.id);
      }, notification.duration);
    }

    return notification.id;
  };

  /**
   * 顯示成功訊息
   */
  const showSuccess = (title: string, message?: string, duration = 3000) => {
    return showNotification({
      type: 'success',
      title,
      message,
      duration,
    });
  };

  /**
   * 顯示錯誤訊息
   */
  const showError = (title: string, message?: string, actions?: NotificationAction[]) => {
    return showNotification({
      type: 'error',
      title,
      message,
      duration: 0, // 錯誤訊息不自動關閉
      actions,
    });
  };

  /**
   * 顯示警告訊息
   */
  const showWarning = (title: string, message?: string, duration = 4000) => {
    return showNotification({
      type: 'warning',
      title,
      message,
      duration,
    });
  };

  /**
   * 顯示資訊訊息
   */
  const showInfo = (title: string, message?: string, duration = 4000) => {
    return showNotification({
      type: 'info',
      title,
      message,
      duration,
    });
  };

  /**
   * 關閉通知訊息
   */
  const dismissNotification = (id: string) => {
    const index = notifications.value.findIndex(n => n.id === id);
    if (index !== -1) {
      notifications.value.splice(index, 1);
    }
  };

  /**
   * 清空所有通知
   */
  const clearNotifications = () => {
    notifications.value = [];
  };

  /**
   * 顯示確認對話框
   */
  const showConfirmDialog = (config: Omit<ConfirmDialogConfig, 'id'>) => {
    const dialog: ConfirmDialogConfig = {
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      confirmText: '確認',
      cancelText: '取消',
      type: 'info',
      ...config,
    };

    confirmDialogs.value.push(dialog);
    return dialog.id;
  };

  /**
   * 關閉確認對話框
   */
  const dismissConfirmDialog = (id: string) => {
    const index = confirmDialogs.value.findIndex(d => d.id === id);
    if (index !== -1) {
      confirmDialogs.value.splice(index, 1);
    }
  };

  /**
   * 開始載入狀態
   */
  const startLoading = (message: string, options?: {
    progress?: number;
    canCancel?: boolean;
    onCancel?: () => void;
  }) => {
    const loadingState: LoadingState = {
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      message,
      progress: options?.progress,
      canCancel: options?.canCancel,
      onCancel: options?.onCancel,
    };

    loadingStates.value.push(loadingState);
    return loadingState.id;
  };

  /**
   * 更新載入狀態
   */
  const updateLoading = (id: string, updates: Partial<Omit<LoadingState, 'id'>>) => {
    const loadingState = loadingStates.value.find(l => l.id === id);
    if (loadingState) {
      Object.assign(loadingState, updates);
    }
  };

  /**
   * 結束載入狀態
   */
  const stopLoading = (id: string) => {
    const index = loadingStates.value.findIndex(l => l.id === id);
    if (index !== -1) {
      loadingStates.value.splice(index, 1);
    }
  };

  /**
   * 清空所有載入狀態
   */
  const clearLoading = () => {
    loadingStates.value = [];
  };

  /**
   * 處理 API 錯誤
   */
  const handleApiError = (error: any, context?: string) => {
    console.error('API 錯誤:', error);

    let title = '操作失敗';
    let message = '請稍後再試';

    if (context) {
      title = `${context}失敗`;
    }

    if (error?.response?.data?.message) {
      message = error.response.data.message;
    } else if (error?.message) {
      message = error.message;
    } else if (typeof error === 'string') {
      message = error;
    }

    const actions: NotificationAction[] = [
      {
        label: '重試',
        action: () => {
          // 這裡可以實作重試邏輯
          console.log('重試操作');
        },
        style: 'primary',
      },
    ];

    showError(title, message, actions);
  };

  /**
   * 處理網路錯誤
   */
  const handleNetworkError = (error: any) => {
    console.error('網路錯誤:', error);

    showError(
      '網路連線錯誤',
      '請檢查您的網路連線並重試',
      [
        {
          label: '重新連線',
          action: () => {
            window.location.reload();
          },
          style: 'primary',
        },
      ]
    );
  };

  /**
   * 顯示使用者訊息（根據類型自動選擇方法）
   */
  const showUserMessage = (message: string, type: 'success' | 'error' | 'warning' | 'info' = 'info') => {
    switch (type) {
      case 'success':
        return showSuccess(message);
      case 'error':
        return showError(message);
      case 'warning':
        return showWarning(message);
      case 'info':
      default:
        return showInfo(message);
    }
  };

  return {
    // 狀態
    notifications,
    confirmDialogs,
    loadingStates,
    isLoading,
    primaryLoadingState,

    // 通知方法
    showNotification,
    showSuccess,
    showError,
    showWarning,
    showInfo,
    dismissNotification,
    clearNotifications,

    // 確認對話框方法
    showConfirmDialog,
    dismissConfirmDialog,

    // 載入狀態方法
    startLoading,
    updateLoading,
    stopLoading,
    clearLoading,

    // 錯誤處理方法
    handleApiError,
    handleNetworkError,
    showUserMessage,
  };
});