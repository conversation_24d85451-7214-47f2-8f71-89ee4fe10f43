
// src/stores/file-system.ts

import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { VirtualFileSystem } from '../lib/file-system';
import type { VirtualFile, VirtualDirectory } from '@shared/types/file-system';

export const useFileSystemStore = defineStore('file-system', () => {
  // State
  const vfs = new VirtualFileSystem();
  const currentFile = ref<string>('');
  const unsavedChanges = ref<Set<string>>(new Set());
  const isLoading = ref(false);

  // Getters
  const files = computed(() => {
    const fileMap = new Map<string, VirtualFile>();
    const allFiles = vfs.getStats();
    
    // Get all files from VFS
    try {
      const rootContents = vfs.listDirectory('/');
      const collectFiles = (items: (VirtualFile | VirtualDirectory)[], basePath = '') => {
        items.forEach(item => {
          if (!item.isDirectory) {
            fileMap.set(item.id, item as VirtualFile);
          } else {
            const dir = item as VirtualDirectory;
            if (dir.children) {
              collectFiles(dir.children, item.path);
            }
          }
        });
      };
      collectFiles(rootContents);
    } catch (error) {
      console.warn('Failed to get files from VFS:', error);
    }
    
    return fileMap;
  });

  // Actions
  const createFile = (path: string, content: string = '') => {
    try {
      vfs.createFile(path, content);
    } catch (error) {
      console.error('Failed to create file:', error);
      throw error;
    }
  };

  const createDirectory = (path: string) => {
    try {
      vfs.createDirectory(path);
    } catch (error) {
      console.error('Failed to create directory:', error);
      throw error;
    }
  };

  const readFile = (path: string) => {
    try {
      return vfs.readFile(path);
    } catch (error) {
      console.error('Failed to read file:', error);
      throw error;
    }
  };

  const updateFile = (path: string, content: string) => {
    try {
      vfs.updateFile(path, content);
      unsavedChanges.value.add(path);
    } catch (error) {
      console.error('Failed to update file:', error);
      throw error;
    }
  };

  const deleteFile = (path: string) => {
    try {
      vfs.deleteFile(path);
      unsavedChanges.value.delete(path);
      if (currentFile.value === path) {
        currentFile.value = '';
      }
    } catch (error) {
      console.error('Failed to delete file:', error);
      throw error;
    }
  };

  const deleteDirectory = (path: string, recursive: boolean = false) => {
    try {
      vfs.deleteDirectory(path, recursive);
    } catch (error) {
      console.error('Failed to delete directory:', error);
      throw error;
    }
  };

  const moveFile = (sourcePath: string, targetPath: string) => {
    try {
      vfs.move(sourcePath, targetPath);
      if (unsavedChanges.value.has(sourcePath)) {
        unsavedChanges.value.delete(sourcePath);
        unsavedChanges.value.add(targetPath);
      }
      if (currentFile.value === sourcePath) {
        currentFile.value = targetPath;
      }
    } catch (error) {
      console.error('Failed to move file:', error);
      throw error;
    }
  };

  const saveFile = async (path: string) => {
    try {
      // TODO: Implement actual file saving to backend
      unsavedChanges.value.delete(path);
    } catch (error) {
      console.error('Failed to save file:', error);
      throw error;
    }
  };

  const loadProject = async (projectId: string) => {
    isLoading.value = true;
    try {
      // TODO: Implement project loading from backend
      console.log('Loading project:', projectId);
    } catch (error) {
      console.error('Failed to load project:', error);
      throw error;
    } finally {
      isLoading.value = false;
    }
  };

  const setCurrentFile = (path: string) => {
    currentFile.value = path;
  };

  const markFileAsModified = (path: string) => {
    unsavedChanges.value.add(path);
  };

  const markFileAsSaved = (path: string) => {
    unsavedChanges.value.delete(path);
  };

  return {
    // State
    vfs,
    currentFile,
    unsavedChanges,
    isLoading,
    
    // Getters
    files,
    
    // Actions
    createFile,
    createDirectory,
    readFile,
    updateFile,
    deleteFile,
    deleteDirectory,
    moveFile,
    saveFile,
    loadProject,
    setCurrentFile,
    markFileAsModified,
    markFileAsSaved,
  };
});
