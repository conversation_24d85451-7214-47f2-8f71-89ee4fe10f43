<template>
  <div class="file-tree">
    <!-- 檔案樹標題欄 -->
    <div class="file-tree-header">
      <h3 class="tree-title">檔案瀏覽器</h3>
      <div class="tree-actions">
        <button 
          @click="refreshTree" 
          class="action-btn refresh-btn" 
          :disabled="isLoading" 
          title="重新整理"
        >
          <span class="icon">🔄</span>
        </button>
        <button 
          @click="showCreateFileDialog" 
          class="action-btn create-file-btn" 
          title="新增檔案"
        >
          <span class="icon">📄</span>
        </button>
        <button 
          @click="showCreateFolderDialog" 
          class="action-btn create-folder-btn" 
          title="新增資料夾"
        >
          <span class="icon">📁</span>
        </button>
      </div>
    </div>

    <!-- 檔案樹內容 -->
    <div class="file-tree-content">
      <!-- 載入狀態 -->
      <div v-if="isLoading" class="loading-state">
        <div class="loading-spinner"></div>
        <span>載入中...</span>
      </div>

      <!-- 無專案狀態 -->
      <div v-else-if="!projectId" class="empty-state">
        <div class="empty-icon">📂</div>
        <p>請先選擇一個專案</p>
      </div>

      <!-- 空專案狀態 -->
      <div v-else-if="fileTreeData.length === 0" class="empty-state">
        <div class="empty-icon">📄</div>
        <p>專案中還沒有檔案</p>
        <button @click="showCreateFileDialog" class="create-first-file-btn">
          建立第一個檔案
        </button>
      </div>

      <!-- 檔案樹 -->
      <div v-else class="file-tree-nodes">
        <FileTreeNode
          v-for="node in fileTreeData"
          :key="node.id"
          :node="node"
          :level="0"
          :selected-file="selectedFile"
          :unsaved-changes="unsavedChanges"
          @file-selected="handleFileSelected"
          @file-created="handleFileCreated"
          @file-deleted="handleFileDeleted"
          @file-renamed="handleFileRenamed"
          @folder-created="handleFolderCreated"
        />
      </div>
    </div>

    <!-- 建立檔案對話框 -->
    <CreateFileDialog
      v-if="showCreateFile"
      :parent-path="createFileParentPath"
      @confirm="handleCreateFile"
      @cancel="hideCreateFileDialog"
    />

    <!-- 建立資料夾對話框 -->
    <CreateFolderDialog
      v-if="showCreateFolder"
      :parent-path="createFolderParentPath"
      @confirm="handleCreateFolder"
      @cancel="hideCreateFolderDialog"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { storeToRefs } from 'pinia'
import { useFileSystemStore } from '../../stores/file-system'
import FileTreeNode from './FileTreeNode.vue'
import CreateFileDialog from './CreateFileDialog.vue'
import CreateFolderDialog from './CreateFolderDialog.vue'
import type { FileNode } from '../../types/file-tree'

// Props
interface Props {
  projectId: string
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'file-selected': [filePath: string]
  'file-created': [parentPath: string, fileName: string]
  'file-deleted': [filePath: string]
  'file-renamed': [oldPath: string, newPath: string]
}>()

// Store
const fileSystemStore = useFileSystemStore()
const { files, currentFile, unsavedChanges } = storeToRefs(fileSystemStore)

// Local state
const isLoading = ref(false)
const selectedFile = ref<string>('')
const showCreateFile = ref(false)
const showCreateFolder = ref(false)
const createFileParentPath = ref('/')
const createFolderParentPath = ref('/')

// Computed
const fileTreeData = computed(() => {
  if (!files.value || files.value.size === 0) {
    return []
  }
  
  return buildFileTree(Array.from(files.value.values()))
})

/**
 * 建構檔案樹結構
 */
const buildFileTree = (fileList: any[]): FileNode[] => {
  const tree: FileNode[] = []
  const pathMap = new Map<string, FileNode>()

  // 先創建所有節點
  fileList.forEach(file => {
    const pathParts = file.path.split('/').filter(Boolean)
    let currentPath = ''

    pathParts.forEach((part, index) => {
      const parentPath = currentPath
      currentPath = currentPath ? `${currentPath}/${part}` : `/${part}`
      
      if (!pathMap.has(currentPath)) {
        const isFile = index === pathParts.length - 1
        const node: FileNode = {
          id: isFile ? file.id : currentPath,
          name: part,
          type: isFile ? 'file' : 'folder',
          path: currentPath,
          children: isFile ? undefined : [],
          isExpanded: false,
          isModified: isFile ? unsavedChanges.value.has(currentPath) : false,
          size: isFile ? file.size : undefined,
          lastModified: isFile ? file.lastModified : undefined,
        }

        pathMap.set(currentPath, node)

        // 添加到父節點或根節點
        if (parentPath) {
          const parent = pathMap.get(parentPath)
          if (parent && parent.children) {
            parent.children.push(node)
          }
        } else {
          tree.push(node)
        }
      }
    })
  })

  // 排序：資料夾在前，檔案在後，然後按名稱排序
  const sortNodes = (nodes: FileNode[]) => {
    nodes.sort((a, b) => {
      if (a.type !== b.type) {
        return a.type === 'folder' ? -1 : 1
      }
      return a.name.localeCompare(b.name)
    })

    nodes.forEach(node => {
      if (node.children) {
        sortNodes(node.children)
      }
    })
  }

  sortNodes(tree)
  return tree
}

/**
 * 重新整理檔案樹
 */
const refreshTree = async () => {
  if (!props.projectId) return
  
  isLoading.value = true
  try {
    await fileSystemStore.loadProject(props.projectId)
  } catch (error) {
    console.error('重新整理檔案樹失敗:', error)
  } finally {
    isLoading.value = false
  }
}

/**
 * 顯示建立檔案對話框
 */
const showCreateFileDialog = (parentPath: string = '/') => {
  createFileParentPath.value = parentPath
  showCreateFile.value = true
}

/**
 * 隱藏建立檔案對話框
 */
const hideCreateFileDialog = () => {
  showCreateFile.value = false
  createFileParentPath.value = '/'
}

/**
 * 顯示建立資料夾對話框
 */
const showCreateFolderDialog = (parentPath: string = '/') => {
  createFolderParentPath.value = parentPath
  showCreateFolder.value = true
}

/**
 * 隱藏建立資料夾對話框
 */
const hideCreateFolderDialog = () => {
  showCreateFolder.value = false
  createFolderParentPath.value = '/'
}

/**
 * 處理檔案選擇
 */
const handleFileSelected = (filePath: string) => {
  selectedFile.value = filePath
  emit('file-selected', filePath)
}

/**
 * 處理檔案建立
 */
const handleFileCreated = (parentPath: string, fileName: string) => {
  emit('file-created', parentPath, fileName)
  hideCreateFileDialog()
}

/**
 * 處理檔案刪除
 */
const handleFileDeleted = (filePath: string) => {
  try {
    // Check if it's a file or directory
    const fileData = files.value.get(filePath)
    if (fileData) {
      fileSystemStore.deleteFile(filePath)
    } else {
      // It might be a directory
      fileSystemStore.deleteDirectory(filePath, true)
    }
    emit('file-deleted', filePath)
  } catch (error) {
    console.error('刪除失敗:', error)
  }
}

/**
 * 處理檔案重新命名
 */
const handleFileRenamed = (oldPath: string, newPath: string) => {
  try {
    fileSystemStore.moveFile(oldPath, newPath)
    emit('file-renamed', oldPath, newPath)
  } catch (error) {
    console.error('重新命名失敗:', error)
  }
}

/**
 * 處理資料夾建立
 */
const handleFolderCreated = (parentPath: string, folderName: string) => {
  try {
    const folderPath = parentPath === '/' 
      ? `/${folderName}` 
      : `${parentPath}/${folderName}`
    
    fileSystemStore.createDirectory(folderPath)
  } catch (error) {
    console.error('建立資料夾失敗:', error)
  }
}

/**
 * 處理建立檔案
 */
const handleCreateFile = async (fileName: string, content: string = '') => {
  try {
    const filePath = createFileParentPath.value === '/' 
      ? `/${fileName}` 
      : `${createFileParentPath.value}/${fileName}`
    
    fileSystemStore.createFile(filePath, content)
    handleFileCreated(createFileParentPath.value, fileName)
  } catch (error) {
    console.error('建立檔案失敗:', error)
  }
}

/**
 * 處理建立資料夾
 */
const handleCreateFolder = async (folderName: string) => {
  try {
    const folderPath = createFolderParentPath.value === '/' 
      ? `/${folderName}` 
      : `${createFolderParentPath.value}/${folderName}`
    
    fileSystemStore.createDirectory(folderPath)
    hideCreateFolderDialog()
  } catch (error) {
    console.error('建立資料夾失敗:', error)
  }
}

// Watch for project changes
watch(() => props.projectId, (newProjectId) => {
  if (newProjectId) {
    refreshTree()
  }
}, { immediate: true })

// Watch for current file changes
watch(currentFile, (newCurrentFile) => {
  if (newCurrentFile) {
    selectedFile.value = newCurrentFile
  }
})

onMounted(() => {
  if (props.projectId) {
    refreshTree()
  }
})
</script>

<style scoped>
.file-tree {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e5e7eb;
}

/* 標題欄 */
.file-tree-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.tree-title {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.tree-actions {
  display: flex;
  gap: 4px;
}

.action-btn {
  width: 28px;
  height: 28px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.action-btn:hover:not(:disabled) {
  background: #f3f4f6;
  border-color: #9ca3af;
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.icon {
  font-size: 12px;
}

/* 內容區域 */
.file-tree-content {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

/* 載入狀態 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 32px 16px;
  color: #6b7280;
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 12px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 空狀態 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 32px 16px;
  text-align: center;
  color: #6b7280;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-state p {
  margin: 0 0 16px 0;
  font-size: 14px;
}

.create-first-file-btn {
  padding: 8px 16px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.create-first-file-btn:hover {
  background: #2563eb;
}

/* 檔案樹節點 */
.file-tree-nodes {
  padding: 4px 0;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .file-tree-header {
    padding: 8px 12px;
  }
  
  .tree-title {
    font-size: 13px;
  }
  
  .action-btn {
    width: 24px;
    height: 24px;
  }
  
  .icon {
    font-size: 10px;
  }
  
  .empty-state {
    padding: 24px 12px;
  }
  
  .empty-icon {
    font-size: 36px;
  }
}
</style>