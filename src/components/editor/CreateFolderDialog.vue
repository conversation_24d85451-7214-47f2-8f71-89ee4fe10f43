<template>
  <div class="modal-overlay" @click="handleCancel">
    <div class="modal-content" @click.stop>
      <h3 class="modal-title">建立新資料夾</h3>
      
      <form @submit.prevent="handleConfirm">
        <div class="form-group">
          <label for="folderName" class="form-label">資料夾名稱</label>
          <input
            id="folderName"
            v-model="folderName"
            type="text"
            class="form-input"
            :class="{ 'error': folderNameError }"
            required
            placeholder="例如: components"
            @input="validateFolderName"
            ref="folderNameInput"
          />
          <div v-if="folderNameError" class="error-message">{{ folderNameError }}</div>
        </div>
        
        <div class="form-group">
          <div class="path-info">
            <span class="path-label">建立位置:</span>
            <span class="path-value">{{ displayPath }}</span>
          </div>
        </div>
        
        <div class="form-actions">
          <button type="button" @click="handleCancel" class="btn btn-secondary">
            取消
          </button>
          <button 
            type="submit" 
            :disabled="!folderName.trim() || !!folderNameError || isCreating"
            class="btn btn-primary"
          >
            {{ isCreating ? '建立中...' : '建立' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue'
import type { CreateFolderDialogProps, CreateFolderDialogEmits } from '../../types/file-tree'

// Props
const props = defineProps<CreateFolderDialogProps>()

// Emits
const emit = defineEmits<CreateFolderDialogEmits>()

// Local state
const folderName = ref('')
const folderNameError = ref('')
const isCreating = ref(false)
const folderNameInput = ref<HTMLInputElement>()

// Computed
const displayPath = computed(() => {
  return props.parentPath === '/' ? '根目錄' : props.parentPath
})

/**
 * 驗證資料夾名稱
 */
const validateFolderName = () => {
  const name = folderName.value.trim()
  folderNameError.value = ''

  if (!name) {
    return
  }

  // 檢查資料夾名稱格式
  const invalidChars = /[<>:"/\\|?*]/
  if (invalidChars.test(name)) {
    folderNameError.value = '資料夾名稱包含無效字元'
    return
  }

  // 檢查資料夾名稱長度
  if (name.length > 255) {
    folderNameError.value = '資料夾名稱過長'
    return
  }

  // 檢查是否包含點開頭（隱藏資料夾）
  if (name.startsWith('.')) {
    folderNameError.value = '不建議建立隱藏資料夾'
    return
  }

  // 檢查是否為保留名稱
  const reservedNames = ['con', 'prn', 'aux', 'nul', 'com1', 'com2', 'com3', 'com4', 'com5', 'com6', 'com7', 'com8', 'com9', 'lpt1', 'lpt2', 'lpt3', 'lpt4', 'lpt5', 'lpt6', 'lpt7', 'lpt8', 'lpt9']
  if (reservedNames.includes(name.toLowerCase())) {
    folderNameError.value = '資料夾名稱為系統保留名稱'
    return
  }

  // TODO: 檢查是否已存在（需要從 store 獲取現有資料夾列表）
}

/**
 * 處理確認建立
 */
const handleConfirm = async () => {
  if (!folderName.value.trim() || folderNameError.value) {
    return
  }

  isCreating.value = true
  try {
    emit('confirm', folderName.value.trim())
  } catch (error) {
    console.error('建立資料夾失敗:', error)
  } finally {
    isCreating.value = false
  }
}

/**
 * 處理取消
 */
const handleCancel = () => {
  emit('cancel')
}

onMounted(async () => {
  // 自動聚焦到資料夾名稱輸入框
  await nextTick()
  folderNameInput.value?.focus()
})
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  padding: 24px;
  border-radius: 8px;
  width: 90%;
  max-width: 400px;
  max-height: 80vh;
  overflow-y: auto;
}

.modal-title {
  margin: 0 0 20px 0;
  font-size: 18px;
  font-weight: 600;
  color: #374151;
}

.form-group {
  margin-bottom: 16px;
}

.form-label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  color: #374151;
  font-size: 14px;
}

.form-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s;
}

.form-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input.error {
  border-color: #dc2626;
}

.error-message {
  color: #dc2626;
  font-size: 12px;
  margin-top: 4px;
}

.path-info {
  padding: 8px 12px;
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  font-size: 13px;
}

.path-label {
  color: #6b7280;
  margin-right: 8px;
}

.path-value {
  color: #374151;
  font-weight: 500;
}

.form-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 20px;
}

.btn {
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s;
  border: 1px solid transparent;
}

.btn-secondary {
  background: white;
  color: #6b7280;
  border-color: #d1d5db;
}

.btn-secondary:hover {
  background: #f9fafb;
  border-color: #9ca3af;
}

.btn-primary {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.btn-primary:hover:not(:disabled) {
  background: #2563eb;
  border-color: #2563eb;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .modal-content {
    padding: 20px;
    margin: 16px;
    max-height: calc(100vh - 32px);
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .btn {
    width: 100%;
  }
}
</style>