<template>
  <div class="code-editor-container" ref="containerRef">
    <div class="editor-toolbar" v-if="showToolbar">
      <div class="toolbar-left">
        <span class="file-path" :class="{ 'has-changes': hasUnsavedChanges }">
          {{ filePath }}
          <span v-if="hasUnsavedChanges" class="unsaved-indicator">●</span>
        </span>
        <span class="language-indicator">{{ language }}</span>
        <span v-if="autoSave" class="auto-save-indicator" title="Auto-save enabled">
          🔄
        </span>
      </div>
      <div class="toolbar-right">
        <button 
          @click="formatCode" 
          :disabled="isFormatting"
          class="format-btn"
          title="Format Code (Ctrl+Shift+F)"
        >
          <span v-if="isFormatting">Formatting...</span>
          <span v-else>Format</span>
        </button>
        <button 
          @click="saveFile" 
          :disabled="!hasUnsavedChanges"
          class="save-btn"
          title="Save File (Ctrl+S)"
        >
          Save
        </button>
      </div>
    </div>
    <div 
      ref="editorRef" 
      class="monaco-editor-wrapper"
      :class="{ 'with-toolbar': showToolbar }"
    ></div>
    <div v-if="validationErrors.length > 0" class="error-panel">
      <div class="error-header">
        <span>{{ validationErrors.length }} error(s)</span>
        <button @click="clearErrors" class="clear-errors-btn">Clear</button>
      </div>
      <div class="error-list">
        <div 
          v-for="error in validationErrors" 
          :key="`${error.line}-${error.column}-${error.message}`"
          class="error-item"
          @click="goToError(error)"
        >
          <span class="error-severity" :class="error.severity">{{ error.severity }}</span>
          <span class="error-location">Line {{ error.line }}, Col {{ error.column }}</span>
          <span class="error-message">{{ error.message }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick, computed } from 'vue'
import loader from '@monaco-editor/loader'
import type { editor, languages, IDisposable } from 'monaco-editor'
import { useFileSystemStore } from '../../stores/file-system'

// Props
interface Props {
  filePath: string
  language: 'vue' | 'typescript' | 'javascript' | 'css' | 'html' | 'json'
  readonly?: boolean
  showToolbar?: boolean
  initialContent?: string
  theme?: 'vs-dark' | 'vs-light' | 'hc-black'
  enableAutoSave?: boolean
  autoSaveDelay?: number
}

const props = withDefaults(defineProps<Props>(), {
  readonly: false,
  showToolbar: true,
  initialContent: '',
  theme: 'vs-dark',
  enableAutoSave: false,
  autoSaveDelay: 2000
})

// Emits
interface ValidationError {
  line: number
  column: number
  message: string
  severity: 'error' | 'warning' | 'info'
}

interface Emits {
  'content-changed': [content: string]
  'file-saved': [filePath: string, content: string]
  'validation-error': [errors: ValidationError[]]
  'cursor-position-changed': [line: number, column: number]
}

const emit = defineEmits<Emits>()

// Refs
const containerRef = ref<HTMLElement>()
const editorRef = ref<HTMLElement>()
const monacoEditor = ref<editor.IStandaloneCodeEditor>()
const monaco = ref<typeof import('monaco-editor')>()

// State
const validationErrors = ref<ValidationError[]>([])
const hasUnsavedChanges = ref(false)
const isFormatting = ref(false)
const disposables = ref<IDisposable[]>([])
const fileSystemStore = useFileSystemStore()

// Computed properties
const fileExists = computed(() => {
  try {
    fileSystemStore.readFile(props.filePath)
    return true
  } catch {
    return false
  }
})

const fileContent = computed(() => {
  try {
    return fileSystemStore.readFile(props.filePath)
  } catch {
    return props.initialContent
  }
})

// Monaco Editor initialization
const initializeMonaco = async () => {
  try {
    monaco.value = await loader.init()
    
    // Configure TypeScript compiler options for better IntelliSense
    monaco.value.languages.typescript.typescriptDefaults.setCompilerOptions({
      target: monaco.value.languages.typescript.ScriptTarget.ES2020,
      lib: ['ES2020', 'DOM', 'DOM.Iterable'],
      allowNonTsExtensions: true,
      moduleResolution: monaco.value.languages.typescript.ModuleResolutionKind.NodeJs,
      module: monaco.value.languages.typescript.ModuleKind.ESNext || 99,
      allowJs: true,
      checkJs: true,
      jsx: monaco.value.languages.typescript.JsxEmit.Preserve,
      strict: true,
      noImplicitAny: false,
      strictNullChecks: true,
      strictFunctionTypes: true,
      noImplicitReturns: true,
      noFallthroughCasesInSwitch: true,
      noUnusedLocals: false,
      noUnusedParameters: false,
      exactOptionalPropertyTypes: false,
      noImplicitOverride: true,
      noPropertyAccessFromIndexSignature: false,
      noImplicitThis: true,
      alwaysStrict: true,
      skipLibCheck: true,
      forceConsistentCasingInFileNames: true,
      moduleDetection: monaco.value.languages.typescript.ModuleDetectionKind.Force,
      resolveJsonModule: true,
      isolatedModules: true,
      allowSyntheticDefaultImports: true,
      esModuleInterop: true,
      experimentalDecorators: true,
      emitDecoratorMetadata: true,
      useDefineForClassFields: true
    })

    // Configure TypeScript diagnostics options for comprehensive error checking
    monaco.value.languages.typescript.typescriptDefaults.setDiagnosticsOptions({
      noSemanticValidation: false,
      noSyntaxValidation: false,
      noSuggestionDiagnostics: false,
      diagnosticCodesToIgnore: [
        1108, // A 'return' statement can only be used within a function body
        1375, // 'await' expressions are only allowed within async functions
        2307, // Cannot find module (for .vue files)
        2339, // Property does not exist on type (for dynamic properties)
      ]
    })

    // Configure JavaScript compiler options
    monaco.value.languages.typescript.javascriptDefaults.setCompilerOptions({
      target: monaco.value.languages.typescript.ScriptTarget.ES2020,
      lib: ['ES2020', 'DOM', 'DOM.Iterable'],
      allowNonTsExtensions: true,
      moduleResolution: monaco.value.languages.typescript.ModuleResolutionKind.NodeJs,
      module: monaco.value.languages.typescript.ModuleKind.ESNext || 99,
      allowJs: true,
      checkJs: true,
      jsx: monaco.value.languages.typescript.JsxEmit.Preserve,
      strict: false,
      noImplicitAny: false,
      skipLibCheck: true,
      allowSyntheticDefaultImports: true,
      esModuleInterop: true
    })

    // Configure JavaScript diagnostics options
    monaco.value.languages.typescript.javascriptDefaults.setDiagnosticsOptions({
      noSemanticValidation: false,
      noSyntaxValidation: false,
      noSuggestionDiagnostics: false
    })

    // Add Vue.js type definitions
    await addVueTypeDefinitions()
    
    // Register Vue language if not already registered
    if (!monaco.value.languages.getLanguages().find(lang => lang.id === 'vue')) {
      registerVueLanguage()
    }

    createEditor()
  } catch (error) {
    console.error('Failed to initialize Monaco Editor:', error)
  }
}

// Add Vue.js type definitions
const addVueTypeDefinitions = async () => {
  if (!monaco.value) return

  // Add comprehensive Vue 3 type definitions
  const vueTypes = `
declare module '*.vue' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

declare module 'vue' {
  export interface ComponentCustomProperties {}
  export interface ComponentCustomProps {}
  
  // Vue 3 Composition API
  export function ref<T>(value: T): Ref<T>
  export function reactive<T extends object>(target: T): T
  export function computed<T>(getter: () => T): ComputedRef<T>
  export function watch<T>(source: T, callback: (newVal: T, oldVal: T) => void): void
  export function watchEffect(effect: () => void): void
  export function onMounted(callback: () => void): void
  export function onUnmounted(callback: () => void): void
  export function onBeforeMount(callback: () => void): void
  export function onBeforeUnmount(callback: () => void): void
  export function onUpdated(callback: () => void): void
  export function onBeforeUpdate(callback: () => void): void
  export function nextTick(callback?: () => void): Promise<void>
  export function defineProps<T>(): T
  export function defineEmits<T>(): T
  export function defineExpose<T>(exposed: T): void
  export function withDefaults<T, D>(props: T, defaults: D): T & D
  
  export interface Ref<T> {
    value: T
  }
  
  export interface ComputedRef<T> {
    readonly value: T
  }
  
  export interface ComponentInternalInstance {
    props: any
    emit: any
    slots: any
    attrs: any
  }
  
  export function getCurrentInstance(): ComponentInternalInstance | null
}

// Tailwind CSS IntelliSense support
declare module 'tailwindcss' {
  export interface Config {
    content: string[]
    theme: any
    plugins: any[]
  }
}

// Common utility types
type Awaited<T> = T extends PromiseLike<infer U> ? U : T
type NonNullable<T> = T extends null | undefined ? never : T
type Partial<T> = { [P in keyof T]?: T[P] }
type Required<T> = { [P in keyof T]-?: T[P] }
type Readonly<T> = { readonly [P in keyof T]: T[P] }
`

  // Add Vue 3 type definitions
  monaco.value.languages.typescript.typescriptDefaults.addExtraLib(
    vueTypes,
    'file:///node_modules/@types/vue/index.d.ts'
  )

  // Add Tailwind CSS classes for better IntelliSense
  const tailwindClasses = `
declare const tailwindClasses: {
  // Layout
  'container': string
  'flex': string
  'grid': string
  'block': string
  'inline': string
  'inline-block': string
  'hidden': string
  
  // Flexbox & Grid
  'flex-row': string
  'flex-col': string
  'flex-wrap': string
  'items-center': string
  'items-start': string
  'items-end': string
  'justify-center': string
  'justify-between': string
  'justify-around': string
  
  // Spacing
  'p-0': string
  'p-1': string
  'p-2': string
  'p-4': string
  'p-8': string
  'm-0': string
  'm-1': string
  'm-2': string
  'm-4': string
  'm-8': string
  
  // Colors
  'text-white': string
  'text-black': string
  'text-gray-500': string
  'bg-white': string
  'bg-black': string
  'bg-gray-100': string
  'bg-blue-500': string
  
  // Typography
  'text-xs': string
  'text-sm': string
  'text-base': string
  'text-lg': string
  'text-xl': string
  'font-normal': string
  'font-medium': string
  'font-semibold': string
  'font-bold': string
  
  // Borders
  'border': string
  'border-2': string
  'border-gray-300': string
  'rounded': string
  'rounded-lg': string
  
  // Effects
  'shadow': string
  'shadow-lg': string
  'hover:bg-gray-100': string
  'focus:outline-none': string
  'transition-colors': string
}
`

  monaco.value.languages.typescript.typescriptDefaults.addExtraLib(
    tailwindClasses,
    'file:///node_modules/@types/tailwindcss/index.d.ts'
  )
}

// Register Vue language support
const registerVueLanguage = () => {
  if (!monaco.value) return

  monaco.value.languages.register({ id: 'vue' })

  // Set up Vue language configuration
  monaco.value.languages.setLanguageConfiguration('vue', {
    comments: {
      blockComment: ['<!--', '-->'],
      lineComment: '//'
    },
    brackets: [
      ['<', '>'],
      ['{', '}'],
      ['(', ')'],
      ['[', ']']
    ],
    autoClosingPairs: [
      { open: '<', close: '>' },
      { open: '{', close: '}' },
      { open: '(', close: ')' },
      { open: '[', close: ']' },
      { open: '"', close: '"' },
      { open: "'", close: "'" },
      { open: '`', close: '`' }
    ],
    surroundingPairs: [
      { open: '<', close: '>' },
      { open: '{', close: '}' },
      { open: '(', close: ')' },
      { open: '[', close: ']' },
      { open: '"', close: '"' },
      { open: "'", close: "'" },
      { open: '`', close: '`' }
    ]
  })

  // Basic Vue syntax highlighting
  monaco.value.languages.setMonarchTokensProvider('vue', {
    tokenizer: {
      root: [
        [/<template[^>]*>/, 'tag.template.begin'],
        [/<\/template>/, 'tag.template.end'],
        [/<script[^>]*>/, 'tag.script.begin'],
        [/<\/script>/, 'tag.script.end'],
        [/<style[^>]*>/, 'tag.style.begin'],
        [/<\/style>/, 'tag.style.end'],
        [/<[^>]+>/, 'tag'],
        [/{{[^}]*}}/, 'interpolation'],
        [/v-[a-zA-Z-]+/, 'directive'],
        [/@[a-zA-Z-]+/, 'event'],
        [/:[a-zA-Z-]+/, 'prop']
      ]
    }
  })
}

// Create Monaco Editor instance
const createEditor = () => {
  if (!monaco.value || !editorRef.value) return

  monacoEditor.value = monaco.value.editor.create(editorRef.value, {
    value: props.initialContent,
    language: getMonacoLanguage(props.language),
    theme: props.theme,
    readOnly: props.readonly,
    automaticLayout: true,
    fontSize: 14,
    fontFamily: 'JetBrains Mono, Fira Code, Monaco, Consolas, "Courier New", monospace',
    lineNumbers: 'on',
    lineNumbersMinChars: 3,
    minimap: { 
      enabled: true,
      maxColumn: 120,
      renderCharacters: false,
      showSlider: 'mouseover'
    },
    scrollBeyondLastLine: false,
    wordWrap: 'on',
    wordWrapColumn: 120,
    tabSize: 2,
    insertSpaces: true,
    detectIndentation: true,
    trimAutoWhitespace: true,
    formatOnPaste: true,
    formatOnType: true,
    autoIndent: 'full',
    
    // Enhanced IntelliSense settings
    suggestOnTriggerCharacters: true,
    acceptSuggestionOnEnter: 'on',
    acceptSuggestionOnCommitCharacter: true,
    snippetSuggestions: 'top',
    wordBasedSuggestions: 'matchingDocuments',
    suggestSelection: 'first',
    quickSuggestions: {
      other: 'on',
      comments: 'on',
      strings: 'on'
    },
    quickSuggestionsDelay: 100,
    
    // Parameter hints and hover
    parameterHints: {
      enabled: true,
      cycle: true
    },
    hover: {
      enabled: true,
      delay: 300,
      sticky: true
    },
    
    // Code actions and refactoring
    lightbulb: {
      enabled: true
    },
    codeActionsOnSave: {
      'source.fixAll': true,
      'source.organizeImports': true
    },
    
    // Error and warning indicators
    glyphMargin: true,
    folding: true,
    foldingStrategy: 'indentation',
    foldingHighlight: true,
    showFoldingControls: 'always',
    unfoldOnClickAfterEndOfLine: true,
    
    // Bracket matching and colorization
    matchBrackets: 'always',
    bracketPairColorization: {
      enabled: true,
      independentColorPoolPerBracketType: true
    },
    guides: {
      bracketPairs: true,
      bracketPairsHorizontal: true,
      highlightActiveBracketPair: true,
      indentation: true,
      highlightActiveIndentation: true
    },
    
    // Selection and cursor
    cursorBlinking: 'smooth',
    cursorSmoothCaretAnimation: 'on',
    cursorWidth: 2,
    selectOnLineNumbers: true,
    selectionHighlight: true,
    occurrencesHighlight: 'singleFile',
    
    // Scrolling and rendering
    smoothScrolling: true,
    mouseWheelZoom: true,
    contextmenu: true,
    copyWithSyntaxHighlighting: true,
    
    // Find and replace
    find: {
      addExtraSpaceOnTop: false,
      autoFindInSelection: 'never',
      seedSearchStringFromSelection: 'always'
    },
    
    // Accessibility
    accessibilitySupport: 'auto',
    ariaLabel: 'Code Editor',
    
    // Performance
    renderWhitespace: 'selection',
    renderControlCharacters: false,
    renderLineHighlight: 'line',
    renderLineHighlightOnlyWhenFocus: false,
    hideCursorInOverviewRuler: false,
    overviewRulerBorder: true,
    overviewRulerLanes: 3,
    
    // Diff editor specific (if needed)
    ignoreTrimWhitespace: false,
    renderSideBySide: true,
    
    // Multi-cursor
    multiCursorModifier: 'alt',
    multiCursorMergeOverlapping: true,
    
    // Sticky scroll
    stickyScroll: {
      enabled: true,
      maxLineCount: 5
    }
  })

  setupEditorEventListeners()
  setupValidation()
}

// Map language prop to Monaco language
const getMonacoLanguage = (lang: string): string => {
  const languageMap: Record<string, string> = {
    vue: 'vue',
    typescript: 'typescript',
    javascript: 'javascript',
    css: 'css',
    html: 'html',
    json: 'json'
  }
  return languageMap[lang] || 'typescript'
}

// Setup editor event listeners
const setupEditorEventListeners = () => {
  if (!monacoEditor.value || !monaco.value) return

  // Content change listener with file system integration
  const contentChangeDisposable = monacoEditor.value.onDidChangeModelContent(() => {
    const content = monacoEditor.value?.getValue() || ''
    trackFileChanges()
    emit('content-changed', content)
  })

  // Cursor position change listener
  const cursorChangeDisposable = monacoEditor.value.onDidChangeCursorPosition((e) => {
    emit('cursor-position-changed', e.position.lineNumber, e.position.column)
  })

  // Keyboard shortcuts
  const saveActionDisposable = monacoEditor.value.addAction({
    id: 'save-file',
    label: 'Save File',
    keybindings: [monaco.value.KeyMod.CtrlCmd | monaco.value.KeyCode.KeyS],
    run: () => {
      saveFile()
    }
  })

  const formatActionDisposable = monacoEditor.value.addAction({
    id: 'format-code',
    label: 'Format Code',
    keybindings: [monaco.value.KeyMod.CtrlCmd | monaco.value.KeyMod.Shift | monaco.value.KeyCode.KeyF],
    run: () => {
      formatCode()
    }
  })

  disposables.value.push(
    contentChangeDisposable,
    cursorChangeDisposable,
    saveActionDisposable,
    formatActionDisposable
  )
}

// Setup validation
const setupValidation = () => {
  if (!monaco.value) return

  // Listen for marker changes (errors, warnings, etc.)
  const markerChangeDisposable = monaco.value.editor.onDidChangeMarkers((uris) => {
    if (!monacoEditor.value) return

    const model = monacoEditor.value.getModel()
    if (!model || !uris.includes(model.uri)) return

    const markers = monaco.value.editor.getModelMarkers({ resource: model.uri })
    const errors: ValidationError[] = markers.map(marker => ({
      line: marker.startLineNumber,
      column: marker.startColumn,
      message: marker.message,
      severity: getSeverityString(marker.severity)
    }))

    validationErrors.value = errors
    emit('validation-error', errors)
  })

  disposables.value.push(markerChangeDisposable)
}

// Convert Monaco severity to string
const getSeverityString = (severity: number): 'error' | 'warning' | 'info' => {
  if (!monaco.value) return 'error'
  
  switch (severity) {
    case monaco.value.MarkerSeverity.Error:
      return 'error'
    case monaco.value.MarkerSeverity.Warning:
      return 'warning'
    case monaco.value.MarkerSeverity.Info:
      return 'info'
    default:
      return 'error'
  }
}

// Format code with enhanced formatting options
const formatCode = async () => {
  if (!monacoEditor.value || !monaco.value || isFormatting.value) return

  isFormatting.value = true
  try {
    // Try document formatting first
    const formatAction = monacoEditor.value.getAction('editor.action.formatDocument')
    if (formatAction) {
      await formatAction.run()
    } else {
      // Fallback to selection formatting if document formatting is not available
      const selection = monacoEditor.value.getSelection()
      if (selection) {
        const formatSelectionAction = monacoEditor.value.getAction('editor.action.formatSelection')
        if (formatSelectionAction) {
          await formatSelectionAction.run()
        }
      }
    }
    
    // Additional formatting for Vue files
    if (props.language === 'vue') {
      await formatVueFile()
    }
  } catch (error) {
    console.error('Failed to format code:', error)
  } finally {
    isFormatting.value = false
  }
}

// Enhanced Vue file formatting
const formatVueFile = async () => {
  if (!monacoEditor.value) return
  
  try {
    const content = monacoEditor.value.getValue()
    
    // Basic Vue SFC formatting
    const formatted = content
      .replace(/(<template[^>]*>)\s*\n/g, '$1\n  ')
      .replace(/\n\s*(<\/template>)/g, '\n$1')
      .replace(/(<script[^>]*>)\s*\n/g, '$1\n')
      .replace(/\n\s*(<\/script>)/g, '\n$1')
      .replace(/(<style[^>]*>)\s*\n/g, '$1\n')
      .replace(/\n\s*(<\/style>)/g, '\n$1')
    
    if (formatted !== content) {
      monacoEditor.value.setValue(formatted)
    }
  } catch (error) {
    console.error('Failed to format Vue file:', error)
  }
}

// Save file with file system integration
const saveFile = () => {
  if (!monacoEditor.value) return

  const content = monacoEditor.value.getValue()
  
  try {
    // Save to virtual file system
    if (fileExists.value) {
      fileSystemStore.vfs.updateFile(props.filePath, content)
    } else {
      fileSystemStore.createFile(props.filePath, content)
    }
    
    hasUnsavedChanges.value = false
    emit('file-saved', props.filePath, content)
  } catch (error) {
    console.error('Failed to save file:', error)
    // Still emit the event for parent components to handle
    emit('file-saved', props.filePath, content)
  }
}

// Go to error location
const goToError = (error: ValidationError) => {
  if (!monacoEditor.value) return

  monacoEditor.value.setPosition({
    lineNumber: error.line,
    column: error.column
  })
  monacoEditor.value.focus()
}

// Clear errors
const clearErrors = () => {
  validationErrors.value = []
}

// Update content programmatically
const updateContent = (content: string) => {
  if (!monacoEditor.value) return
  
  monacoEditor.value.setValue(content)
  hasUnsavedChanges.value = false
}

// Load file content from file system
const loadFileContent = () => {
  if (!monacoEditor.value) return
  
  try {
    const content = fileSystemStore.readFile(props.filePath)
    monacoEditor.value.setValue(content)
    hasUnsavedChanges.value = false
  } catch (error) {
    // File doesn't exist, use initial content
    monacoEditor.value.setValue(props.initialContent)
    hasUnsavedChanges.value = false
  }
}

// Auto-save functionality
const autoSave = ref(props.enableAutoSave)
const autoSaveDelay = ref(props.autoSaveDelay)
let autoSaveTimeout: NodeJS.Timeout | null = null

const scheduleAutoSave = () => {
  if (!autoSave.value) return
  
  if (autoSaveTimeout) {
    clearTimeout(autoSaveTimeout)
  }
  
  autoSaveTimeout = setTimeout(() => {
    if (hasUnsavedChanges.value) {
      saveFile()
    }
  }, autoSaveDelay.value)
}

// Track file changes
const trackFileChanges = () => {
  if (!monacoEditor.value) return
  
  const content = monacoEditor.value.getValue()
  const originalContent = fileExists.value ? fileContent.value : props.initialContent
  
  hasUnsavedChanges.value = content !== originalContent
  
  if (hasUnsavedChanges.value) {
    scheduleAutoSave()
  }
}

// Get current content
const getContent = (): string => {
  return monacoEditor.value?.getValue() || ''
}

// Focus editor
const focus = () => {
  monacoEditor.value?.focus()
}

// Watch for language changes
watch(() => props.language, (newLanguage) => {
  if (!monacoEditor.value || !monaco.value) return
  
  const model = monacoEditor.value.getModel()
  if (model) {
    monaco.value.editor.setModelLanguage(model, getMonacoLanguage(newLanguage))
  }
})

// Watch for theme changes
watch(() => props.theme, (newTheme) => {
  if (!monaco.value) return
  monaco.value.editor.setTheme(newTheme)
})

// Watch for file path changes
watch(() => props.filePath, (newPath, oldPath) => {
  if (newPath !== oldPath) {
    loadFileContent()
  }
})

// Watch for file system changes
watch(() => fileContent.value, (newContent) => {
  if (!monacoEditor.value) return
  
  const currentContent = monacoEditor.value.getValue()
  if (currentContent !== newContent && !hasUnsavedChanges.value) {
    monacoEditor.value.setValue(newContent)
  }
})

// Lifecycle
onMounted(async () => {
  await nextTick()
  await initializeMonaco()
  
  // Load file content after Monaco is initialized
  if (monacoEditor.value) {
    loadFileContent()
  }
})

onUnmounted(() => {
  // Clear auto-save timeout
  if (autoSaveTimeout) {
    clearTimeout(autoSaveTimeout)
  }
  
  // Dispose of all event listeners
  disposables.value.forEach(disposable => disposable.dispose())
  disposables.value = []
  
  // Dispose of editor
  monacoEditor.value?.dispose()
})

// Expose methods for parent components
defineExpose({
  updateContent,
  getContent,
  focus,
  formatCode,
  saveFile,
  loadFileContent,
  hasUnsavedChanges: () => hasUnsavedChanges.value,
  enableAutoSave: (enabled: boolean) => { autoSave.value = enabled },
  setAutoSaveDelay: (delay: number) => { autoSaveDelay.value = delay }
})
</script>

<style scoped>
.code-editor-container {
  @apply flex flex-col h-full bg-gray-900 text-white;
}

.editor-toolbar {
  @apply flex items-center justify-between px-4 py-2 bg-gray-800 border-b border-gray-700;
}

.toolbar-left {
  @apply flex items-center space-x-4;
}

.file-path {
  @apply text-sm text-gray-300 font-mono flex items-center space-x-1;
}

.file-path.has-changes {
  @apply text-yellow-300;
}

.unsaved-indicator {
  @apply text-yellow-400 ml-1;
}

.auto-save-indicator {
  @apply text-xs text-blue-400 ml-2;
}

.language-indicator {
  @apply px-2 py-1 text-xs bg-blue-600 text-white rounded;
}

.toolbar-right {
  @apply flex items-center space-x-2;
}

.format-btn,
.save-btn {
  @apply px-3 py-1 text-sm bg-gray-700 hover:bg-gray-600 text-white rounded transition-colors;
}

.format-btn:disabled,
.save-btn:disabled {
  @apply bg-gray-800 text-gray-500 cursor-not-allowed;
}

.monaco-editor-wrapper {
  @apply flex-1 min-h-0;
}

.monaco-editor-wrapper.with-toolbar {
  @apply h-[calc(100%-3rem)];
}

.error-panel {
  @apply bg-red-900 border-t border-red-700;
  max-height: 200px;
  overflow-y: auto;
}

.error-header {
  @apply flex items-center justify-between px-4 py-2 bg-red-800;
}

.error-header span {
  @apply text-sm font-medium text-red-100;
}

.clear-errors-btn {
  @apply px-2 py-1 text-xs bg-red-700 hover:bg-red-600 text-white rounded;
}

.error-list {
  @apply divide-y divide-red-700;
}

.error-item {
  @apply flex items-center space-x-3 px-4 py-2 hover:bg-red-800 cursor-pointer;
}

.error-severity {
  @apply px-2 py-1 text-xs rounded font-medium;
}

.error-severity.error {
  @apply bg-red-600 text-white;
}

.error-severity.warning {
  @apply bg-yellow-600 text-white;
}

.error-severity.info {
  @apply bg-blue-600 text-white;
}

.error-location {
  @apply text-xs text-red-300 font-mono;
}

.error-message {
  @apply text-sm text-red-100 flex-1;
}
</style>