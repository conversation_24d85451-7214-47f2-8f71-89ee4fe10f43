<template>
  <div class="modal-overlay" @click="handleCancel">
    <div class="modal-content" @click.stop>
      <h3 class="modal-title">建立新檔案</h3>
      
      <form @submit.prevent="handleConfirm">
        <div class="form-group">
          <label for="fileName" class="form-label">檔案名稱</label>
          <input
            id="fileName"
            v-model="fileName"
            type="text"
            class="form-input"
            :class="{ 'error': fileNameError }"
            required
            placeholder="例如: Component.vue"
            @input="validateFileName"
            ref="fileNameInput"
          />
          <div v-if="fileNameError" class="error-message">{{ fileNameError }}</div>
        </div>
        
        <div class="form-group">
          <label for="fileTemplate" class="form-label">檔案模板</label>
          <select 
            id="fileTemplate" 
            v-model="selectedTemplate" 
            class="form-select"
            @change="updateContent"
          >
            <option value="">空白檔案</option>
            <option value="vue-component">Vue 組件</option>
            <option value="vue-page">Vue 頁面</option>
            <option value="typescript">TypeScript 檔案</option>
            <option value="javascript">JavaScript 檔案</option>
            <option value="css">CSS 檔案</option>
            <option value="scss">SCSS 檔案</option>
            <option value="json">JSON 檔案</option>
          </select>
        </div>
        
        <div v-if="selectedTemplate" class="form-group">
          <label for="fileContent" class="form-label">檔案內容預覽</label>
          <textarea
            id="fileContent"
            v-model="fileContent"
            class="form-textarea"
            rows="8"
            readonly
          ></textarea>
        </div>
        
        <div class="form-actions">
          <button type="button" @click="handleCancel" class="btn btn-secondary">
            取消
          </button>
          <button 
            type="submit" 
            :disabled="!fileName.trim() || !!fileNameError || isCreating"
            class="btn btn-primary"
          >
            {{ isCreating ? '建立中...' : '建立' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue'
import type { CreateFileDialogProps, CreateFileDialogEmits } from '../../types/file-tree'

// Props
const props = defineProps<CreateFileDialogProps>()

// Emits
const emit = defineEmits<CreateFileDialogEmits>()

// Local state
const fileName = ref('')
const selectedTemplate = ref('')
const fileContent = ref('')
const fileNameError = ref('')
const isCreating = ref(false)
const fileNameInput = ref<HTMLInputElement>()

/**
 * 驗證檔案名稱
 */
const validateFileName = () => {
  const name = fileName.value.trim()
  fileNameError.value = ''

  if (!name) {
    return
  }

  // 檢查檔案名稱格式
  const invalidChars = /[<>:"/\\|?*]/
  if (invalidChars.test(name)) {
    fileNameError.value = '檔案名稱包含無效字元'
    return
  }

  // 檢查檔案名稱長度
  if (name.length > 255) {
    fileNameError.value = '檔案名稱過長'
    return
  }

  // TODO: 檢查是否已存在（需要從 store 獲取現有檔案列表）
}

/**
 * 更新檔案內容
 */
const updateContent = () => {
  fileContent.value = getTemplateContent(selectedTemplate.value)
}

/**
 * 取得檔案模板內容
 */
const getTemplateContent = (template: string): string => {
  switch (template) {
    case 'vue-component':
      return `<template>
  <div class="component">
    <!-- 組件內容 -->
  </div>
</template>

<script setup lang="ts">
// 組件邏輯
</script>

<style scoped>
.component {
  /* 組件樣式 */
}
</style>`

    case 'vue-page':
      return `<template>
  <div class="page">
    <h1>頁面標題</h1>
    <!-- 頁面內容 -->
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

// 頁面邏輯
onMounted(() => {
  // 頁面載入時執行
})
</script>

<style scoped>
.page {
  padding: 2rem;
}
</style>`

    case 'typescript':
      return `// TypeScript 檔案
export interface ExampleInterface {
  id: string
  name: string
}

export class ExampleClass {
  constructor(private data: ExampleInterface) {}
  
  public getName(): string {
    return this.data.name
  }
}`

    case 'javascript':
      return `// JavaScript 檔案
export function exampleFunction() {
  console.log('Hello, World!')
}

export const exampleConstant = 'example'`

    case 'css':
      return `/* CSS 樣式檔案 */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem;
}`

    case 'scss':
      return `// SCSS 樣式檔案
$primary-color: #3b82f6;
$secondary-color: #6b7280;

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem;
  
  .header {
    color: $primary-color;
    margin-bottom: 1rem;
  }
}`

    case 'json':
      return `{
  "name": "example",
  "version": "1.0.0",
  "description": "範例 JSON 檔案"
}`

    default:
      return ''
  }
}

/**
 * 處理確認建立
 */
const handleConfirm = async () => {
  if (!fileName.value.trim() || fileNameError.value) {
    return
  }

  isCreating.value = true
  try {
    emit('confirm', fileName.value.trim(), fileContent.value)
  } catch (error) {
    console.error('建立檔案失敗:', error)
  } finally {
    isCreating.value = false
  }
}

/**
 * 處理取消
 */
const handleCancel = () => {
  emit('cancel')
}

onMounted(async () => {
  // 自動聚焦到檔案名稱輸入框
  await nextTick()
  fileNameInput.value?.focus()
})
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  padding: 24px;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  max-height: 80vh;
  overflow-y: auto;
}

.modal-title {
  margin: 0 0 20px 0;
  font-size: 18px;
  font-weight: 600;
  color: #374151;
}

.form-group {
  margin-bottom: 16px;
}

.form-label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  color: #374151;
  font-size: 14px;
}

.form-input,
.form-select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s;
}

.form-input:focus,
.form-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input.error {
  border-color: #dc2626;
}

.form-textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 13px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  background-color: #f9fafb;
  resize: vertical;
  min-height: 120px;
}

.error-message {
  color: #dc2626;
  font-size: 12px;
  margin-top: 4px;
}

.form-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 20px;
}

.btn {
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s;
  border: 1px solid transparent;
}

.btn-secondary {
  background: white;
  color: #6b7280;
  border-color: #d1d5db;
}

.btn-secondary:hover {
  background: #f9fafb;
  border-color: #9ca3af;
}

.btn-primary {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.btn-primary:hover:not(:disabled) {
  background: #2563eb;
  border-color: #2563eb;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .modal-content {
    padding: 20px;
    margin: 16px;
    max-height: calc(100vh - 32px);
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .btn {
    width: 100%;
  }
}
</style>