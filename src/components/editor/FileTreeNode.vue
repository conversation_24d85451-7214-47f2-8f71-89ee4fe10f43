<template>
  <div class="file-tree-node">
    <div 
      class="node-content"
      :class="{ 
        'is-selected': isSelected,
        'is-modified': node.isModified,
        'is-drag-over': isDragOver
      }"
      :style="{ paddingLeft: `${level * 16 + 8}px` }"
      :draggable="true"
      @click="handleClick"
      @contextmenu.prevent="showContextMenu"
      @dragstart="handleDragStart"
      @dragover.prevent="handleDragOver"
      @dragleave="handleDragLeave"
      @drop.prevent="handleDrop"
    >
      <!-- 展開/收合圖示 -->
      <button 
        v-if="node.type === 'folder'" 
        class="expand-button"
        @click.stop="toggleExpanded"
        :aria-label="node.isExpanded ? '收合資料夾' : '展開資料夾'"
      >
        <span class="expand-icon" :class="{ 'expanded': node.isExpanded }">
          ▶
        </span>
      </button>
      <div v-else class="expand-spacer"></div>
      
      <!-- 檔案/資料夾圖示 -->
      <span class="node-icon">
        {{ getNodeIcon() }}
      </span>
      
      <!-- 檔案/資料夾名稱 -->
      <span class="node-name" :title="node.path">
        {{ node.name }}
      </span>
      
      <!-- 變更指示器 -->
      <span v-if="node.isModified" class="modified-indicator" title="檔案已修改">
        ●
      </span>
      
      <!-- 檔案大小 (僅檔案) -->
      <span v-if="node.type === 'file' && node.size !== undefined" class="file-size">
        {{ formatFileSize(node.size) }}
      </span>
      
      <!-- 操作按鈕 (hover 時顯示) -->
      <div class="node-actions">
        <button 
          v-if="node.type === 'folder'"
          @click.stop="handleCreateFile"
          class="action-btn"
          title="新增檔案"
        >
          <span class="action-icon">📄</span>
        </button>
        <button 
          v-if="node.type === 'folder'"
          @click.stop="showCreateFolderDialog"
          class="action-btn"
          title="新增資料夾"
        >
          <span class="action-icon">📁</span>
        </button>
        <button 
          @click.stop="handleRename"
          class="action-btn"
          title="重新命名"
        >
          <span class="action-icon">✏️</span>
        </button>
        <button 
          @click.stop="handleDelete"
          class="action-btn danger"
          title="刪除"
        >
          <span class="action-icon">🗑️</span>
        </button>
      </div>
    </div>
    
    <!-- 子節點 -->
    <div v-if="node.type === 'folder' && node.isExpanded && node.children" class="children">
      <FileTreeNode
        v-for="child in node.children"
        :key="child.id"
        :node="child"
        :level="level + 1"
        :selected-file="selectedFile"
        :unsaved-changes="unsavedChanges"
        @file-selected="$emit('file-selected', $event)"
        @file-created="$emit('file-created', $event)"
        @file-deleted="$emit('file-deleted', $event)"
        @file-renamed="$emit('file-renamed', $event)"
        @folder-created="$emit('folder-created', $event)"
      />
    </div>
    
    <!-- 右鍵選單 -->
    <ContextMenu
      v-if="showMenu"
      :x="menuPosition.x"
      :y="menuPosition.y"
      :items="contextMenuItems"
      @select="handleMenuSelect"
      @close="hideContextMenu"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import type { FileNode } from '../../types/file-tree'
import ContextMenu from '../ui/ContextMenu.vue'

// Props
interface Props {
  node: FileNode
  level: number
  selectedFile: string
  unsavedChanges: Set<string>
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'file-selected': [filePath: string]
  'file-created': [parentPath: string, fileName: string]
  'file-deleted': [filePath: string]
  'file-renamed': [oldPath: string, newPath: string]
  'folder-created': [parentPath: string, folderName: string]
}>()

// Local state
const showMenu = ref(false)
const menuPosition = ref({ x: 0, y: 0 })
const isDragOver = ref(false)

// Computed
const isSelected = computed(() => {
  return props.node.type === 'file' && props.node.path === props.selectedFile
})

const contextMenuItems = computed(() => {
  const items = [
    {
      id: 'rename',
      label: '重新命名',
      icon: '✏️'
    }
  ]

  if (props.node.type === 'folder') {
    items.push(
      {
        id: 'create-file',
        label: '新增檔案',
        icon: '📄'
      },
      {
        id: 'create-folder',
        label: '新增資料夾',
        icon: '📁'
      }
    )
  }

  items.push(
    { id: 'separator', label: '', icon: '' },
    {
      id: 'delete',
      label: '刪除',
      icon: '🗑️',
      danger: true
    }
  )

  return items
})

/**
 * 處理節點點擊
 */
const handleClick = () => {
  if (props.node.type === 'file') {
    emit('file-selected', props.node.path)
  } else {
    toggleExpanded()
  }
}

/**
 * 切換資料夾展開狀態
 */
const toggleExpanded = () => {
  if (props.node.type === 'folder') {
    props.node.isExpanded = !props.node.isExpanded
  }
}

/**
 * 顯示右鍵選單
 */
const showContextMenu = (event: MouseEvent) => {
  menuPosition.value = {
    x: event.clientX,
    y: event.clientY
  }
  showMenu.value = true
}

/**
 * 隱藏右鍵選單
 */
const hideContextMenu = () => {
  showMenu.value = false
}

/**
 * 處理選單選擇
 */
const handleMenuSelect = (itemId: string) => {
  switch (itemId) {
    case 'rename':
      handleRename()
      break
    case 'create-file':
      handleCreateFile()
      break
    case 'create-folder':
      handleCreateFolder()
      break
    case 'delete':
      handleDelete()
      break
  }
  hideContextMenu()
}

/**
 * 處理重新命名
 */
const handleRename = () => {
  const newName = prompt('請輸入新名稱:', props.node.name)
  if (newName && newName.trim() && newName !== props.node.name) {
    // 驗證檔案名稱
    const invalidChars = /[<>:"/\\|?*]/
    if (invalidChars.test(newName.trim())) {
      alert('檔案名稱包含無效字元')
      return
    }
    
    // 建構新路徑
    const pathParts = props.node.path.split('/')
    pathParts[pathParts.length - 1] = newName.trim()
    const newPath = pathParts.join('/')
    emit('file-renamed', props.node.path, newPath)
  }
}

/**
 * 處理建立檔案
 */
const handleCreateFile = () => {
  if (props.node.type === 'folder') {
    emit('file-created', props.node.path, '')
  }
}

/**
 * 處理建立資料夾
 */
const handleCreateFolder = () => {
  if (props.node.type === 'folder') {
    const folderName = prompt('請輸入資料夾名稱:')
    if (folderName && folderName.trim()) {
      // Emit event to parent to handle folder creation
      emit('folder-created', props.node.path, folderName.trim())
    }
  }
}

/**
 * 顯示建立資料夾對話框
 */
const showCreateFolderDialog = () => {
  handleCreateFolder()
}

/**
 * 處理拖拽開始
 */
const handleDragStart = (event: DragEvent) => {
  if (event.dataTransfer) {
    event.dataTransfer.setData('text/plain', JSON.stringify({
      type: 'file-tree-node',
      path: props.node.path,
      name: props.node.name,
      nodeType: props.node.type
    }))
    event.dataTransfer.effectAllowed = 'move'
  }
}

/**
 * 處理拖拽懸停
 */
const handleDragOver = (event: DragEvent) => {
  if (props.node.type === 'folder' && event.dataTransfer) {
    const data = event.dataTransfer.getData('text/plain')
    if (data) {
      try {
        const dragData = JSON.parse(data)
        if (dragData.type === 'file-tree-node' && dragData.path !== props.node.path) {
          event.dataTransfer.dropEffect = 'move'
          isDragOver.value = true
        }
      } catch (error) {
        // Invalid drag data
      }
    }
  }
}

/**
 * 處理拖拽離開
 */
const handleDragLeave = () => {
  isDragOver.value = false
}

/**
 * 處理拖拽放下
 */
const handleDrop = (event: DragEvent) => {
  isDragOver.value = false
  
  if (props.node.type === 'folder' && event.dataTransfer) {
    const data = event.dataTransfer.getData('text/plain')
    if (data) {
      try {
        const dragData = JSON.parse(data)
        if (dragData.type === 'file-tree-node' && dragData.path !== props.node.path) {
          // 建構新路徑
          const newPath = `${props.node.path}/${dragData.name}`
          emit('file-renamed', dragData.path, newPath)
        }
      } catch (error) {
        console.error('處理拖拽放下失敗:', error)
      }
    }
  }
}

/**
 * 處理刪除
 */
const handleDelete = () => {
  const itemType = props.node.type === 'file' ? '檔案' : '資料夾'
  if (confirm(`確定要刪除${itemType} "${props.node.name}" 嗎？`)) {
    emit('file-deleted', props.node.path)
  }
}

/**
 * 取得節點圖示
 */
const getNodeIcon = (): string => {
  if (props.node.type === 'folder') {
    return props.node.isExpanded ? '📂' : '📁'
  }
  
  // 根據檔案副檔名返回對應圖示
  const ext = props.node.name.split('.').pop()?.toLowerCase()
  
  switch (ext) {
    case 'vue':
      return '🟢'
    case 'js':
    case 'mjs':
      return '🟨'
    case 'ts':
      return '🔷'
    case 'css':
    case 'scss':
    case 'sass':
      return '🎨'
    case 'html':
      return '🌐'
    case 'json':
      return '📋'
    case 'md':
      return '📝'
    case 'png':
    case 'jpg':
    case 'jpeg':
    case 'gif':
    case 'svg':
      return '🖼️'
    default:
      return '📄'
  }
}

/**
 * 格式化檔案大小
 */
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}
</script>

<style scoped>
.file-tree-node {
  user-select: none;
}

.node-content {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s;
  min-height: 24px;
  position: relative;
}

.node-content:hover {
  background-color: #f3f4f6;
}

.node-content.is-selected {
  background-color: #dbeafe;
  color: #1d4ed8;
}

.node-content.is-modified {
  font-weight: 500;
}

.node-content.is-drag-over {
  background-color: #dbeafe;
  border: 2px dashed #3b82f6;
  border-radius: 4px;
}

.node-content[draggable="true"] {
  cursor: grab;
}

.node-content[draggable="true"]:active {
  cursor: grabbing;
}

.expand-button {
  width: 16px;
  height: 16px;
  border: none;
  background: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 4px;
  padding: 0;
  border-radius: 2px;
  transition: background-color 0.2s;
}

.expand-button:hover {
  background-color: #e5e7eb;
}

.expand-spacer {
  width: 16px;
  margin-right: 4px;
}

.expand-icon {
  font-size: 10px;
  color: #6b7280;
  transition: transform 0.2s;
  transform: rotate(0deg);
}

.expand-icon.expanded {
  transform: rotate(90deg);
}

.node-icon {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
  font-size: 12px;
}

.node-name {
  flex: 1;
  font-size: 13px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  min-width: 0;
}

.modified-indicator {
  color: #f59e0b;
  font-size: 12px;
  margin-left: 4px;
  margin-right: 4px;
}

.file-size {
  font-size: 11px;
  color: #9ca3af;
  margin-left: 8px;
  white-space: nowrap;
}

.node-actions {
  display: none;
  align-items: center;
  gap: 2px;
  margin-left: 8px;
}

.node-content:hover .node-actions {
  display: flex;
}

.action-btn {
  width: 20px;
  height: 20px;
  border: none;
  background: none;
  cursor: pointer;
  border-radius: 3px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
  padding: 0;
}

.action-btn:hover {
  background-color: #e5e7eb;
}

.action-btn.danger:hover {
  background-color: #fef2f2;
}

.action-icon {
  font-size: 10px;
}

.action-btn.danger .action-icon {
  color: #dc2626;
}

.children {
  /* 子節點容器 */
}

/* 響應式設計 */
@media (max-width: 768px) {
  .node-content {
    padding: 6px 8px;
    min-height: 28px;
  }
  
  .node-name {
    font-size: 14px;
  }
  
  .file-size {
    display: none; /* 在小螢幕上隱藏檔案大小 */
  }
}
</style>