<template>
  <div class="project-manager">
    <!-- 專案管理標題 -->
    <div class="project-manager-header">
      <h2 class="title">專案管理</h2>
      <div class="header-actions">
        <button 
          @click="refreshProjects" 
          :disabled="isLoading"
          class="refresh-btn"
          :class="{ loading: isLoading }"
        >
          <span v-if="isLoading">載入中...</span>
          <span v-else>重新整理</span>
        </button>
        <button 
          @click="showCreateDialog = true" 
          class="create-btn"
        >
          新建專案
        </button>
      </div>
    </div>

    <!-- 錯誤提示 -->
    <div v-if="error" class="error-message">
      <span>{{ error }}</span>
      <button @click="clearError" class="error-close">×</button>
    </div>

    <!-- 專案列表 -->
    <div class="project-list">
      <div v-if="isLoading && projects.length === 0" class="loading-state">
        <div class="loading-spinner"></div>
        <span>載入專案中...</span>
      </div>

      <div v-else-if="projects.length === 0" class="empty-state">
        <div class="empty-icon">📁</div>
        <h3>尚無專案</h3>
        <p>建立您的第一個專案開始使用</p>
        <button @click="showCreateDialog = true" class="create-first-btn">
          建立專案
        </button>
      </div>

      <div v-else class="project-grid">
        <div 
          v-for="project in projects" 
          :key="project.id"
          class="project-card"
          :class="{ 
            active: currentProject?.id === project.id,
            loading: loadingProjectId === project.id 
          }"
        >
          <div class="project-card-header">
            <h3 class="project-name">{{ project.name }}</h3>
            <div class="project-actions">
              <button 
                @click="editProject(project)"
                class="action-btn edit-btn"
                title="編輯專案"
              >
                ✏️
              </button>
              <button 
                @click="confirmDeleteProject(project)"
                class="action-btn delete-btn"
                title="刪除專案"
              >
                🗑️
              </button>
            </div>
          </div>

          <div class="project-description">
            {{ project.description || '無描述' }}
          </div>

          <div class="project-meta">
            <div class="file-count">
              {{ project.files?.length || 0 }} 個檔案
            </div>
            <div class="project-date">
              {{ formatDate(project.updatedAt) }}
            </div>
          </div>

          <div class="project-card-footer">
            <button 
              @click="selectProject(project)"
              :disabled="loadingProjectId === project.id"
              class="select-btn"
              :class="{ 
                selected: currentProject?.id === project.id,
                loading: loadingProjectId === project.id 
              }"
            >
              <span v-if="loadingProjectId === project.id">載入中...</span>
              <span v-else-if="currentProject?.id === project.id">已選擇</span>
              <span v-else>選擇專案</span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 建立專案對話框 -->
    <div v-if="showCreateDialog" class="modal-overlay" @click="closeCreateDialog">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>{{ editingProject ? '編輯專案' : '建立新專案' }}</h3>
          <button @click="closeCreateDialog" class="modal-close">×</button>
        </div>

        <form @submit.prevent="submitProject" class="project-form">
          <div class="form-group">
            <label for="projectName">專案名稱 *</label>
            <input 
              id="projectName"
              v-model="projectForm.name" 
              type="text" 
              required 
              placeholder="輸入專案名稱"
              maxlength="255"
              :disabled="isSubmitting"
            />
            <div v-if="projectForm.name.length > 200" class="form-hint warning">
              名稱長度: {{ projectForm.name.length }}/255
            </div>
          </div>

          <div class="form-group">
            <label for="projectDescription">專案描述</label>
            <textarea 
              id="projectDescription"
              v-model="projectForm.description" 
              placeholder="輸入專案描述（可選）"
              rows="3"
              maxlength="1000"
              :disabled="isSubmitting"
            ></textarea>
            <div v-if="projectForm.description && projectForm.description.length > 800" class="form-hint warning">
              描述長度: {{ projectForm.description.length }}/1000
            </div>
          </div>

          <div class="form-actions">
            <button 
              type="button" 
              @click="closeCreateDialog"
              :disabled="isSubmitting"
              class="cancel-btn"
            >
              取消
            </button>
            <button 
              type="submit" 
              :disabled="!projectForm.name.trim() || isSubmitting"
              class="submit-btn"
            >
              <span v-if="isSubmitting">處理中...</span>
              <span v-else>{{ editingProject ? '更新' : '建立' }}</span>
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- 刪除確認對話框 -->
    <div v-if="showDeleteDialog" class="modal-overlay" @click="closeDeleteDialog">
      <div class="modal-content delete-modal" @click.stop>
        <div class="modal-header">
          <h3>確認刪除</h3>
          <button @click="closeDeleteDialog" class="modal-close">×</button>
        </div>

        <div class="delete-content">
          <div class="warning-icon">⚠️</div>
          <p>您確定要刪除專案 <strong>{{ projectToDelete?.name }}</strong> 嗎？</p>
          <p class="warning-text">此操作無法復原，專案中的所有檔案都將被永久刪除。</p>
        </div>

        <div class="form-actions">
          <button 
            type="button" 
            @click="closeDeleteDialog"
            :disabled="isDeleting"
            class="cancel-btn"
          >
            取消
          </button>
          <button 
            @click="executeDelete"
            :disabled="isDeleting"
            class="delete-confirm-btn"
          >
            <span v-if="isDeleting">刪除中...</span>
            <span v-else>確認刪除</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { storeToRefs } from 'pinia'
import { useProjectStore, type Project } from '../../stores/project'

// Props
interface ProjectManagerProps {
  showCreateDialog?: boolean
}

const props = withDefaults(defineProps<ProjectManagerProps>(), {
  showCreateDialog: false
})

// Emits
interface ProjectManagerEmits {
  'project-selected': (projectId: string) => void
  'project-created': (project: Project) => void
  'project-deleted': (projectId: string) => void
}

const emit = defineEmits<ProjectManagerEmits>()

// Store
const projectStore = useProjectStore()
const { 
  currentProject, 
  projects, 
  isLoading, 
  error 
} = storeToRefs(projectStore)

const {
  loadProjects,
  createProject,
  setCurrentProject,
  updateProject,
  deleteProject,
  clearError
} = projectStore

// 本地狀態
const showCreateDialog = ref(props.showCreateDialog)
const showDeleteDialog = ref(false)
const editingProject = ref<Project | null>(null)
const projectToDelete = ref<Project | null>(null)
const loadingProjectId = ref<string | null>(null)
const isSubmitting = ref(false)
const isDeleting = ref(false)

// 表單資料
const projectForm = ref({
  name: '',
  description: ''
})

// 計算屬性
const projectCount = computed(() => projects.value.length)

// 方法
const refreshProjects = async () => {
  try {
    await loadProjects()
  } catch (err) {
    console.error('重新載入專案失敗:', err)
  }
}

const selectProject = async (project: Project) => {
  if (currentProject.value?.id === project.id) {
    return
  }

  loadingProjectId.value = project.id
  try {
    await setCurrentProject(project)
    emit('project-selected', project.id)
  } catch (err) {
    console.error('選擇專案失敗:', err)
  } finally {
    loadingProjectId.value = null
  }
}

const editProject = (project: Project) => {
  editingProject.value = project
  projectForm.value = {
    name: project.name,
    description: project.description || ''
  }
  showCreateDialog.value = true
}

const confirmDeleteProject = (project: Project) => {
  projectToDelete.value = project
  showDeleteDialog.value = true
}

const submitProject = async () => {
  if (!projectForm.value.name.trim()) {
    return
  }

  isSubmitting.value = true
  try {
    const projectData = {
      name: projectForm.value.name.trim(),
      description: projectForm.value.description.trim() || undefined
    }

    let result: Project
    if (editingProject.value) {
      // 更新專案
      result = await updateProject(editingProject.value.id, projectData)
    } else {
      // 建立新專案
      result = await createProject(projectData)
      emit('project-created', result)
    }

    closeCreateDialog()
  } catch (err) {
    console.error('提交專案失敗:', err)
  } finally {
    isSubmitting.value = false
  }
}

const executeDelete = async () => {
  if (!projectToDelete.value) {
    return
  }

  isDeleting.value = true
  try {
    await deleteProject(projectToDelete.value.id)
    emit('project-deleted', projectToDelete.value.id)
    closeDeleteDialog()
  } catch (err) {
    console.error('刪除專案失敗:', err)
  } finally {
    isDeleting.value = false
  }
}

const closeCreateDialog = () => {
  showCreateDialog.value = false
  editingProject.value = null
  projectForm.value = {
    name: '',
    description: ''
  }
}

const closeDeleteDialog = () => {
  showDeleteDialog.value = false
  projectToDelete.value = null
}

const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-TW', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

// 生命週期
onMounted(() => {
  if (projects.value.length === 0) {
    refreshProjects()
  }
})
</script>

<style scoped>
.project-manager {
  padding: 1.5rem;
  max-width: 1200px;
  margin: 0 auto;
}

/* 標題區域 */
.project-manager-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #e9ecef;
}

.title {
  font-size: 2rem;
  font-weight: 700;
  color: #2c3e50;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 1rem;
}

.refresh-btn,
.create-btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.refresh-btn {
  background: #6c757d;
  color: white;
}

.refresh-btn:hover:not(:disabled) {
  background: #5a6268;
}

.refresh-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.refresh-btn.loading {
  animation: pulse 1.5s ease-in-out infinite;
}

.create-btn {
  background: #007bff;
  color: white;
}

.create-btn:hover {
  background: #0056b3;
}

/* 錯誤訊息 */
.error-message {
  background: #f8d7da;
  color: #721c24;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border: 1px solid #f5c6cb;
}

.error-close {
  background: none;
  border: none;
  color: #721c24;
  font-size: 1.25rem;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 載入狀態 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 3rem;
  color: #6c757d;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e9ecef;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

/* 空狀態 */
.empty-state {
  text-align: center;
  padding: 4rem 2rem;
  color: #6c757d;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.empty-state h3 {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  color: #495057;
}

.empty-state p {
  margin-bottom: 2rem;
  font-size: 1.1rem;
}

.create-first-btn {
  background: #28a745;
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.create-first-btn:hover {
  background: #218838;
}

/* 專案網格 */
.project-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 1.5rem;
}

/* 專案卡片 */
.project-card {
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  padding: 1.5rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.project-card:hover {
  border-color: #007bff;
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
  transform: translateY(-2px);
}

.project-card.active {
  border-color: #28a745;
  background: #f8fff9;
}

.project-card.loading {
  opacity: 0.7;
}

.project-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.project-name {
  font-size: 1.25rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
  flex: 1;
  margin-right: 1rem;
  word-break: break-word;
}

.project-actions {
  display: flex;
  gap: 0.5rem;
}

.action-btn {
  background: none;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  width: 32px;
  height: 32px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.edit-btn:hover {
  background: #e3f2fd;
  border-color: #2196f3;
}

.delete-btn:hover {
  background: #ffebee;
  border-color: #f44336;
}

.project-description {
  color: #6c757d;
  margin-bottom: 1rem;
  line-height: 1.5;
  min-height: 1.5em;
}

.project-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  font-size: 0.875rem;
  color: #6c757d;
}

.project-card-footer {
  margin-top: auto;
}

.select-btn {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #007bff;
  border-radius: 8px;
  background: white;
  color: #007bff;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.select-btn:hover:not(:disabled) {
  background: #007bff;
  color: white;
}

.select-btn.selected {
  background: #28a745;
  border-color: #28a745;
  color: white;
}

.select-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 對話框 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.modal-content {
  background: white;
  border-radius: 12px;
  width: 100%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 1.5rem 0;
  margin-bottom: 1.5rem;
}

.modal-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.5rem;
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #6c757d;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.modal-close:hover {
  background: #f8f9fa;
}

/* 表單 */
.project-form {
  padding: 0 1.5rem 1.5rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #495057;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #dee2e6;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.2s ease;
  box-sizing: border-box;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #007bff;
}

.form-group textarea {
  resize: vertical;
  font-family: inherit;
}

.form-hint {
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

.form-hint.warning {
  color: #856404;
}

.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 2rem;
}

.cancel-btn,
.submit-btn {
  padding: 0.75rem 1.5rem;
  border: 2px solid;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancel-btn {
  background: white;
  color: #6c757d;
  border-color: #dee2e6;
}

.cancel-btn:hover:not(:disabled) {
  background: #f8f9fa;
}

.submit-btn {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

.submit-btn:hover:not(:disabled) {
  background: #0056b3;
}

.submit-btn:disabled,
.cancel-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 刪除對話框 */
.delete-modal {
  max-width: 400px;
}

.delete-content {
  padding: 0 1.5rem;
  text-align: center;
}

.warning-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.delete-content p {
  margin-bottom: 1rem;
  line-height: 1.5;
}

.warning-text {
  color: #dc3545;
  font-size: 0.875rem;
}

.delete-confirm-btn {
  background: #dc3545;
  color: white;
  border-color: #dc3545;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.delete-confirm-btn:hover:not(:disabled) {
  background: #c82333;
}

.delete-confirm-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 動畫 */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* 響應式設計 */
@media (max-width: 768px) {
  .project-manager {
    padding: 1rem;
  }

  .project-manager-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .header-actions {
    justify-content: center;
  }

  .project-grid {
    grid-template-columns: 1fr;
  }

  .modal-overlay {
    padding: 0.5rem;
  }

  .form-actions {
    flex-direction: column;
  }

  .cancel-btn,
  .submit-btn {
    width: 100%;
  }
}
</style>