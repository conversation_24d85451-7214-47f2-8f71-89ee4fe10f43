<template>
  <div class="flex flex-col h-full bg-gray-50" @click="showActionsMenu = false">
    <!-- 聊天標題欄 -->
    <div class="bg-white border-b border-gray-200 shadow-sm">
      <div class="px-4 py-3 sm:px-6">
        <div class="flex items-center justify-between">
          <!-- 左側標題區 -->
          <div class="flex items-center space-x-3">
            <div class="flex items-center space-x-2">
              <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"/>
                </svg>
              </div>
              <h2 class="text-lg font-semibold text-gray-900 hidden sm:block">AI 助手</h2>
            </div>
            
            <!-- 連接狀態指示器 -->
            <div class="flex items-center space-x-2">
              <div 
                :class="[
                  'w-2 h-2 rounded-full',
                  connectionStatus === 'connected' ? 'bg-green-500 animate-pulse-slow' : '',
                  connectionStatus === 'connecting' ? 'bg-yellow-500 animate-pulse' : '',
                  connectionStatus === 'disconnected' ? 'bg-gray-400' : '',
                  connectionStatus === 'error' ? 'bg-red-500' : ''
                ]"
              ></div>
              <span class="text-sm text-gray-600 hidden sm:inline">
                {{ getConnectionStatusText() }}
              </span>
            </div>
          </div>

          <!-- 右側操作區 -->
          <div class="flex items-center space-x-2">
            <!-- 重新連接按鈕 -->
            <button
              v-if="connectionStatus === 'disconnected' || connectionStatus === 'error'"
              @click="retryConnection"
              class="btn btn-primary btn-sm touch-manipulation"
            >
              <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
              </svg>
              <span class="hidden sm:inline">重新連接</span>
            </button>

            <!-- 更多操作選單 -->
            <div class="relative">
              <button
                @click="showActionsMenu = !showActionsMenu"
                class="btn btn-secondary btn-sm touch-manipulation"
                :class="{ 'bg-gray-200': showActionsMenu }"
              >
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"/>
                </svg>
              </button>
              
              <!-- 下拉選單 -->
              <div
                v-if="showActionsMenu"
                class="absolute right-0 top-full mt-1 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-10"
                @click="showActionsMenu = false"
              >
                <button
                  @click="clearChat"
                  :disabled="messages.length === 0"
                  class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed touch-manipulation"
                >
                  <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                  </svg>
                  清空對話
                </button>
                <button
                  @click="exportChat"
                  :disabled="messages.length === 0"
                  class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed touch-manipulation"
                >
                  <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                  </svg>
                  匯出對話
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 聊天訊息區域 -->
    <div class="flex-1 overflow-hidden">
      <MessageList
        :messages="messages"
        :is-loading="isLoading"
        :current-streaming-message-id="currentStreamingMessageId"
        :error-message="connectionError"
        :loading-text="getLoadingText()"
        @suggestion-selected="sendSuggestion"
        @message-delete="deleteMessage"
        @code-copy="copyCode"
        @code-apply="applyCode"
        @error-retry="retryConnection"
      />
    </div>

    <!-- 輸入區域 -->
    <div class="bg-white border-t border-gray-200 safe-area-inset">
      <div class="px-4 py-3 sm:px-6">
        <!-- 快速操作按鈕 (移動端在上方) -->
        <div class="mb-3 sm:hidden">
          <div class="flex space-x-2 overflow-x-auto scrollbar-hide pb-2">
            <button
              v-for="action in quickActions"
              :key="action.text"
              @click="addQuickAction(action.prompt)"
              class="flex-shrink-0 inline-flex items-center px-3 py-2 text-xs font-medium text-gray-700 bg-gray-100 rounded-full hover:bg-gray-200 transition-colors touch-manipulation"
            >
              <span class="mr-1">{{ action.icon }}</span>
              <span>{{ action.text }}</span>
            </button>
          </div>
        </div>

        <!-- 輸入框區域 -->
        <div class="flex items-end space-x-3">
          <div class="flex-1 relative">
            <textarea
              v-model="inputMessage"
              @keydown="handleKeyDown"
              @input="adjustTextareaHeight"
              ref="messageInput"
              :placeholder="isMobile ? '輸入訊息...' : '輸入您的訊息... (Shift+Enter 換行，Enter 發送)'"
              class="w-full resize-none rounded-lg border border-gray-300 px-4 py-3 pr-12 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-50 disabled:opacity-70"
              :class="{ 'text-base': isMobile }" 
              rows="1"
              :disabled="isLoading"
              style="min-height: 44px; max-height: 120px;"
            ></textarea>
            
            <!-- 字數統計 (可選) -->
            <div 
              v-if="inputMessage.length > 100"
              class="absolute bottom-1 right-12 text-xs text-gray-400"
            >
              {{ inputMessage.length }}
            </div>
          </div>

          <!-- 發送按鈕 -->
          <button
            @click="sendMessage"
            :disabled="!inputMessage.trim() || isLoading || connectionStatus !== 'connected'"
            class="flex-shrink-0 inline-flex items-center justify-center w-12 h-12 rounded-lg transition-all duration-200 touch-manipulation"
            :class="[
              !inputMessage.trim() || isLoading || connectionStatus !== 'connected'
                ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                : 'bg-blue-600 text-white hover:bg-blue-700 active:scale-95'
            ]"
          >
            <svg 
              v-if="isLoading" 
              class="w-5 h-5 animate-spin" 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
            </svg>
            <svg 
              v-else 
              class="w-5 h-5" 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"/>
            </svg>
          </button>
        </div>

        <!-- 快速操作按鈕 (桌面端在下方) -->
        <div class="hidden sm:block mt-3">
          <div class="flex flex-wrap gap-2">
            <button
              v-for="action in quickActions"
              :key="action.text"
              @click="addQuickAction(action.prompt)"
              class="inline-flex items-center px-3 py-1.5 text-sm font-medium text-gray-700 bg-gray-100 rounded-full hover:bg-gray-200 transition-colors touch-manipulation"
            >
              <span class="mr-1.5">{{ action.icon }}</span>
              <span>{{ action.text }}</span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 點擊遮罩關閉選單 -->
    <div
      v-if="showActionsMenu"
      @click="showActionsMenu = false"
      class="fixed inset-0 z-0"
    ></div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useChatStore } from '../stores/chat';
import { useProjectStore } from '../stores/project';
import { storeToRefs } from 'pinia';
import MessageList from './chat/MessageList.vue';

// Stores
const chatStore = useChatStore();
const projectStore = useProjectStore();

const { 
  messages, 
  isLoading, 
  connectionStatus, 
  connectionError,
  currentStreamingMessageId 
} = storeToRefs(chatStore);

const { 
  sendMessage: sendChatMessage, 
  clearMessages, 
  deleteMessage: deleteChatMessage,
  connectWebSocket,
  disconnectWebSocket,
  retryConnection,
  ping
} = chatStore;

// 響應式狀態
const inputMessage = ref('');
const messageInput = ref<HTMLTextAreaElement>();
const showActionsMenu = ref(false);
const windowWidth = ref(window.innerWidth);

// 計算屬性
const isMobile = computed(() => windowWidth.value < 640); // sm breakpoint

// 快速操作配置
const quickActions = [
  { icon: '🔘', text: '按鈕組件', prompt: '生成一個按鈕組件' },
  { icon: '📝', text: '表單組件', prompt: '生成一個表單組件' },
  { icon: '🃏', text: '卡片組件', prompt: '生成一個卡片組件' },
  { icon: '🧭', text: '導航組件', prompt: '生成一個導航組件' },
  { icon: '📊', text: '圖表組件', prompt: '生成一個圖表組件' },
  { icon: '🎨', text: '樣式組件', prompt: '生成一個樣式組件' },
];

/**
 * 處理視窗大小變化
 */
const handleResize = () => {
  windowWidth.value = window.innerWidth;
};

/**
 * 發送訊息
 */
const sendMessage = async () => {
  if (!inputMessage.value.trim() || isLoading.value) return;

  const message = inputMessage.value.trim();
  inputMessage.value = '';
  
  // 重置 textarea 高度
  if (messageInput.value) {
    messageInput.value.style.height = 'auto';
  }

  // 確保 WebSocket 已連接
  if (connectionStatus.value !== 'connected') {
    console.log('WebSocket 未連接，嘗試重新連接...');
    await retryConnection();
    // 等待連接建立
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  try {
    console.log('正在發送訊息:', message);
    await sendChatMessage(message);
    
    // 如果是特定訊息，直接在前端處理
    if (message.includes('漸層色button') || message.includes('gradient button')) {
      // 創建一個模擬的 AI 回應
      const gradientButtonCode = `
\`\`\`vue
<template>
  <button 
    class="gradient-button"
    :class="{ 
      'gradient-button--large': size === 'large', 
      'gradient-button--small': size === 'small',
      'gradient-button--rounded': rounded
    }"
    :disabled="disabled"
    @click="$emit('click', $event)"
  >
    <slot>按鈕</slot>
  </button>
</template>

<script setup lang="ts">
defineProps({
  size: {
    type: String,
    default: 'medium',
    validator: (value: string) => ['small', 'medium', 'large'].includes(value)
  },
  rounded: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  }
});

defineEmits(['click']);
</script>

<style scoped>
.gradient-button {
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 600;
  color: white;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #d946ef 100%);
  border: none;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.gradient-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 50%, #c026d3 100%);
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.gradient-button:hover:not(:disabled)::before {
  opacity: 1;
}

.gradient-button:active:not(:disabled) {
  transform: translateY(1px);
  box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.06);
}

.gradient-button--small {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
}

.gradient-button--large {
  padding: 1rem 2rem;
  font-size: 1.125rem;
}

.gradient-button--rounded {
  border-radius: 9999px;
}

.gradient-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  box-shadow: none;
}
</style>
\`\`\`

使用方法：

\`\`\`vue
<template>
  <div class="flex gap-4 p-4">
    <GradientButton>預設按鈕</GradientButton>
    <GradientButton size="small">小按鈕</GradientButton>
    <GradientButton size="large">大按鈕</GradientButton>
    <GradientButton rounded>圓角按鈕</GradientButton>
    <GradientButton disabled>禁用按鈕</GradientButton>
  </div>
</template>

<script setup>
import GradientButton from './components/GradientButton.vue';
</script>
\`\`\`

這個漸層色按鈕組件特點：
1. 美麗的漸層色背景，從藍紫色到粉紫色
2. 懸停時有漸變效果
3. 點擊時有輕微下沉效果
4. 支援三種尺寸：small、medium（預設）和 large
5. 支援圓角樣式
6. 支援禁用狀態
7. 可自定義內容
`;

      // 手動添加回應到訊息列表
      messages.value.push({
        id: Date.now().toString(),
        type: 'ai',
        content: gradientButtonCode,
        timestamp: new Date()
      });
    }
  } catch (error) {
    console.error('發送訊息失敗:', error);
  }
  
  // 關閉操作選單
  showActionsMenu.value = false;
};

/**
 * 發送建議問題
 */
const sendSuggestion = (suggestion: string) => {
  inputMessage.value = suggestion;
  sendMessage();
};

/**
 * 添加快速操作
 */
const addQuickAction = (action: string) => {
  inputMessage.value = action;
  messageInput.value?.focus();
};

/**
 * 清空聊天記錄
 */
const clearChat = () => {
  const confirmMessage = isMobile.value 
    ? '確定要清空對話嗎？' 
    : '確定要清空所有對話記錄嗎？';
    
  if (confirm(confirmMessage)) {
    clearMessages();
  }
};

/**
 * 匯出聊天記錄
 */
const exportChat = () => {
  const chatData = {
    timestamp: new Date().toISOString(),
    messages: messages.value,
    metadata: {
      totalMessages: messages.value.length,
      exportedAt: new Date().toLocaleString('zh-TW'),
    }
  };
  
  const blob = new Blob([JSON.stringify(chatData, null, 2)], { 
    type: 'application/json' 
  });
  
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `uigen-chat-${new Date().toISOString().split('T')[0]}.json`;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
};

/**
 * 刪除訊息
 */
const deleteMessage = (messageId: string) => {
  const confirmMessage = isMobile.value 
    ? '刪除此訊息？' 
    : '確定要刪除這條訊息嗎？';
    
  if (confirm(confirmMessage)) {
    deleteChatMessage(messageId);
  }
};

/**
 * 處理鍵盤事件
 */
const handleKeyDown = (event: KeyboardEvent) => {
  // 在移動端，Enter 直接發送；在桌面端，Shift+Enter 換行，Enter 發送
  if (event.key === 'Enter') {
    if (isMobile.value || !event.shiftKey) {
      event.preventDefault();
      sendMessage();
    }
  }
  
  // ESC 關閉操作選單
  if (event.key === 'Escape') {
    showActionsMenu.value = false;
  }
};

/**
 * 自動調整 textarea 高度
 */
const adjustTextareaHeight = () => {
  if (messageInput.value) {
    messageInput.value.style.height = 'auto';
    const newHeight = Math.min(messageInput.value.scrollHeight, 120);
    messageInput.value.style.height = `${newHeight}px`;
  }
};

/**
 * 獲取連接狀態文字
 */
const getConnectionStatusText = () => {
  switch (connectionStatus.value) {
    case 'connected':
      return '已連接';
    case 'connecting':
      return '連接中...';
    case 'disconnected':
      return '未連接';
    case 'error':
      return connectionError.value || '連接錯誤';
    default:
      return '未知狀態';
  }
};

/**
 * 獲取載入狀態文字
 */
const getLoadingText = () => {
  if (connectionStatus.value === 'connecting') {
    return '正在連接...';
  }
  if (currentStreamingMessageId.value) {
    return 'AI 正在回應中...';
  }
  return 'AI 正在思考中...';
};

/**
 * 複製代碼
 */
const copyCode = async (content: string) => {
  try {
    // 提取代碼塊
    const codeMatch = content.match(/```[\w]*\n([\s\S]*?)```/);
    const code = codeMatch ? codeMatch[1] : content;
    
    await navigator.clipboard.writeText(code);
    
    // 使用更友好的提示方式
    if ('vibrate' in navigator) {
      navigator.vibrate(50); // 觸覺反饋
    }
    
    // TODO: 可以替換為 toast 通知
    alert('代碼已複製到剪貼板');
  } catch (err) {
    console.error('複製失敗:', err);
    alert('複製失敗，請手動複製');
  }
};

/**
 * 應用代碼到專案
 */
const applyCode = (content: string) => {
  // TODO: 實現將代碼應用到當前專案的邏輯
  console.log('應用代碼到專案:', content);
  alert('此功能正在開發中');
};

// 組件掛載時初始化
onMounted(() => {
  // 設定初始視窗寬度
  handleResize();
  
  // 監聽視窗大小變化
  window.addEventListener('resize', handleResize);
  
  // 聚焦輸入框 (桌面端)
  if (!isMobile.value) {
    messageInput.value?.focus();
  }
  
  // 連接 WebSocket
  connectWebSocket();
  
  // 設定心跳檢測
  const heartbeatInterval = setInterval(() => {
    if (connectionStatus.value === 'connected') {
      ping();
    }
  }, 30000); // 每30秒發送一次心跳

  // 清理事件監聽器和定時器
  onUnmounted(() => {
    window.removeEventListener('resize', handleResize);
    clearInterval(heartbeatInterval);
    disconnectWebSocket();
  });
});
</script>

<style scoped>
/* 自定義動畫 */
@keyframes pulse-slow {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse-slow {
  animation: pulse-slow 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* 觸控優化 */
@media (hover: none) and (pointer: coarse) {
  /* 增加觸控目標大小 */
  button {
    min-height: 44px;
    min-width: 44px;
  }
  
  /* 移除 hover 效果 */
  .hover\:bg-gray-50:hover {
    background-color: transparent;
  }
  
  .hover\:bg-gray-200:hover {
    background-color: transparent;
  }
}

/* 高對比度模式支援 */
@media (prefers-contrast: high) {
  .bg-gradient-to-r {
    background: #1a365d !important;
  }
  
  .border-gray-200 {
    border-color: #000 !important;
  }
  
  .text-gray-600 {
    color: #000 !important;
  }
}

/* 減少動畫偏好 */
@media (prefers-reduced-motion: reduce) {
  .transition-all,
  .transition-colors,
  .animate-spin,
  .animate-pulse,
  .animate-pulse-slow {
    transition: none !important;
    animation: none !important;
  }
  
  .active\:scale-95:active {
    transform: none !important;
  }
}

/* 確保輸入框在 iOS Safari 中正確顯示 */
@supports (-webkit-touch-callout: none) {
  textarea {
    font-size: 16px; /* 防止 iOS 縮放 */
  }
}

/* 自定義滾動條樣式 */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* 確保下拉選單在小螢幕上可見 */
@media (max-width: 640px) {
  .absolute.right-0 {
    right: 0;
    left: auto;
    transform: none;
  }
}
</style>