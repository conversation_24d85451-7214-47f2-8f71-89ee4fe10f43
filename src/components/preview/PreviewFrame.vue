<template>
  <div class="preview-frame-container">
    <!-- Preview Header -->
    <div class="preview-header">
      <div class="header-left">
        <h3 class="preview-title">組件預覽</h3>
        <div class="compile-status" :class="`status-${compileStatus}`">
          <span class="status-indicator"></span>
          <span class="status-text">{{ getStatusText() }}</span>
        </div>
      </div>
      <div class="header-right">
        <button 
          @click="refreshPreview" 
          :disabled="isCompiling"
          class="refresh-btn"
          title="重新整理預覽"
        >
          <span v-if="isCompiling">編譯中...</span>
          <span v-else>🔄 重新整理</span>
        </button>
        <button 
          @click="toggleFullscreen" 
          class="fullscreen-btn"
          title="全螢幕預覽"
        >
          📺
        </button>
      </div>
    </div>

    <!-- Loading Indicator -->
    <div v-if="isCompiling" class="loading-overlay">
      <div class="loading-spinner"></div>
      <p class="loading-text">正在編譯組件...</p>
    </div>

    <!-- Error Display -->
    <div v-else-if="compileError" class="error-display">
      <div class="error-header">
        <span class="error-icon">❌</span>
        <h4>編譯錯誤</h4>
      </div>
      <div class="error-content">
        <pre class="error-message">{{ compileError.message }}</pre>
        <div v-if="compileError.line" class="error-location">
          位置: 第 {{ compileError.line }} 行<span v-if="compileError.column">，第 {{ compileError.column }} 列</span>
        </div>
        <div v-if="compileError.file" class="error-file">
          檔案: {{ compileError.file }}
        </div>
      </div>
      <div class="error-actions">
        <button @click="clearError" class="clear-error-btn">
          清除錯誤
        </button>
        <button @click="refreshPreview" class="retry-btn">
          重試編譯
        </button>
      </div>
    </div>

    <!-- Runtime Error Display -->
    <div v-else-if="runtimeError" class="runtime-error-display">
      <div class="error-header">
        <span class="error-icon">⚠️</span>
        <h4>執行時錯誤</h4>
      </div>
      <div class="error-content">
        <pre class="error-message">{{ runtimeError.message }}</pre>
        <div v-if="runtimeError.stack" class="error-stack">
          <details>
            <summary>錯誤堆疊</summary>
            <pre>{{ runtimeError.stack }}</pre>
          </details>
        </div>
      </div>
      <div class="error-actions">
        <button @click="clearRuntimeError" class="clear-error-btn">
          清除錯誤
        </button>
      </div>
    </div>

    <!-- Preview Frame -->
    <div 
      v-else 
      class="preview-content"
      :class="{ 'fullscreen': isFullscreen }"
    >
      <iframe
        ref="previewIframe"
        class="preview-iframe"
        :srcdoc="previewHtml"
        sandbox="allow-scripts allow-same-origin allow-modals"
        @load="onIframeLoad"
      ></iframe>
    </div>

    <!-- Fullscreen Overlay -->
    <div v-if="isFullscreen" class="fullscreen-overlay" @click="toggleFullscreen">
      <div class="fullscreen-content" @click.stop>
        <div class="fullscreen-header">
          <h3>全螢幕預覽</h3>
          <button @click="toggleFullscreen" class="close-fullscreen-btn">
            ✕
          </button>
        </div>
        <iframe
          class="fullscreen-iframe"
          :srcdoc="previewHtml"
          sandbox="allow-scripts allow-same-origin allow-modals"
        ></iframe>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, onUnmounted, nextTick } from 'vue'
import { compileVueComponent, type CompileResult } from '../../lib/vue-compiler'

// Props
interface Props {
  componentCode: string
  dependencies?: string[]
  autoRefresh?: boolean
  refreshDelay?: number
}

const props = withDefaults(defineProps<Props>(), {
  dependencies: () => [],
  autoRefresh: true,
  refreshDelay: 1000
})

// Emits
interface CompileError {
  message: string
  line?: number
  column?: number
  file?: string
}

interface RuntimeError {
  message: string
  stack?: string
  componentStack?: string
}

interface Emits {
  'compile-success': []
  'compile-error': [error: CompileError]
  'runtime-error': [error: RuntimeError]
  'preview-ready': []
}

const emit = defineEmits<Emits>()

// State
const isCompiling = ref(false)
const compileStatus = ref<'idle' | 'compiling' | 'success' | 'error'>('idle')
const compileError = ref<CompileError | null>(null)
const runtimeError = ref<RuntimeError | null>(null)
const previewHtml = ref('')
const isFullscreen = ref(false)
const previewIframe = ref<HTMLIFrameElement>()

// Auto-refresh functionality
let refreshTimeout: NodeJS.Timeout | null = null

// Compile and preview the Vue component
const compileAndPreview = async () => {
  if (!props.componentCode.trim()) {
    previewHtml.value = getEmptyPreviewHtml()
    compileStatus.value = 'idle'
    return
  }

  isCompiling.value = true
  compileStatus.value = 'compiling'
  compileError.value = null
  runtimeError.value = null

  try {
    const result = await compileVueComponent(props.componentCode, 'PreviewComponent.vue')
    
    if (result.errors.length > 0) {
      const error = result.errors[0]
      compileError.value = {
        message: error.message,
        line: error.line,
        column: error.column,
        file: error.file
      }
      compileStatus.value = 'error'
      emit('compile-error', compileError.value)
      return
    }

    // Generate preview HTML
    previewHtml.value = generatePreviewHtml(result)
    compileStatus.value = 'success'
    emit('compile-success')

  } catch (error) {
    const compileErr: CompileError = {
      message: error instanceof Error ? error.message : '未知編譯錯誤'
    }
    compileError.value = compileErr
    compileStatus.value = 'error'
    emit('compile-error', compileErr)
  } finally {
    isCompiling.value = false
  }
}

// Generate HTML for preview
const generatePreviewHtml = (compileResult: CompileResult): string => {
  const { script, template, styles } = compileResult

  const stylesContent = styles.join('\n')
  
  return `<!DOCTYPE html>
<html lang="zh-TW">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Vue Component Preview</title>
  <script src="https://unpkg.com/vue@3/dist/vue.global.js"><\/script>
  <script src="https://cdn.tailwindcss.com"><\/script>
  <style>
    body {
      margin: 0;
      padding: 16px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: #f8f9fa;
    }
    .preview-container {
      background: white;
      border-radius: 8px;
      padding: 24px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    .error-boundary {
      color: #dc3545;
      background: #f8d7da;
      border: 1px solid #f5c6cb;
      border-radius: 4px;
      padding: 12px;
      margin: 16px 0;
    }
    ${stylesContent}
  <\/style>
<\/head>
<body>
  <div id="app">
    <div class="preview-container">
      <preview-component><\/preview-component>
    <\/div>
  <\/div>

  <script>
    const { createApp, ref, reactive, computed, watch, onMounted, onUnmounted } = Vue;
    
    // Error handling
    window.addEventListener('error', (event) => {
      window.parent.postMessage({
        type: 'runtime-error',
        error: {
          message: event.message,
          stack: event.error?.stack,
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno
        }
      }, '*');
    });

    window.addEventListener('unhandledrejection', (event) => {
      window.parent.postMessage({
        type: 'runtime-error',
        error: {
          message: event.reason?.message || 'Unhandled Promise Rejection',
          stack: event.reason?.stack
        }
      }, '*');
    });

    try {
      // Component definition
      ${script}

      // Create and mount app
      const app = createApp({
        components: {
          'preview-component': PreviewComponent || {}
        }
      });

      app.config.errorHandler = (err, instance, info) => {
        window.parent.postMessage({
          type: 'runtime-error',
          error: {
            message: err.message,
            stack: err.stack,
            componentStack: info
          }
        }, '*');
      };

      app.mount('#app');

      // Notify parent that preview is ready
      window.parent.postMessage({ type: 'preview-ready' }, '*');

    } catch (error) {
      document.getElementById('app').innerHTML = '<div class="error-boundary"><h3>組件載入錯誤<\/h3><p>' + error.message + '<\/p><details><summary>錯誤詳情<\/summary><pre>' + error.stack + '<\/pre><\/details><\/div>';
      
      window.parent.postMessage({
        type: 'runtime-error',
        error: {
          message: error.message,
          stack: error.stack
        }
      }, '*');
    }
  <\/script>
<\/body>
<\/html>`.trim()
}

// Generate empty preview HTML
const getEmptyPreviewHtml = (): string => {
  return `<!DOCTYPE html>
<html lang="zh-TW">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Vue Component Preview</title>
  <script src="https://cdn.tailwindcss.com"><\/script>
  <style>
    body {
      margin: 0;
      padding: 16px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: #f8f9fa;
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: calc(100vh - 32px);
    }
    .empty-state {
      text-align: center;
      color: #6c757d;
    }
  <\/style>
<\/head>
<body>
  <div class="empty-state">
    <h3>等待組件程式碼...<\/h3>
    <p>請在編輯器中輸入 Vue 組件程式碼以查看預覽<\/p>
  <\/div>
<\/body>
<\/html>`.trim()
}

// Handle iframe messages
const handleIframeMessage = (event: MessageEvent) => {
  if (event.data?.type === 'runtime-error') {
    runtimeError.value = event.data.error
    emit('runtime-error', event.data.error)
  } else if (event.data?.type === 'preview-ready') {
    emit('preview-ready')
  }
}

// Refresh preview
const refreshPreview = () => {
  compileAndPreview()
}

// Schedule auto-refresh
const scheduleRefresh = () => {
  if (!props.autoRefresh) return

  if (refreshTimeout) {
    clearTimeout(refreshTimeout)
  }

  refreshTimeout = setTimeout(() => {
    compileAndPreview()
  }, props.refreshDelay)
}

// Clear compile error
const clearError = () => {
  compileError.value = null
  compileStatus.value = 'idle'
}

// Clear runtime error
const clearRuntimeError = () => {
  runtimeError.value = null
}

// Toggle fullscreen
const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value
}

// Handle iframe load
const onIframeLoad = () => {
  // Iframe loaded successfully
}

// Get status text
const getStatusText = (): string => {
  switch (compileStatus.value) {
    case 'idle':
      return '待機中'
    case 'compiling':
      return '編譯中...'
    case 'success':
      return '編譯成功'
    case 'error':
      return '編譯錯誤'
    default:
      return '未知狀態'
  }
}

// Watch for component code changes
watch(() => props.componentCode, () => {
  scheduleRefresh()
}, { immediate: true })

// Watch for dependencies changes
watch(() => props.dependencies, () => {
  scheduleRefresh()
}, { deep: true })

// Lifecycle
onMounted(() => {
  // Listen for iframe messages
  window.addEventListener('message', handleIframeMessage)
  
  // Initial compile
  nextTick(() => {
    compileAndPreview()
  })
})

onUnmounted(() => {
  // Clean up
  if (refreshTimeout) {
    clearTimeout(refreshTimeout)
  }
  window.removeEventListener('message', handleIframeMessage)
})

// Expose methods
defineExpose({
  refreshPreview,
  clearError,
  clearRuntimeError,
  toggleFullscreen,
  getStatus: () => compileStatus.value,
  hasError: () => !!compileError.value || !!runtimeError.value
})
</script>

<style scoped>
.preview-frame-container {
  @apply flex flex-col h-full bg-white border border-gray-200 rounded-lg overflow-hidden;
}

.preview-header {
  @apply flex items-center justify-between px-4 py-3 bg-gray-50 border-b border-gray-200;
}

.header-left {
  @apply flex items-center space-x-4;
}

.preview-title {
  @apply text-lg font-semibold text-gray-800 m-0;
}

.compile-status {
  @apply flex items-center space-x-2 text-sm;
}

.status-indicator {
  @apply w-2 h-2 rounded-full;
}

.status-idle .status-indicator {
  @apply bg-gray-400;
}

.status-compiling .status-indicator {
  @apply bg-yellow-400 animate-pulse;
}

.status-success .status-indicator {
  @apply bg-green-400;
}

.status-error .status-indicator {
  @apply bg-red-400;
}

.status-text {
  @apply text-gray-600 font-medium;
}

.header-right {
  @apply flex items-center space-x-2;
}

.refresh-btn,
.fullscreen-btn {
  @apply px-3 py-1 text-sm bg-blue-500 hover:bg-blue-600 text-white rounded transition-colors;
}

.refresh-btn:disabled {
  @apply bg-gray-400 cursor-not-allowed;
}

.loading-overlay {
  @apply flex flex-col items-center justify-center flex-1 bg-gray-50;
}

.loading-spinner {
  @apply w-8 h-8 border-4 border-blue-200 border-t-blue-500 rounded-full animate-spin mb-4;
}

.loading-text {
  @apply text-gray-600 text-sm;
}

.error-display,
.runtime-error-display {
  @apply flex-1 p-6 bg-red-50;
}

.error-header {
  @apply flex items-center space-x-2 mb-4;
}

.error-icon {
  @apply text-2xl;
}

.error-header h4 {
  @apply text-lg font-semibold text-red-800 m-0;
}

.error-content {
  @apply mb-4;
}

.error-message {
  @apply bg-red-100 border border-red-200 rounded p-3 text-red-800 text-sm font-mono whitespace-pre-wrap mb-3;
}

.error-location,
.error-file {
  @apply text-sm text-red-600 mb-1;
}

.error-stack {
  @apply mt-2;
}

.error-stack details {
  @apply bg-red-100 border border-red-200 rounded p-2;
}

.error-stack summary {
  @apply cursor-pointer text-red-700 font-medium;
}

.error-stack pre {
  @apply mt-2 text-xs text-red-600 font-mono whitespace-pre-wrap;
}

.error-actions {
  @apply flex space-x-2;
}

.clear-error-btn,
.retry-btn {
  @apply px-4 py-2 text-sm bg-red-500 hover:bg-red-600 text-white rounded transition-colors;
}

.preview-content {
  @apply flex-1 relative;
}

.preview-iframe {
  @apply w-full h-full border-0;
}

.fullscreen-overlay {
  @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50;
}

.fullscreen-content {
  @apply bg-white rounded-lg shadow-2xl w-11/12 h-5/6 flex flex-col;
}

.fullscreen-header {
  @apply flex items-center justify-between px-6 py-4 border-b border-gray-200;
}

.fullscreen-header h3 {
  @apply text-lg font-semibold text-gray-800 m-0;
}

.close-fullscreen-btn {
  @apply w-8 h-8 flex items-center justify-center bg-gray-100 hover:bg-gray-200 rounded-full text-gray-600 transition-colors;
}

.fullscreen-iframe {
  @apply flex-1 border-0 rounded-b-lg;
}

/* Responsive design */
@media (max-width: 768px) {
  .preview-header {
    @apply px-3 py-2;
  }
  
  .preview-title {
    @apply text-base;
  }
  
  .header-right {
    @apply space-x-1;
  }
  
  .refresh-btn,
  .fullscreen-btn {
    @apply px-2 py-1 text-xs;
  }
  
  .fullscreen-content {
    @apply w-full h-full rounded-none;
  }
  
  .fullscreen-header {
    @apply px-4 py-3;
  }
}
</style>