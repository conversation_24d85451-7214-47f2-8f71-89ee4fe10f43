<template>
  <button 
    class="gradient-button"
    :class="{ 
      'gradient-button--large': size === 'large', 
      'gradient-button--small': size === 'small',
      'gradient-button--rounded': rounded
    }"
    :disabled="disabled"
    @click="$emit('click', $event)"
  >
    <slot>按鈕</slot>
  </button>
</template>

<script setup lang="ts">
defineProps({
  size: {
    type: String,
    default: 'medium',
    validator: (value: string) => ['small', 'medium', 'large'].includes(value)
  },
  rounded: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  }
});

defineEmits(['click']);
</script>

<style scoped>
.gradient-button {
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 600;
  color: white;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #d946ef 100%);
  border: none;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.gradient-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 50%, #c026d3 100%);
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.gradient-button:hover:not(:disabled)::before {
  opacity: 1;
}

.gradient-button:active:not(:disabled) {
  transform: translateY(1px);
  box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.06);
}

.gradient-button--small {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
}

.gradient-button--large {
  padding: 1rem 2rem;
  font-size: 1.125rem;
}

.gradient-button--rounded {
  border-radius: 9999px;
}

.gradient-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  box-shadow: none;
}
</style>