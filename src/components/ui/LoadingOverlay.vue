<!-- /src/components/ui/LoadingOverlay.vue -->
<template>
  <Teleport to="body">
    <Transition
      name="loading-overlay"
      appear
    >
      <div
        v-if="isLoading && primaryLoadingState"
        class="fixed inset-0 z-40 flex items-center justify-center bg-black bg-opacity-50"
        role="dialog"
        aria-modal="true"
        aria-labelledby="loading-title"
        aria-describedby="loading-description"
      >
        <div class="bg-white rounded-lg shadow-xl p-6 max-w-sm mx-4 min-w-[300px]">
          <!-- 載入動畫 -->
          <div class="flex items-center justify-center mb-4">
            <div class="relative">
              <!-- 外圈 -->
              <div class="w-12 h-12 border-4 border-gray-200 rounded-full"></div>
              <!-- 內圈（旋轉） -->
              <div class="absolute top-0 left-0 w-12 h-12 border-4 border-indigo-600 border-t-transparent rounded-full animate-spin"></div>
            </div>
          </div>

          <!-- 載入訊息 -->
          <div class="text-center">
            <h3
              id="loading-title"
              class="text-lg font-medium text-gray-900 mb-2"
            >
              {{ primaryLoadingState.message }}
            </h3>

            <!-- 進度條 -->
            <div
              v-if="primaryLoadingState.progress !== undefined"
              class="mb-4"
            >
              <div class="flex justify-between text-sm text-gray-600 mb-1">
                <span>進度</span>
                <span>{{ Math.round(primaryLoadingState.progress) }}%</span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2">
                <div
                  class="bg-indigo-600 h-2 rounded-full transition-all duration-300 ease-out"
                  :style="{ width: `${primaryLoadingState.progress}%` }"
                />
              </div>
            </div>

            <!-- 詳細狀態訊息 -->
            <p
              id="loading-description"
              class="text-sm text-gray-500 mb-4"
            >
              請稍候，系統正在處理您的請求...
            </p>

            <!-- 取消按鈕 -->
            <button
              v-if="primaryLoadingState.canCancel && primaryLoadingState.onCancel"
              type="button"
              class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200"
              @click="handleCancel"
            >
              <XMarkIcon class="w-4 h-4 mr-2" />
              取消
            </button>
          </div>

          <!-- 多個載入狀態指示器 -->
          <div
            v-if="loadingStates.length > 1"
            class="mt-4 pt-4 border-t border-gray-200"
          >
            <p class="text-xs text-gray-500 text-center">
              {{ loadingStates.length }} 個任務進行中
            </p>
            <div class="flex justify-center mt-2 space-x-1">
              <div
                v-for="(_, index) in loadingStates"
                :key="index"
                :class="[
                  'w-2 h-2 rounded-full',
                  index === loadingStates.length - 1 ? 'bg-indigo-600' : 'bg-gray-300'
                ]"
              />
            </div>
          </div>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useUIStore } from '@/stores/ui';
import { XMarkIcon } from '@heroicons/vue/24/outline';

const uiStore = useUIStore();

// 響應式數據
const isLoading = computed(() => uiStore.isLoading);
const primaryLoadingState = computed(() => uiStore.primaryLoadingState);
const loadingStates = computed(() => uiStore.loadingStates);

// 方法
const handleCancel = () => {
  if (primaryLoadingState.value?.onCancel) {
    primaryLoadingState.value.onCancel();
  }
};
</script>

<style scoped>
/* 載入覆蓋層動畫 */
.loading-overlay-enter-active,
.loading-overlay-leave-active {
  transition: opacity 0.3s ease;
}

.loading-overlay-enter-from,
.loading-overlay-leave-to {
  opacity: 0;
}

/* 旋轉動畫 */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}
</style>