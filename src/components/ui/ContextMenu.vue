<template>
  <div 
    class="context-menu"
    :style="{ left: `${x}px`, top: `${y}px` }"
    @click.stop
  >
    <template v-for="item in items" :key="item.id">
      <div v-if="item.id === 'separator'" class="menu-separator"></div>
      <div 
        v-else
        class="menu-item"
        :class="{ 'danger': item.danger }"
        @click="handleSelect(item.id)"
      >
        <span v-if="item.icon" class="menu-icon">{{ item.icon }}</span>
        <span class="menu-label">{{ item.label }}</span>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted } from 'vue'
import type { ContextMenuItem } from '../../types/file-tree'

// Props
interface Props {
  x: number
  y: number
  items: ContextMenuItem[]
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  select: [itemId: string]
  close: []
}>()

/**
 * 處理選單項目選擇
 */
const handleSelect = (itemId: string) => {
  emit('select', itemId)
}

/**
 * 處理點擊外部關閉選單
 */
const handleClickOutside = (event: MouseEvent) => {
  emit('close')
}

/**
 * 處理 ESC 鍵關閉選單
 */
const handleKeyDown = (event: KeyboardEvent) => {
  if (event.key === 'Escape') {
    emit('close')
  }
}

onMounted(() => {
  // 延遲添加事件監聽器，避免立即觸發
  setTimeout(() => {
    document.addEventListener('click', handleClickOutside)
    document.addEventListener('keydown', handleKeyDown)
  }, 0)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
  document.removeEventListener('keydown', handleKeyDown)
})
</script>

<style scoped>
.context-menu {
  position: fixed;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  padding: 4px 0;
  min-width: 160px;
  z-index: 1000;
  font-size: 13px;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  cursor: pointer;
  transition: background-color 0.2s;
  color: #374151;
}

.menu-item:hover {
  background-color: #f3f4f6;
}

.menu-item.danger {
  color: #dc2626;
}

.menu-item.danger:hover {
  background-color: #fef2f2;
}

.menu-icon {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
  font-size: 12px;
}

.menu-label {
  flex: 1;
}

.menu-separator {
  height: 1px;
  background-color: #e5e7eb;
  margin: 4px 0;
}
</style>