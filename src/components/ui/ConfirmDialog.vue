<!-- /src/components/ui/ConfirmDialog.vue -->
<template>
  <Teleport to="body">
    <div
      v-if="confirmDialogs.length > 0"
      class="fixed inset-0 z-50 overflow-y-auto"
      aria-labelledby="modal-title"
      role="dialog"
      aria-modal="true"
    >
      <!-- 背景遮罩 -->
      <div
        class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0"
      >
        <Transition
          name="modal-backdrop"
          appear
        >
          <div
            v-if="currentDialog"
            class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
            @click="handleCancel"
          />
        </Transition>

        <!-- 這個元素用於在 SM 螢幕上居中對話框 -->
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

        <!-- 對話框內容 -->
        <Transition
          name="modal-content"
          appear
        >
          <div
            v-if="currentDialog"
            class="relative inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6"
          >
            <div class="sm:flex sm:items-start">
              <!-- 圖示 -->
              <div
                :class="[
                  'mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full sm:mx-0 sm:h-10 sm:w-10',
                  dialogIconBackgroundClasses[currentDialog.type || 'info']
                ]"
              >
                <component
                  :is="dialogIcons[currentDialog.type || 'info']"
                  :class="[
                    'h-6 w-6',
                    dialogIconClasses[currentDialog.type || 'info']
                  ]"
                  aria-hidden="true"
                />
              </div>

              <!-- 內容 -->
              <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left flex-1">
                <h3
                  id="modal-title"
                  class="text-lg leading-6 font-medium text-gray-900"
                >
                  {{ currentDialog.title }}
                </h3>
                <div class="mt-2">
                  <p class="text-sm text-gray-500">
                    {{ currentDialog.message }}
                  </p>
                </div>
              </div>
            </div>

            <!-- 按鈕區域 -->
            <div class="mt-5 sm:mt-4 sm:flex sm:flex-row-reverse">
              <button
                ref="confirmButton"
                type="button"
                :class="[
                  'w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 text-base font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 sm:ml-3 sm:w-auto sm:text-sm',
                  'transition-colors duration-200',
                  confirmButtonClasses[currentDialog.type || 'info'],
                  { 'opacity-50 cursor-not-allowed': isProcessing }
                ]"
                :disabled="isProcessing"
                @click="handleConfirm"
              >
                <svg
                  v-if="isProcessing"
                  class="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    class="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    stroke-width="4"
                  />
                  <path
                    class="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  />
                </svg>
                {{ isProcessing ? '處理中...' : currentDialog.confirmText }}
              </button>
              
              <button
                type="button"
                class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:w-auto sm:text-sm transition-colors duration-200"
                :disabled="isProcessing"
                @click="handleCancel"
              >
                {{ currentDialog.cancelText }}
              </button>
            </div>
          </div>
        </Transition>
      </div>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
import { computed, ref, nextTick, watch } from 'vue';
import { useUIStore } from '@/stores/ui';
import {
  ExclamationTriangleIcon,
  InformationCircleIcon,
  ExclamationCircleIcon,
} from '@heroicons/vue/24/outline';

const uiStore = useUIStore();

// 響應式數據
const confirmDialogs = computed(() => uiStore.confirmDialogs);
const currentDialog = computed(() => confirmDialogs.value[confirmDialogs.value.length - 1]);
const isProcessing = ref(false);
const confirmButton = ref<HTMLButtonElement>();

// 對話框類型對應的圖示
const dialogIcons = {
  info: InformationCircleIcon,
  warning: ExclamationTriangleIcon,
  danger: ExclamationCircleIcon,
};

// 對話框類型對應的圖示背景顏色
const dialogIconBackgroundClasses = {
  info: 'bg-blue-100',
  warning: 'bg-yellow-100',
  danger: 'bg-red-100',
};

// 對話框類型對應的圖示顏色
const dialogIconClasses = {
  info: 'text-blue-600',
  warning: 'text-yellow-600',
  danger: 'text-red-600',
};

// 確認按鈕樣式
const confirmButtonClasses = {
  info: 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500',
  warning: 'bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500',
  danger: 'bg-red-600 hover:bg-red-700 focus:ring-red-500',
};

// 監聽對話框變化，自動聚焦確認按鈕
watch(currentDialog, async (newDialog) => {
  if (newDialog) {
    await nextTick();
    confirmButton.value?.focus();
  }
});

// 方法
const handleConfirm = async () => {
  if (!currentDialog.value || isProcessing.value) return;

  const dialog = currentDialog.value;
  isProcessing.value = true;

  try {
    await dialog.onConfirm();
    uiStore.dismissConfirmDialog(dialog.id);
  } catch (error) {
    console.error('確認操作失敗:', error);
    // 可以在這裡顯示錯誤訊息
    uiStore.showError('操作失敗', error instanceof Error ? error.message : '未知錯誤');
  } finally {
    isProcessing.value = false;
  }
};

const handleCancel = () => {
  if (!currentDialog.value || isProcessing.value) return;

  const dialog = currentDialog.value;
  
  if (dialog.onCancel) {
    dialog.onCancel();
  }
  
  uiStore.dismissConfirmDialog(dialog.id);
};

// 鍵盤事件處理
const handleKeydown = (event: KeyboardEvent) => {
  if (!currentDialog.value) return;

  switch (event.key) {
    case 'Enter':
      event.preventDefault();
      handleConfirm();
      break;
    case 'Escape':
      event.preventDefault();
      handleCancel();
      break;
  }
};

// 監聽鍵盤事件
watch(currentDialog, (newDialog) => {
  if (newDialog) {
    document.addEventListener('keydown', handleKeydown);
  } else {
    document.removeEventListener('keydown', handleKeydown);
  }
});
</script>

<style scoped>
/* 背景遮罩動畫 */
.modal-backdrop-enter-active,
.modal-backdrop-leave-active {
  transition: opacity 0.3s ease;
}

.modal-backdrop-enter-from,
.modal-backdrop-leave-to {
  opacity: 0;
}

/* 對話框內容動畫 */
.modal-content-enter-active,
.modal-content-leave-active {
  transition: all 0.3s ease;
}

.modal-content-enter-from,
.modal-content-leave-to {
  opacity: 0;
  transform: translate(0, -20px) scale(0.95);
}
</style>