<!-- /src/components/ui/NotificationContainer.vue -->
<template>
  <Teleport to="body">
    <div
      v-if="notifications.length > 0"
      class="fixed top-4 right-4 z-50 space-y-2 max-w-sm"
      role="region"
      aria-label="通知訊息"
    >
      <TransitionGroup
        name="notification"
        tag="div"
        class="space-y-2"
      >
        <div
          v-for="notification in notifications"
          :key="notification.id"
          :class="[
            'relative rounded-lg shadow-lg border p-4 max-w-sm',
            'transform transition-all duration-300 ease-in-out',
            notificationClasses[notification.type]
          ]"
          role="alert"
          :aria-live="notification.type === 'error' ? 'assertive' : 'polite'"
        >
          <!-- 圖示 -->
          <div class="flex items-start">
            <div class="flex-shrink-0">
              <component
                :is="notificationIcons[notification.type]"
                :class="[
                  'h-5 w-5',
                  notificationIconClasses[notification.type]
                ]"
              />
            </div>

            <!-- 內容 -->
            <div class="ml-3 flex-1">
              <h4 :class="[
                'text-sm font-medium',
                notificationTextClasses[notification.type]
              ]">
                {{ notification.title }}
              </h4>
              
              <p
                v-if="notification.message"
                :class="[
                  'mt-1 text-sm',
                  notificationTextClasses[notification.type]
                ]"
              >
                {{ notification.message }}
              </p>

              <!-- 動作按鈕 -->
              <div
                v-if="notification.actions && notification.actions.length > 0"
                class="mt-3 flex space-x-2"
              >
                <button
                  v-for="action in notification.actions"
                  :key="action.label"
                  :class="[
                    'text-xs font-medium px-2 py-1 rounded',
                    'transition-colors duration-200',
                    actionButtonClasses[action.style || 'secondary']
                  ]"
                  @click="handleActionClick(notification.id, action)"
                >
                  {{ action.label }}
                </button>
              </div>
            </div>

            <!-- 關閉按鈕 -->
            <div class="ml-4 flex-shrink-0">
              <button
                :class="[
                  'rounded-md inline-flex text-gray-400 hover:text-gray-500',
                  'focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500',
                  'transition-colors duration-200'
                ]"
                @click="dismissNotification(notification.id)"
                :aria-label="`關閉 ${notification.title} 通知`"
              >
                <XMarkIcon class="h-5 w-5" />
              </button>
            </div>
          </div>

          <!-- 進度條（如果有 duration） -->
          <div
            v-if="notification.duration && notification.duration > 0"
            class="absolute bottom-0 left-0 h-1 bg-current opacity-20 rounded-b-lg"
            :style="{
              animation: `shrink ${notification.duration}ms linear forwards`
            }"
          />
        </div>
      </TransitionGroup>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useUIStore } from '@/stores/ui';
import {
  CheckCircleIcon,
  ExclamationCircleIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  XMarkIcon,
} from '@heroicons/vue/24/outline';
import type { NotificationAction } from '@/stores/ui';

const uiStore = useUIStore();

// 響應式數據
const notifications = computed(() => uiStore.notifications);

// 通知類型對應的樣式類別
const notificationClasses = {
  success: 'bg-green-50 border-green-200',
  error: 'bg-red-50 border-red-200',
  warning: 'bg-yellow-50 border-yellow-200',
  info: 'bg-blue-50 border-blue-200',
};

// 通知類型對應的圖示
const notificationIcons = {
  success: CheckCircleIcon,
  error: ExclamationCircleIcon,
  warning: ExclamationTriangleIcon,
  info: InformationCircleIcon,
};

// 通知類型對應的圖示顏色
const notificationIconClasses = {
  success: 'text-green-400',
  error: 'text-red-400',
  warning: 'text-yellow-400',
  info: 'text-blue-400',
};

// 通知類型對應的文字顏色
const notificationTextClasses = {
  success: 'text-green-800',
  error: 'text-red-800',
  warning: 'text-yellow-800',
  info: 'text-blue-800',
};

// 動作按鈕樣式
const actionButtonClasses = {
  primary: 'bg-indigo-600 text-white hover:bg-indigo-700',
  secondary: 'bg-gray-200 text-gray-800 hover:bg-gray-300',
  danger: 'bg-red-600 text-white hover:bg-red-700',
};

// 方法
const dismissNotification = (id: string) => {
  uiStore.dismissNotification(id);
};

const handleActionClick = (notificationId: string, action: NotificationAction) => {
  action.action();
  dismissNotification(notificationId);
};
</script>

<style scoped>
/* 通知動畫 */
.notification-enter-active,
.notification-leave-active {
  transition: all 0.3s ease;
}

.notification-enter-from {
  opacity: 0;
  transform: translateX(100%);
}

.notification-leave-to {
  opacity: 0;
  transform: translateX(100%);
}

.notification-move {
  transition: transform 0.3s ease;
}

/* 進度條動畫 */
@keyframes shrink {
  from {
    width: 100%;
  }
  to {
    width: 0%;
  }
}
</style>