<template>
  <div class="message-list" ref="messageListContainer">
    <!-- 空狀態 -->
    <div v-if="messages.length === 0" class="empty-state">
      <div class="empty-icon">💬</div>
      <h3>開始與 AI 對話</h3>
      <p>輸入您的問題或需求，AI 將協助您生成 Vue 組件</p>
      <div class="suggestion-chips">
        <button 
          v-for="suggestion in suggestions" 
          :key="suggestion"
          @click="$emit('suggestion-selected', suggestion)"
          class="suggestion-chip"
        >
          {{ suggestion }}
        </button>
      </div>
    </div>

    <!-- 訊息列表 -->
    <div 
      v-for="message in messages" 
      :key="message.id" 
      class="message-wrapper"
      :class="`message-${message.type}`"
    >
      <div class="message-bubble">
        <div class="message-header">
          <span class="message-role">
            {{ getRoleDisplayName(message.type) }}
          </span>
          <span class="message-time">
            {{ formatTime(message.timestamp) }}
          </span>
          <span 
            v-if="message.id === currentStreamingMessageId" 
            class="streaming-indicator"
            title="正在接收中..."
          >
            ⚡
          </span>
          <button 
            @click="$emit('message-delete', message.id)" 
            class="delete-message-btn"
            title="刪除訊息"
          >
            ×
          </button>
        </div>
        <div class="message-content">
          <div v-if="message.type === 'ai'" class="ai-message">
            <div v-html="formatAIMessage(message.content)"></div>
            <!-- 如果 AI 回應包含代碼，顯示複製按鈕 -->
            <div v-if="hasCodeBlock(message.content)" class="message-actions">
              <button 
                @click="$emit('code-copy', message.content)" 
                class="copy-code-btn"
              >
                複製代碼
              </button>
              <button 
                @click="$emit('code-apply', message.content)" 
                class="apply-code-btn"
              >
                應用到專案
              </button>
            </div>
          </div>
          <div v-else-if="message.type === 'system'" class="system-message">
            {{ message.content }}
          </div>
          <div v-else class="user-message">
            {{ message.content }}
          </div>
        </div>
      </div>
    </div>

    <!-- 載入指示器 -->
    <div v-if="isLoading" class="loading-message">
      <div class="loading-bubble">
        <div class="loading-dots">
          <span></span>
          <span></span>
          <span></span>
        </div>
        <span class="loading-text">{{ loadingText }}</span>
      </div>
    </div>

    <!-- 錯誤提示 -->
    <div v-if="errorMessage" class="error-message">
      <div class="error-bubble">
        <div class="error-icon">⚠️</div>
        <div class="error-content">
          <div class="error-title">發生錯誤</div>
          <div class="error-description">{{ errorMessage }}</div>
          <button @click="$emit('error-retry')" class="error-retry-btn">
            重試
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick, watch, onMounted } from 'vue';
import type { ChatMessage } from '../../stores/chat';

interface Props {
  messages: ChatMessage[];
  isLoading: boolean;
  currentStreamingMessageId: string;
  errorMessage?: string;
  loadingText?: string;
}

interface Emits {
  (e: 'suggestion-selected', suggestion: string): void;
  (e: 'message-delete', messageId: string): void;
  (e: 'code-copy', content: string): void;
  (e: 'code-apply', content: string): void;
  (e: 'error-retry'): void;
}

const props = withDefaults(defineProps<Props>(), {
  loadingText: 'AI 正在思考中...',
  errorMessage: ''
});

const emit = defineEmits<Emits>();

// 本地狀態
const messageListContainer = ref<HTMLElement>();

// 建議問題
const suggestions = [
  '生成一個響應式的導航欄組件',
  '創建一個用戶登入表單',
  '設計一個產品卡片列表',
  '製作一個側邊欄選單',
];

/**
 * 獲取角色顯示名稱
 */
const getRoleDisplayName = (type: string) => {
  switch (type) {
    case 'user':
      return '您';
    case 'ai':
      return 'AI 助手';
    case 'system':
      return '系統';
    default:
      return type;
  }
};

/**
 * 格式化時間
 */
const formatTime = (timestamp: Date) => {
  return new Date(timestamp).toLocaleTimeString('zh-TW', {
    hour: '2-digit',
    minute: '2-digit',
  });
};

/**
 * 格式化 AI 訊息（支援 Markdown）
 */
const formatAIMessage = (content: string) => {
  // 簡單的 Markdown 轉換
  return content
    .replace(/```(\w+)?\n([\s\S]*?)```/g, '<pre><code class="language-$1">$2</code></pre>')
    .replace(/`([^`]+)`/g, '<code>$1</code>')
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    .replace(/\n/g, '<br>');
};

/**
 * 檢查是否包含代碼塊
 */
const hasCodeBlock = (content: string) => {
  return content.includes('```') || content.includes('<template>') || content.includes('<script>');
};

/**
 * 滾動到底部
 */
const scrollToBottom = () => {
  nextTick(() => {
    if (messageListContainer.value) {
      messageListContainer.value.scrollTop = messageListContainer.value.scrollHeight;
    }
  });
};

// 監聽訊息變化，自動滾動到底部
watch(() => props.messages, () => {
  scrollToBottom();
}, { deep: true });

// 監聽載入狀態變化
watch(() => props.isLoading, (newValue) => {
  if (newValue) {
    scrollToBottom();
  }
});

// 組件掛載時滾動到底部
onMounted(() => {
  scrollToBottom();
});
</script>

<style scoped>
.message-list {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  scroll-behavior: smooth;
}

/* 空狀態 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  color: #6c757d;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.empty-state h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
  color: #495057;
}

.empty-state p {
  margin: 0 0 2rem 0;
  font-size: 1rem;
}

.suggestion-chips {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  justify-content: center;
}

.suggestion-chip {
  padding: 0.5rem 1rem;
  border: 1px solid #dee2e6;
  border-radius: 20px;
  background: white;
  color: #495057;
  cursor: pointer;
  font-size: 0.875rem;
  transition: all 0.2s;
}

.suggestion-chip:hover {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

/* 訊息樣式 */
.message-wrapper {
  margin-bottom: 1rem;
}

.message-wrapper.message-user {
  display: flex;
  justify-content: flex-end;
}

.message-wrapper.message-ai,
.message-wrapper.message-system {
  display: flex;
  justify-content: flex-start;
}

.message-bubble {
  max-width: 70%;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  overflow: hidden;
}

.message-user .message-bubble {
  background: #007bff;
  color: white;
}

.message-system .message-bubble {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  color: #6c757d;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem 0.5rem;
  font-size: 0.875rem;
  border-bottom: 1px solid rgba(0,0,0,0.1);
}

.message-user .message-header {
  border-bottom-color: rgba(255,255,255,0.2);
}

.message-role {
  font-weight: 600;
}

.message-time {
  opacity: 0.7;
}

.delete-message-btn {
  background: none;
  border: none;
  color: inherit;
  cursor: pointer;
  font-size: 1.25rem;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.delete-message-btn:hover {
  background: rgba(0,0,0,0.1);
}

.message-user .delete-message-btn:hover {
  background: rgba(255,255,255,0.2);
}

/* 串流指示器 */
.streaming-indicator {
  color: #007bff;
  font-size: 0.875rem;
  animation: blink 1s infinite;
}

.message-user .streaming-indicator {
  color: rgba(255,255,255,0.8);
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0.3;
  }
}

.message-content {
  padding: 0.5rem 1rem 1rem;
}

.ai-message {
  line-height: 1.6;
}

.ai-message :deep(pre) {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 6px;
  overflow-x: auto;
  margin: 0.5rem 0;
}

.ai-message :deep(code) {
  background: #f8f9fa;
  padding: 0.2rem 0.4rem;
  border-radius: 3px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.message-actions {
  display: flex;
  gap: 0.5rem;
  margin-top: 1rem;
  padding-top: 0.5rem;
  border-top: 1px solid #e9ecef;
}

.copy-code-btn,
.apply-code-btn {
  padding: 0.5rem 1rem;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  background: white;
  color: #495057;
  cursor: pointer;
  font-size: 0.875rem;
  transition: all 0.2s;
}

.copy-code-btn:hover {
  background: #f8f9fa;
}

.apply-code-btn {
  background: #28a745;
  color: white;
  border-color: #28a745;
}

.apply-code-btn:hover {
  background: #218838;
}

.system-message {
  font-style: italic;
  color: #6c757d;
  text-align: center;
  padding: 0.5rem;
}

/* 載入指示器 */
.loading-message {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 1rem;
}

.loading-bubble {
  background: white;
  border-radius: 12px;
  padding: 1rem;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.loading-dots {
  display: flex;
  gap: 0.25rem;
}

.loading-dots span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #007bff;
  animation: loading-bounce 1.4s ease-in-out infinite both;
}

.loading-dots span:nth-child(1) { animation-delay: -0.32s; }
.loading-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes loading-bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

.loading-text {
  color: #6c757d;
  font-size: 0.875rem;
}

/* 錯誤提示 */
.error-message {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 1rem;
}

.error-bubble {
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 12px;
  padding: 1rem;
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  max-width: 70%;
}

.error-icon {
  font-size: 1.25rem;
  flex-shrink: 0;
}

.error-content {
  flex: 1;
}

.error-title {
  font-weight: 600;
  color: #721c24;
  margin-bottom: 0.25rem;
}

.error-description {
  color: #721c24;
  font-size: 0.875rem;
  margin-bottom: 0.75rem;
}

.error-retry-btn {
  padding: 0.375rem 0.75rem;
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background-color 0.2s;
}

.error-retry-btn:hover {
  background: #c82333;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .message-bubble {
    max-width: 85%;
  }
  
  .suggestion-chips {
    flex-direction: column;
    align-items: center;
  }
  
  .error-bubble {
    max-width: 90%;
  }
}
</style>