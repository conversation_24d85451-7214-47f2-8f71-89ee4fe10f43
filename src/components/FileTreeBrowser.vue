<template>
  <div class="file-tree-browser">
    <!-- 檔案樹標題欄 -->
    <div class="file-tree-header">
      <h3 class="tree-title">檔案瀏覽器</h3>
      <div class="tree-actions">
        <button @click="refreshTree" class="refresh-btn" :disabled="isLoading" title="重新整理">
          🔄
        </button>
        <button @click="showCreateFile = true" class="create-file-btn" title="新增檔案">
          📄
        </button>
        <button @click="showCreateFolder = true" class="create-folder-btn" title="新增資料夾">
          📁
        </button>
      </div>
    </div>

    <!-- 檔案樹內容 -->
    <div class="file-tree-content">
      <div v-if="isLoading" class="loading-state">
        <div class="loading-spinner"></div>
        <span>載入中...</span>
      </div>

      <div v-else-if="!currentProject" class="empty-state">
        <div class="empty-icon">📂</div>
        <p>請先選擇一個專案</p>
      </div>

      <div v-else-if="treeData.length === 0" class="empty-state">
        <div class="empty-icon">📄</div>
        <p>專案中還沒有檔案</p>
        <button @click="showCreateFile = true" class="create-first-file-btn">
          建立第一個檔案
        </button>
      </div>

      <div v-else class="file-tree">
        <FileTreeNode
          v-for="node in treeData"
          :key="node.id"
          :node="node"
          :level="0"
          @select="selectFile"
          @rename="renameFile"
          @delete="deleteFile"
          @create-file="createFileInFolder"
          @create-folder="createFolderInFolder"
        />
      </div>
    </div>

    <!-- 建立檔案對話框 -->
    <div v-if="showCreateFile" class="modal-overlay" @click="showCreateFile = false">
      <div class="modal-content" @click.stop>
        <h3>建立新檔案</h3>
        <form @submit.prevent="createFile">
          <div class="form-group">
            <label for="fileName">檔案名稱</label>
            <input
              id="fileName"
              v-model="newFileName"
              type="text"
              required
              placeholder="例如: Component.vue"
              @input="validateFileName"
            />
            <div v-if="fileNameError" class="error-message">{{ fileNameError }}</div>
          </div>
          <div class="form-group">
            <label for="fileTemplate">檔案模板</label>
            <select id="fileTemplate" v-model="selectedTemplate">
              <option value="">空白檔案</option>
              <option value="vue-component">Vue 組件</option>
              <option value="vue-page">Vue 頁面</option>
              <option value="typescript">TypeScript 檔案</option>
              <option value="javascript">JavaScript 檔案</option>
              <option value="css">CSS 檔案</option>
              <option value="scss">SCSS 檔案</option>
              <option value="json">JSON 檔案</option>
            </select>
          </div>
          <div class="form-actions">
            <button type="button" @click="showCreateFile = false">取消</button>
            <button type="submit" :disabled="!newFileName.trim() || !!fileNameError || isCreating">
              {{ isCreating ? '建立中...' : '建立' }}
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- 建立資料夾對話框 -->
    <div v-if="showCreateFolder" class="modal-overlay" @click="showCreateFolder = false">
      <div class="modal-content" @click.stop>
        <h3>建立新資料夾</h3>
        <form @submit.prevent="createFolder">
          <div class="form-group">
            <label for="folderName">資料夾名稱</label>
            <input
              id="folderName"
              v-model="newFolderName"
              type="text"
              required
              placeholder="例如: components"
              @input="validateFolderName"
            />
            <div v-if="folderNameError" class="error-message">{{ folderNameError }}</div>
          </div>
          <div class="form-actions">
            <button type="button" @click="showCreateFolder = false">取消</button>
            <button type="submit" :disabled="!newFolderName.trim() || !!folderNameError || isCreating">
              {{ isCreating ? '建立中...' : '建立' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { storeToRefs } from 'pinia';
import { useProjectStore } from '../stores/project';
import { useFileSystemStore } from '../stores/file-system';
import FileTreeNode from './FileTreeNode.vue';

// Stores
const projectStore = useProjectStore();
const fileSystemStore = useFileSystemStore();

const { currentProject } = storeToRefs(projectStore);
const { files, isLoading } = storeToRefs(fileSystemStore);

// 本地狀態
const showCreateFile = ref(false);
const showCreateFolder = ref(false);
const newFileName = ref('');
const newFolderName = ref('');
const selectedTemplate = ref('');
const fileNameError = ref('');
const folderNameError = ref('');
const isCreating = ref(false);

// 檔案樹節點介面
interface TreeNode {
  id: string;
  name: string;
  type: 'file' | 'folder';
  path: string;
  children?: TreeNode[];
  isExpanded?: boolean;
  size?: number;
  lastModified?: Date;
}

// 計算檔案樹資料
const treeData = computed(() => {
  if (!currentProject.value || !files.value.length) return [];
  
  return buildFileTree(files.value);
});

/**
 * 建構檔案樹結構
 */
const buildFileTree = (fileList: any[]): TreeNode[] => {
  const tree: TreeNode[] = [];
  const pathMap = new Map<string, TreeNode>();

  // 先創建所有節點
  fileList.forEach(file => {
    const pathParts = file.name.split('/');
    let currentPath = '';

    pathParts.forEach((part, index) => {
      const parentPath = currentPath;
      currentPath = currentPath ? `${currentPath}/${part}` : part;
      
      if (!pathMap.has(currentPath)) {
        const isFile = index === pathParts.length - 1;
        const node: TreeNode = {
          id: isFile ? file.id : currentPath,
          name: part,
          type: isFile ? 'file' : 'folder',
          path: currentPath,
          children: isFile ? undefined : [],
          isExpanded: false,
          size: isFile ? file.content?.length || 0 : undefined,
          lastModified: isFile ? new Date(file.updatedAt) : undefined,
        };

        pathMap.set(currentPath, node);

        // 添加到父節點或根節點
        if (parentPath) {
          const parent = pathMap.get(parentPath);
          if (parent && parent.children) {
            parent.children.push(node);
          }
        } else {
          tree.push(node);
        }
      }
    });
  });

  // 排序：資料夾在前，檔案在後，然後按名稱排序
  const sortNodes = (nodes: TreeNode[]) => {
    nodes.sort((a, b) => {
      if (a.type !== b.type) {
        return a.type === 'folder' ? -1 : 1;
      }
      return a.name.localeCompare(b.name);
    });

    nodes.forEach(node => {
      if (node.children) {
        sortNodes(node.children);
      }
    });
  };

  sortNodes(tree);
  return tree;
};

/**
 * 重新整理檔案樹
 */
const refreshTree = async () => {
  if (currentProject.value) {
    await fileSystemStore.loadProjectFiles(currentProject.value.id);
  }
};

/**
 * 選擇檔案
 */
const selectFile = (node: TreeNode) => {
  if (node.type === 'file') {
    // TODO: 打開檔案編輯器
    console.log('選擇檔案:', node);
  } else {
    // 切換資料夾展開狀態
    node.isExpanded = !node.isExpanded;
  }
};

/**
 * 重新命名檔案/資料夾
 */
const renameFile = async (node: TreeNode, newName: string) => {
  try {
    if (node.type === 'file') {
      // TODO: 實現檔案重新命名
      console.log('重新命名檔案:', node.name, '->', newName);
    } else {
      // TODO: 實現資料夾重新命名
      console.log('重新命名資料夾:', node.name, '->', newName);
    }
  } catch (error) {
    console.error('重新命名失敗:', error);
  }
};

/**
 * 刪除檔案/資料夾
 */
const deleteFile = async (node: TreeNode) => {
  if (!confirm(`確定要刪除 ${node.type === 'file' ? '檔案' : '資料夾'} "${node.name}" 嗎？`)) {
    return;
  }

  try {
    if (node.type === 'file') {
      await fileSystemStore.deleteFile(node.id);
    } else {
      // TODO: 實現資料夾刪除
      console.log('刪除資料夾:', node.name);
    }
    await refreshTree();
  } catch (error) {
    console.error('刪除失敗:', error);
  }
};

/**
 * 在資料夾中建立檔案
 */
const createFileInFolder = (folderNode: TreeNode) => {
  // TODO: 設定建立檔案的目標資料夾
  showCreateFile.value = true;
};

/**
 * 在資料夾中建立資料夾
 */
const createFolderInFolder = (folderNode: TreeNode) => {
  // TODO: 設定建立資料夾的目標資料夾
  showCreateFolder.value = true;
};

/**
 * 驗證檔案名稱
 */
const validateFileName = () => {
  const name = newFileName.value.trim();
  fileNameError.value = '';

  if (!name) {
    return;
  }

  // 檢查檔案名稱格式
  const invalidChars = /[<>:"/\\|?*]/;
  if (invalidChars.test(name)) {
    fileNameError.value = '檔案名稱包含無效字元';
    return;
  }

  // 檢查是否已存在
  const exists = files.value.some(file => file.name === name);
  if (exists) {
    fileNameError.value = '檔案名稱已存在';
    return;
  }
};

/**
 * 驗證資料夾名稱
 */
const validateFolderName = () => {
  const name = newFolderName.value.trim();
  folderNameError.value = '';

  if (!name) {
    return;
  }

  // 檢查資料夾名稱格式
  const invalidChars = /[<>:"/\\|?*]/;
  if (invalidChars.test(name)) {
    folderNameError.value = '資料夾名稱包含無效字元';
    return;
  }
};

/**
 * 建立檔案
 */
const createFile = async () => {
  if (!currentProject.value || fileNameError.value) return;

  isCreating.value = true;
  try {
    const content = getTemplateContent(selectedTemplate.value);
    
    await fileSystemStore.createFile({
      name: newFileName.value.trim(),
      content,
      projectId: currentProject.value.id,
    });

    // 重置表單
    newFileName.value = '';
    selectedTemplate.value = '';
    showCreateFile.value = false;
    
    await refreshTree();
  } catch (error) {
    console.error('建立檔案失敗:', error);
  } finally {
    isCreating.value = false;
  }
};

/**
 * 建立資料夾
 */
const createFolder = async () => {
  if (!currentProject.value || folderNameError.value) return;

  isCreating.value = true;
  try {
    // TODO: 實現資料夾建立邏輯
    console.log('建立資料夾:', newFolderName.value);
    
    // 重置表單
    newFolderName.value = '';
    showCreateFolder.value = false;
  } catch (error) {
    console.error('建立資料夾失敗:', error);
  } finally {
    isCreating.value = false;
  }
};

/**
 * 取得檔案模板內容
 */
const getTemplateContent = (template: string): string => {
  switch (template) {
    case 'vue-component':
      return `<template>
  <div class="component">
    <!-- 組件內容 -->
  </div>
</template>

<script setup lang="ts">
// 組件邏輯
<\/script>

<style scoped>
.component {
  /* 組件樣式 */
}
</style>`;

    case 'vue-page':
      return `<template>
  <div class="page">
    <h1>頁面標題</h1>
    <!-- 頁面內容 -->
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';

// 頁面邏輯
onMounted(() => {
  // 頁面載入時執行
});
<\/script>

<style scoped>
.page {
  padding: 2rem;
}
</style>`;

    case 'typescript':
      return `// TypeScript 檔案
export interface ExampleInterface {
  id: string;
  name: string;
}

export class ExampleClass {
  constructor(private data: ExampleInterface) {}
  
  public getName(): string {
    return this.data.name;
  }
}`;

    case 'javascript':
      return `// JavaScript 檔案
export function exampleFunction() {
  console.log('Hello, World!');
}

export const exampleConstant = 'example';`;

    case 'css':
      return `/* CSS 樣式檔案 */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem;
}`;

    case 'scss':
      return `// SCSS 樣式檔案
$primary-color: #007bff;
$secondary-color: #6c757d;

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem;
  
  .header {
    color: $primary-color;
    margin-bottom: 1rem;
  }
}`;

    case 'json':
      return `{
  "name": "example",
  "version": "1.0.0",
  "description": "範例 JSON 檔案"
}`;

    default:
      return '';
  }
};

// 監聽當前專案變化，自動重新整理檔案樹
watch(currentProject, (newProject) => {
  if (newProject) {
    refreshTree();
  }
}, { immediate: true });
</script>

<style scoped>
.file-tree-browser {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

/* 標題欄 */
.file-tree-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.tree-title {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: #495057;
}

.tree-actions {
  display: flex;
  gap: 0.5rem;
}

.refresh-btn,
.create-file-btn,
.create-folder-btn {
  width: 32px;
  height: 32px;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  background: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  transition: all 0.2s;
}

.refresh-btn:hover,
.create-file-btn:hover,
.create-folder-btn:hover {
  background: #e9ecef;
  border-color: #adb5bd;
}

.refresh-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 內容區域 */
.file-tree-content {
  flex: 1;
  overflow-y: auto;
  padding: 0.5rem;
}

/* 載入狀態 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  color: #6c757d;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e9ecef;
  border-top: 3px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 空狀態 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
  color: #6c757d;
}

.empty-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.empty-state p {
  margin: 0 0 1rem 0;
}

.create-first-file-btn {
  padding: 0.5rem 1rem;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background-color 0.2s;
}

.create-first-file-btn:hover {
  background: #0056b3;
}

/* 檔案樹 */
.file-tree {
  padding: 0.5rem 0;
}

/* 對話框樣式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  padding: 2rem;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  max-height: 80vh;
  overflow-y: auto;
}

.modal-content h3 {
  margin: 0 0 1.5rem 0;
  color: #495057;
}

.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #495057;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  font-size: 1rem;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.error-message {
  color: #dc3545;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 1.5rem;
}

.form-actions button {
  padding: 0.75rem 1.5rem;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.2s;
}

.form-actions button[type="button"] {
  background: white;
  color: #6c757d;
}

.form-actions button[type="submit"] {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

.form-actions button:hover {
  opacity: 0.9;
}

.form-actions button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .file-tree-header {
    padding: 0.75rem;
  }
  
  .tree-title {
    font-size: 0.875rem;
  }
  
  .tree-actions button {
    width: 28px;
    height: 28px;
    font-size: 0.75rem;
  }
  
  .modal-content {
    padding: 1.5rem;
    margin: 1rem;
  }
}
</style>