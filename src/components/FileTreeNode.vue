<template>
  <div class="file-tree-node">
    <div 
      class="node-content"
      :class="{ 'is-selected': isSelected }"
      :style="{ paddingLeft: `${level * 20 + 8}px` }"
      @click="handleClick"
      @contextmenu.prevent="showContextMenu"
    >
      <!-- 展開/收合圖示 -->
      <span 
        v-if="node.type === 'folder'" 
        class="expand-icon"
        @click.stop="toggleExpanded"
      >
        {{ node.isExpanded ? '📂' : '📁' }}
      </span>
      
      <!-- 檔案/資料夾圖示 -->
      <span class="node-icon">
        {{ node.type === 'file' ? getFileIcon(node.name) : (node.isExpanded ? '📂' : '📁') }}
      </span>
      
      <!-- 檔案/資料夾名稱 -->
      <span class="node-name">{{ node.name }}</span>
      
      <!-- 檔案大小 (僅檔案) -->
      <span v-if="node.type === 'file' && node.size !== undefined" class="file-size">
        {{ formatFileSize(node.size) }}
      </span>
    </div>
    
    <!-- 子節點 -->
    <div v-if="node.type === 'folder' && node.isExpanded && node.children" class="children">
      <FileTreeNode
        v-for="child in node.children"
        :key="child.id"
        :node="child"
        :level="level + 1"
        @select="$emit('select', $event)"
        @rename="$emit('rename', $event)"
        @delete="$emit('delete', $event)"
        @create-file="$emit('create-file', $event)"
        @create-folder="$emit('create-folder', $event)"
      />
    </div>
    
    <!-- 右鍵選單 -->
    <div 
      v-if="showMenu" 
      class="context-menu"
      :style="menuStyle"
      @click.stop
    >
      <div class="menu-item" @click="handleRename">
        ✏️ 重新命名
      </div>
      <div v-if="node.type === 'folder'" class="menu-item" @click="handleCreateFile">
        📄 新增檔案
      </div>
      <div v-if="node.type === 'folder'" class="menu-item" @click="handleCreateFolder">
        📁 新增資料夾
      </div>
      <div class="menu-separator"></div>
      <div class="menu-item danger" @click="handleDelete">
        🗑️ 刪除
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';

// Props
interface TreeNode {
  id: string;
  name: string;
  type: 'file' | 'folder';
  path: string;
  children?: TreeNode[];
  isExpanded?: boolean;
  size?: number;
  lastModified?: Date;
}

interface Props {
  node: TreeNode;
  level: number;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  select: [node: TreeNode];
  rename: [node: TreeNode, newName: string];
  delete: [node: TreeNode];
  'create-file': [folderNode: TreeNode];
  'create-folder': [folderNode: TreeNode];
}>();

// 本地狀態
const showMenu = ref(false);
const menuStyle = ref({});
const isSelected = ref(false);

/**
 * 處理節點點擊
 */
const handleClick = () => {
  emit('select', props.node);
  isSelected.value = true;
};

/**
 * 切換資料夾展開狀態
 */
const toggleExpanded = () => {
  if (props.node.type === 'folder') {
    props.node.isExpanded = !props.node.isExpanded;
  }
};

/**
 * 顯示右鍵選單
 */
const showContextMenu = (event: MouseEvent) => {
  showMenu.value = true;
  menuStyle.value = {
    position: 'fixed',
    left: `${event.clientX}px`,
    top: `${event.clientY}px`,
    zIndex: 1000,
  };
  
  // 點擊其他地方時隱藏選單
  const hideMenu = () => {
    showMenu.value = false;
    document.removeEventListener('click', hideMenu);
  };
  
  setTimeout(() => {
    document.addEventListener('click', hideMenu);
  }, 0);
};

/**
 * 處理重新命名
 */
const handleRename = () => {
  const newName = prompt('請輸入新名稱:', props.node.name);
  if (newName && newName.trim() && newName !== props.node.name) {
    emit('rename', props.node, newName.trim());
  }
  showMenu.value = false;
};

/**
 * 處理刪除
 */
const handleDelete = () => {
  emit('delete', props.node);
  showMenu.value = false;
};

/**
 * 處理在資料夾中建立檔案
 */
const handleCreateFile = () => {
  emit('create-file', props.node);
  showMenu.value = false;
};

/**
 * 處理在資料夾中建立資料夾
 */
const handleCreateFolder = () => {
  emit('create-folder', props.node);
  showMenu.value = false;
};

/**
 * 根據檔案副檔名取得圖示
 */
const getFileIcon = (fileName: string): string => {
  const ext = fileName.split('.').pop()?.toLowerCase();
  
  switch (ext) {
    case 'vue':
      return '🟢';
    case 'js':
    case 'mjs':
      return '🟨';
    case 'ts':
      return '🔷';
    case 'css':
    case 'scss':
    case 'sass':
      return '🎨';
    case 'html':
      return '🌐';
    case 'json':
      return '📋';
    case 'md':
      return '📝';
    case 'png':
    case 'jpg':
    case 'jpeg':
    case 'gif':
    case 'svg':
      return '🖼️';
    default:
      return '📄';
  }
};

/**
 * 格式化檔案大小
 */
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
};
</script>

<style scoped>
.file-tree-node {
  user-select: none;
}

.node-content {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.2s;
  min-height: 28px;
}

.node-content:hover {
  background-color: #f8f9fa;
}

.node-content.is-selected {
  background-color: #e3f2fd;
  color: #1976d2;
}

.expand-icon {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 4px;
  cursor: pointer;
  font-size: 12px;
}

.node-icon {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
  font-size: 12px;
}

.node-name {
  flex: 1;
  font-size: 0.875rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.file-size {
  font-size: 0.75rem;
  color: #6c757d;
  margin-left: 8px;
}

.children {
  /* 子節點容器 */
}

/* 右鍵選單 */
.context-menu {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 4px 0;
  min-width: 150px;
}

.menu-item {
  padding: 8px 12px;
  cursor: pointer;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: background-color 0.2s;
}

.menu-item:hover {
  background-color: #f8f9fa;
}

.menu-item.danger {
  color: #dc3545;
}

.menu-item.danger:hover {
  background-color: #f8d7da;
}

.menu-separator {
  height: 1px;
  background-color: #dee2e6;
  margin: 4px 0;
}
</style>