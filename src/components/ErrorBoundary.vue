<template>
  <div v-if="hasError" class="error-boundary">
    <div class="error-container">
      <div class="error-icon">⚠️</div>
      <h2 class="error-title">糟糕！發生了錯誤</h2>
      <p class="error-message">{{ errorMessage }}</p>
      <div class="error-actions">
        <button @click="retry" class="retry-button">重試</button>
        <button @click="goHome" class="home-button">回到首頁</button>
      </div>
      <details v-if="errorDetails" class="error-details">
        <summary>錯誤詳情</summary>
        <pre>{{ errorDetails }}</pre>
      </details>
    </div>
  </div>
  <slot v-else />
</template>

<script setup lang="ts">
import { ref, onErrorCaptured } from 'vue';
import { useRouter } from 'vue-router';

// Props
interface Props {
  fallbackMessage?: string;
}

const props = withDefaults(defineProps<Props>(), {
  fallbackMessage: '應用程式遇到了意外錯誤，請稍後再試。',
});

// 錯誤狀態
const hasError = ref(false);
const errorMessage = ref('');
const errorDetails = ref('');

// Router
const router = useRouter();

// 捕獲錯誤
onErrorCaptured((error: Error, instance, info) => {
  console.error('❌ ErrorBoundary 捕獲錯誤:', error);
  console.error('📍 錯誤位置:', info);
  
  hasError.value = true;
  errorMessage.value = error.message || props.fallbackMessage;
  errorDetails.value = `${error.stack}\n\n組件資訊: ${info}`;
  
  // 回報錯誤到監控系統（如果有的話）
  reportError(error, info);
  
  // 阻止錯誤繼續向上傳播
  return false;
});

/**
 * 重試操作
 */
const retry = () => {
  hasError.value = false;
  errorMessage.value = '';
  errorDetails.value = '';
  console.log('🔄 用戶選擇重試');
};

/**
 * 回到首頁
 */
const goHome = () => {
  hasError.value = false;
  errorMessage.value = '';
  errorDetails.value = '';
  router.push('/');
  console.log('🏠 用戶選擇回到首頁');
};

/**
 * 回報錯誤到監控系統
 */
const reportError = (error: Error, info: string) => {
  // 這裡可以整合錯誤監控服務，如 Sentry
  console.log('📊 回報錯誤到監控系統:', {
    message: error.message,
    stack: error.stack,
    componentInfo: info,
    timestamp: new Date().toISOString(),
    userAgent: navigator.userAgent,
    url: window.location.href,
  });
};
</script>

<style scoped>
.error-boundary {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 2rem;
  background-color: #f8f9fa;
}

.error-container {
  max-width: 600px;
  padding: 2rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.error-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.error-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #dc3545;
  margin-bottom: 1rem;
}

.error-message {
  color: #6c757d;
  margin-bottom: 2rem;
  line-height: 1.5;
}

.error-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-bottom: 2rem;
}

.retry-button,
.home-button {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.retry-button {
  background-color: #007bff;
  color: white;
}

.retry-button:hover {
  background-color: #0056b3;
}

.home-button {
  background-color: #6c757d;
  color: white;
}

.home-button:hover {
  background-color: #545b62;
}

.error-details {
  text-align: left;
  margin-top: 1rem;
}

.error-details summary {
  cursor: pointer;
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: #6c757d;
}

.error-details pre {
  background-color: #f8f9fa;
  padding: 1rem;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 0.875rem;
  color: #495057;
  white-space: pre-wrap;
  word-break: break-word;
}
</style>