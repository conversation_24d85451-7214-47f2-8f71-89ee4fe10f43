<template>
  <div class="chat-container">
    <div class="messages-container">
      <div v-for="message in messages" :key="message.id" class="message" :class="`message-${message.role}`">
        <div class="message-content">{{ message.content }}</div>
      </div>
    </div>
    <div class="input-container">
      <input v-model="newMessage" @keyup.enter="sendMessage" placeholder="輸入訊息..." />
      <button @click="sendMessage">發送</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useChatStore } from '../stores/chat';

const chatStore = useChatStore();
const messages = chatStore.messages;
const newMessage = ref('');

const sendMessage = () => {
  if (newMessage.value.trim() !== '') {
    chatStore.sendMessage(newMessage.value.trim());
    newMessage.value = '';
  }
};
</script>

<style scoped>
.chat-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.messages-container {
  flex-grow: 1;
  overflow-y: auto;
  padding: 1rem;
}

.message {
  margin-bottom: 1rem;
}

.message-user {
  text-align: right;
}

.message-content {
  display: inline-block;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
}

.message-user .message-content {
  background-color: #dcf8c6;
}

.message-assistant .message-content {
  background-color: #f1f0f0;
}

.input-container {
  display: flex;
  padding: 1rem;
  border-top: 1px solid #ccc;
}

.input-container input {
  flex-grow: 1;
  padding: 0.5rem;
  border: 1px solid #ccc;
  border-radius: 0.5rem;
}

.input-container button {
  margin-left: 1rem;
  padding: 0.5rem 1rem;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 0.5rem;
  cursor: pointer;
}
</style>
