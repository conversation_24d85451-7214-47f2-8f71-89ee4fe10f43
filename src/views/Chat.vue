<template>
  <div class="chat-container">
    <!-- 連接狀態指示器 -->
    <div class="connection-status" :class="`status-${chatStore.connectionStatus}`">
      <div class="status-indicator"></div>
      <span class="status-text">
        {{ getStatusText() }}
      </span>
      <button
        v-if="chatStore.connectionStatus === 'disconnected' || chatStore.connectionStatus === 'error'"
        @click="chatStore.connectWebSocket()"
        class="reconnect-btn"
      >
        重新連接
      </button>
    </div>

    <div class="messages-container">
      <div v-for="message in messages" :key="message.id" class="message" :class="`message-${message.type}`">
        <div class="message-content">{{ message.content }}</div>
      </div>

      <!-- 載入中指示器 -->
      <div v-if="chatStore.isLoading" class="message message-ai">
        <div class="message-content loading">
          <span class="loading-dots">正在思考中...</span>
        </div>
      </div>
    </div>

    <div class="input-container">
      <input
        v-model="newMessage"
        @keyup.enter="sendMessage"
        placeholder="輸入訊息..."
        :disabled="chatStore.connectionStatus !== 'connected'"
      />
      <button
        @click="sendMessage"
        :disabled="chatStore.connectionStatus !== 'connected' || !newMessage.trim()"
      >
        發送
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { useChatStore } from '../stores/chat';

const chatStore = useChatStore();
const messages = chatStore.messages;
const newMessage = ref('');

const sendMessage = () => {
  if (newMessage.value.trim() !== '') {
    chatStore.sendMessage(newMessage.value.trim());
    newMessage.value = '';
  }
};

const getStatusText = () => {
  switch (chatStore.connectionStatus) {
    case 'connected':
      return '已連接';
    case 'connecting':
      return '連接中...';
    case 'disconnected':
      return '未連接';
    case 'error':
      return '連接錯誤';
    default:
      return '未知狀態';
  }
};

// 組件掛載時連接 WebSocket
onMounted(() => {
  console.log('🔌 Chat 組件掛載，嘗試連接 WebSocket');
  chatStore.connectWebSocket();
});

// 組件卸載時斷開 WebSocket
onUnmounted(() => {
  console.log('🔌 Chat 組件卸載，斷開 WebSocket');
  chatStore.disconnectWebSocket();
});
</script>

<style scoped>
.chat-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* 連接狀態指示器 */
.connection-status {
  display: flex;
  align-items: center;
  padding: 0.5rem 1rem;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  font-size: 0.875rem;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 0.5rem;
}

.status-connected .status-indicator {
  background-color: #28a745;
}

.status-connecting .status-indicator {
  background-color: #ffc107;
  animation: pulse 1.5s infinite;
}

.status-disconnected .status-indicator {
  background-color: #6c757d;
}

.status-error .status-indicator {
  background-color: #dc3545;
}

.reconnect-btn {
  margin-left: auto;
  padding: 0.25rem 0.5rem;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 0.25rem;
  cursor: pointer;
  font-size: 0.75rem;
}

.reconnect-btn:hover {
  background-color: #0056b3;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.messages-container {
  flex-grow: 1;
  overflow-y: auto;
  padding: 1rem;
}

.message {
  margin-bottom: 1rem;
}

.message-user {
  text-align: right;
}

.message-ai {
  text-align: left;
}

.message-content {
  display: inline-block;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  max-width: 80%;
}

.message-user .message-content {
  background-color: #dcf8c6;
}

.message-ai .message-content {
  background-color: #f1f0f0;
}

.loading {
  background-color: #e9ecef !important;
  color: #6c757d;
}

.loading-dots {
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0%, 20% { opacity: 1; }
  50% { opacity: 0.5; }
  80%, 100% { opacity: 1; }
}

.input-container {
  display: flex;
  padding: 1rem;
  border-top: 1px solid #ccc;
}

.input-container input {
  flex-grow: 1;
  padding: 0.5rem;
  border: 1px solid #ccc;
  border-radius: 0.5rem;
}

.input-container input:disabled {
  background-color: #f8f9fa;
  color: #6c757d;
  cursor: not-allowed;
}

.input-container button {
  margin-left: 1rem;
  padding: 0.5rem 1rem;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 0.5rem;
  cursor: pointer;
}

.input-container button:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

.input-container button:not(:disabled):hover {
  background-color: #0056b3;
}
</style>
