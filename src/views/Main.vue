<template>
  <ErrorBoundary>
    <div class="flex flex-col h-screen bg-gray-50">
      <!-- 頂部導航欄 -->
      <header class="bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="flex justify-between items-center h-16">
            <!-- 左側標題區 -->
            <div class="flex items-center space-x-4">
              <!-- 移動端選單按鈕 -->
              <button
                @click="toggleSidebar"
                class="lg:hidden p-2 rounded-md hover:bg-white/10 transition-colors touch-manipulation"
                :aria-label="sidebarVisible ? '關閉側邊欄' : '開啟側邊欄'"
              >
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path 
                    v-if="!sidebarVisible"
                    stroke-linecap="round" 
                    stroke-linejoin="round" 
                    stroke-width="2" 
                    d="M4 6h16M4 12h16M4 18h16"
                  />
                  <path 
                    v-else
                    stroke-linecap="round" 
                    stroke-linejoin="round" 
                    stroke-width="2" 
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
              
              <div class="flex items-baseline space-x-3">
                <h1 class="text-xl sm:text-2xl font-bold">UIGen Vue</h1>
                <span class="hidden sm:inline text-sm opacity-90">AI 驅動的 Vue 組件生成器</span>
              </div>
            </div>

            <!-- 右側操作區 -->
            <div class="flex items-center space-x-3">
              <!-- 當前專案顯示 -->
              <div v-if="currentProject" class="hidden sm:flex items-center space-x-2 text-sm">
                <span class="opacity-90">當前專案:</span>
                <span class="bg-white/20 px-2 py-1 rounded font-medium">
                  {{ currentProject.name }}
                </span>
              </div>
              
              <!-- 桌面端側邊欄切換 -->
              <button
                @click="toggleSidebar"
                class="hidden lg:flex items-center space-x-1 px-3 py-2 rounded-md hover:bg-white/10 transition-colors text-sm"
              >
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h8m-8 6h16"/>
                </svg>
                <span>{{ sidebarVisible ? '隱藏' : '顯示' }}</span>
              </button>
            </div>
          </div>
        </div>
      </header>

      <!-- 主要內容區域 -->
      <div class="flex flex-1 overflow-hidden">
        <!-- 側邊欄遮罩 (移動端) -->
        <div
          v-if="sidebarVisible && isMobile"
          @click="closeSidebar"
          class="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
        ></div>

        <!-- 側邊欄 -->
        <aside
          :class="[
            'bg-white border-r border-gray-200 flex flex-col transition-transform duration-300 ease-in-out z-50',
            'lg:relative lg:translate-x-0',
            sidebarVisible 
              ? 'fixed inset-y-0 left-0 w-80 translate-x-0' 
              : 'fixed inset-y-0 left-0 w-80 -translate-x-full lg:w-0 lg:overflow-hidden'
          ]"
        >
          <!-- 側邊欄標題 (移動端) -->
          <div class="lg:hidden flex items-center justify-between p-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-900">專案管理</h2>
            <button
              @click="closeSidebar"
              class="p-2 rounded-md hover:bg-gray-100 transition-colors touch-manipulation"
            >
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
              </svg>
            </button>
          </div>

          <!-- 專案管理區域 -->
          <div class="flex-1 overflow-y-auto scrollbar-hide">
            <div class="p-4 border-b border-gray-200">
              <h3 class="text-sm font-semibold text-gray-900 mb-3 hidden lg:block">專案管理</h3>
              
              <!-- 操作按鈕 -->
              <div class="grid grid-cols-1 gap-2 mb-4">
                <button
                  @click="showCreateProject = true"
                  class="btn btn-primary w-full touch-manipulation"
                >
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
                  </svg>
                  新建專案
                </button>
                <button
                  @click="loadProjects"
                  :disabled="isLoading"
                  class="btn btn-secondary w-full touch-manipulation"
                >
                  <svg 
                    class="w-4 h-4 mr-2" 
                    :class="{ 'animate-spin': isLoading }"
                    fill="none" 
                    stroke="currentColor" 
                    viewBox="0 0 24 24"
                  >
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                  </svg>
                  {{ isLoading ? '載入中...' : '重新整理' }}
                </button>
              </div>
              
              <!-- 專案列表 -->
              <div class="space-y-2 max-h-64 overflow-y-auto scrollbar-hide">
                <div
                  v-for="project in projects"
                  :key="project.id"
                  @click="selectProject(project)"
                  :class="[
                    'p-3 rounded-lg border cursor-pointer transition-all duration-200 touch-manipulation',
                    currentProject?.id === project.id
                      ? 'bg-blue-50 border-blue-200 ring-2 ring-blue-500 ring-opacity-20'
                      : 'bg-white border-gray-200 hover:bg-gray-50 hover:border-gray-300'
                  ]"
                >
                  <div class="font-medium text-gray-900 text-sm truncate">
                    {{ project.name }}
                  </div>
                  <div class="text-xs text-gray-500 mt-1 line-clamp-2">
                    {{ project.description || '無描述' }}
                  </div>
                </div>
                
                <div v-if="projects.length === 0 && !isLoading" class="text-center py-8">
                  <svg class="w-12 h-12 mx-auto text-gray-400 mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                  </svg>
                  <p class="text-sm text-gray-500">尚無專案</p>
                  <p class="text-xs text-gray-400 mt-1">點擊上方按鈕建立新專案</p>
                </div>
              </div>
            </div>

            <!-- 檔案樹區域 -->
            <div v-if="currentProject" class="p-4">
              <h3 class="text-sm font-semibold text-gray-900 mb-3">專案檔案</h3>
              <div class="space-y-1 max-h-48 overflow-y-auto scrollbar-hide">
                <div
                  v-for="file in currentProjectFiles"
                  :key="file.id"
                  class="flex items-center space-x-2 p-2 rounded hover:bg-gray-50 cursor-pointer transition-colors touch-manipulation"
                >
                  <svg class="w-4 h-4 text-gray-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                  </svg>
                  <span class="text-sm text-gray-700 truncate">{{ file.name }}</span>
                </div>
                
                <div v-if="currentProjectFiles.length === 0" class="text-center py-6">
                  <svg class="w-8 h-8 mx-auto text-gray-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"/>
                  </svg>
                  <p class="text-xs text-gray-500">專案中尚無檔案</p>
                </div>
              </div>
            </div>
          </div>
        </aside>

        <!-- 主要工作區 -->
        <main class="flex-1 overflow-auto bg-white">
          <router-view />
        </main>
      </div>

      <!-- 建立專案對話框 -->
      <div v-if="showCreateProject" class="modal-overlay" @click="closeCreateProjectModal">
        <div class="modal-content p-6" @click.stop>
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900">建立新專案</h3>
            <button
              @click="closeCreateProjectModal"
              class="p-2 rounded-md hover:bg-gray-100 transition-colors touch-manipulation"
            >
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
              </svg>
            </button>
          </div>
          
          <form @submit.prevent="createProject" class="space-y-4">
            <div>
              <label for="projectName" class="block text-sm font-medium text-gray-700 mb-1">
                專案名稱 <span class="text-red-500">*</span>
              </label>
              <input
                id="projectName"
                v-model="newProjectName"
                type="text"
                required
                placeholder="輸入專案名稱"
                class="input"
                :disabled="isLoading"
              />
            </div>
            
            <div>
              <label for="projectDescription" class="block text-sm font-medium text-gray-700 mb-1">
                專案描述
              </label>
              <textarea
                id="projectDescription"
                v-model="newProjectDescription"
                placeholder="輸入專案描述（可選）"
                rows="3"
                class="input resize-none"
                :disabled="isLoading"
              ></textarea>
            </div>
            
            <div class="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                @click="closeCreateProjectModal"
                class="btn btn-secondary"
                :disabled="isLoading"
              >
                取消
              </button>
              <button
                type="submit"
                :disabled="!newProjectName.trim() || isLoading"
                class="btn btn-primary"
              >
                <svg v-if="isLoading" class="w-4 h-4 mr-2 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                </svg>
                {{ isLoading ? '建立中...' : '建立專案' }}
              </button>
            </div>
          </form>
        </div>
      </div>

      <!-- 錯誤提示 -->
      <div
        v-if="error"
        class="fixed top-4 right-4 bg-red-500 text-white px-4 py-3 rounded-lg shadow-lg flex items-center space-x-3 z-50 animate-fade-in"
      >
        <svg class="w-5 h-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
        </svg>
        <span class="text-sm">{{ error }}</span>
        <button
          @click="clearError"
          class="p-1 rounded hover:bg-red-600 transition-colors touch-manipulation"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
          </svg>
        </button>
      </div>
    </div>
  </ErrorBoundary>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue';
import { storeToRefs } from 'pinia';
import { useProjectStore } from '../stores/project';
import ErrorBoundary from '../components/ErrorBoundary.vue';

// Store
const projectStore = useProjectStore();
const { 
  currentProject, 
  projects, 
  isLoading, 
  error, 
  currentProjectFiles 
} = storeToRefs(projectStore);

const {
  loadProjects,
  createProject: createProjectAction,
  setCurrentProject,
  clearError
} = projectStore;

// 響應式狀態
const sidebarVisible = ref(false); // 預設在移動端隱藏
const showCreateProject = ref(false);
const newProjectName = ref('');
const newProjectDescription = ref('');
const windowWidth = ref(window.innerWidth);

// 計算屬性
const isMobile = computed(() => windowWidth.value < 1024); // lg breakpoint

/**
 * 處理視窗大小變化
 */
const handleResize = () => {
  windowWidth.value = window.innerWidth;
  
  // 在桌面端自動顯示側邊欄，移動端自動隱藏
  if (windowWidth.value >= 1024) {
    sidebarVisible.value = true;
  } else {
    sidebarVisible.value = false;
  }
};

/**
 * 切換側邊欄顯示/隱藏
 */
const toggleSidebar = () => {
  sidebarVisible.value = !sidebarVisible.value;
};

/**
 * 關閉側邊欄 (主要用於移動端)
 */
const closeSidebar = () => {
  if (isMobile.value) {
    sidebarVisible.value = false;
  }
};

/**
 * 選擇專案並在移動端自動關閉側邊欄
 */
const selectProject = (project: any) => {
  setCurrentProject(project);
  if (isMobile.value) {
    closeSidebar();
  }
};

/**
 * 關閉建立專案對話框
 */
const closeCreateProjectModal = () => {
  showCreateProject.value = false;
  newProjectName.value = '';
  newProjectDescription.value = '';
};

/**
 * 建立新專案
 */
const createProject = async () => {
  try {
    await createProjectAction({
      name: newProjectName.value.trim(),
      description: newProjectDescription.value.trim() || undefined,
    });
    
    closeCreateProjectModal();
  } catch (err) {
    console.error('建立專案失敗:', err);
  }
};

// 組件掛載時的初始化
onMounted(() => {
  loadProjects();
  
  // 設定初始側邊欄狀態
  handleResize();
  
  // 監聽視窗大小變化
  window.addEventListener('resize', handleResize);
  
  // 監聽鍵盤事件 (ESC 關閉側邊欄和對話框)
  const handleKeydown = (event: KeyboardEvent) => {
    if (event.key === 'Escape') {
      if (showCreateProject.value) {
        closeCreateProjectModal();
      } else if (sidebarVisible.value && isMobile.value) {
        closeSidebar();
      }
    }
  };
  
  document.addEventListener('keydown', handleKeydown);
  
  // 清理事件監聽器
  onUnmounted(() => {
    window.removeEventListener('resize', handleResize);
    document.removeEventListener('keydown', handleKeydown);
  });
});
</script>

<style scoped>
/* 自定義樣式補充 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 確保側邊欄在移動端正確顯示 */
@media (max-width: 1023px) {
  aside {
    top: 4rem; /* 避免被 header 遮擋 */
  }
}

/* 觸控優化 */
@media (hover: none) and (pointer: coarse) {
  .touch-manipulation {
    touch-action: manipulation;
  }
  
  /* 增加觸控目標大小 */
  button, 
  [role="button"] {
    min-height: 44px;
    min-width: 44px;
  }
}

/* 高對比度模式支援 */
@media (prefers-contrast: high) {
  .bg-gradient-to-r {
    background: #1a365d !important;
  }
  
  .border-gray-200 {
    border-color: #000 !important;
  }
}

/* 減少動畫偏好 */
@media (prefers-reduced-motion: reduce) {
  .transition-transform,
  .transition-colors,
  .transition-all,
  .animate-spin,
  .animate-fade-in {
    transition: none !important;
    animation: none !important;
  }
}
</style>