/**
 * 檔案樹節點介面
 */
export interface FileNode {
  id: string
  name: string
  type: 'file' | 'folder'
  path: string
  children?: FileNode[]
  isExpanded?: boolean
  isModified?: boolean
  size?: number
  lastModified?: Date
}

/**
 * 檔案樹 Props 介面
 */
export interface FileTreeProps {
  projectId: string
}

/**
 * 檔案樹 Emits 介面
 */
export interface FileTreeEmits {
  'file-selected': [filePath: string]
  'file-created': [parentPath: string, fileName: string]
  'file-deleted': [filePath: string]
  'file-renamed': [oldPath: string, newPath: string]
}

/**
 * 檔案樹節點 Props 介面
 */
export interface FileTreeNodeProps {
  node: FileNode
  level: number
  selectedFile: string
  unsavedChanges: Set<string>
}

/**
 * 檔案樹節點 Emits 介面
 */
export interface FileTreeNodeEmits {
  'file-selected': [filePath: string]
  'file-created': [parentPath: string, fileName: string]
  'file-deleted': [filePath: string]
  'file-renamed': [oldPath: string, newPath: string]
}

/**
 * 右鍵選單項目介面
 */
export interface ContextMenuItem {
  id: string
  label: string
  icon: string
  danger?: boolean
}

/**
 * 檔案建立對話框 Props 介面
 */
export interface CreateFileDialogProps {
  parentPath: string
}

/**
 * 檔案建立對話框 Emits 介面
 */
export interface CreateFileDialogEmits {
  confirm: [fileName: string, content: string]
  cancel: []
}

/**
 * 資料夾建立對話框 Props 介面
 */
export interface CreateFolderDialogProps {
  parentPath: string
}

/**
 * 資料夾建立對話框 Emits 介面
 */
export interface CreateFolderDialogEmits {
  confirm: [folderName: string]
  cancel: []
}