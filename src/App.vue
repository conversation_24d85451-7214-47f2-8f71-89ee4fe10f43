<template>
  <!-- <StagewiseToolbar :config="config" /> -->
  <router-view />
  <!-- 全域 UX 組件 -->
  <NotificationContainer />
  <ConfirmDialog />
  <LoadingOverlay />
</template>

<script setup lang="ts">
  // import { StagewiseToolbar, type ToolbarConfig } from '@stagewise/toolbar-vue';
  // const config: ToolbarConfig = {
  //   plugins: [], // Add your custom plugins here
  // };

  import NotificationContainer from '@/components/ui/NotificationContainer.vue';
  import ConfirmDialog from '@/components/ui/ConfirmDialog.vue';
  import LoadingOverlay from '@/components/ui/LoadingOverlay.vue';

  // This is the main App component.
  // It simply renders the component matched by the current route.
</script>

<style>
  /* Global styles can go here */
</style>
