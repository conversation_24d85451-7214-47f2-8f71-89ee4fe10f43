import { parse, compileTemplate, compileScript, compileStyle } from '@vue/compiler-sfc'
import type { SFCDescriptor, CompilerError } from '@vue/compiler-sfc'

export interface CompileError {
  message: string
  line?: number
  column?: number
  file?: string
}

export interface CompileResult {
  script: string
  template: string
  styles: string[]
  errors: CompileError[]
  warnings: CompileError[]
}

/**
 * 編譯 Vue 單檔案組件
 * @param source Vue SFC 原始碼
 * @param filename 檔案名稱
 * @returns 編譯結果
 */
export async function compileVueComponent(
  source: string,
  filename: string = 'Component.vue'
): Promise<CompileResult> {
  const result: CompileResult = {
    script: '',
    template: '',
    styles: [],
    errors: [],
    warnings: []
  }

  try {
    // 解析 SFC
    const { descriptor, errors: parseErrors } = parse(source, {
      filename,
      sourceMap: false
    })

    // 處理解析錯誤
    if (parseErrors.length > 0) {
      result.errors.push(...parseErrors.map(formatCompilerError))
      return result
    }

    // 編譯 script 區塊
    if (descriptor.script || descriptor.scriptSetup) {
      try {
        const scriptResult = compileScript(descriptor, {
          id: filename,
          isProd: false,
          inlineTemplate: false,
          templateOptions: {
            compilerOptions: {
              mode: 'module'
            }
          }
        })

        result.script = generateScriptCode(scriptResult.content, descriptor)
      } catch (error) {
        result.errors.push({
          message: error instanceof Error ? error.message : '腳本編譯錯誤',
          file: filename
        })
      }
    } else {
      // 如果沒有 script 區塊，生成預設的組件定義
      result.script = generateDefaultScript(descriptor)
    }

    // 編譯 template 區塊
    if (descriptor.template) {
      try {
        const templateResult = compileTemplate({
          source: descriptor.template.content,
          filename,
          id: filename,
          scoped: descriptor.styles.some(style => style.scoped),
          slotted: false,
          isProd: false,
          compilerOptions: {
            mode: 'module',
            prefixIdentifiers: false,
            hoistStatic: false,
            cacheHandlers: false
          }
        })

        if (templateResult.errors.length > 0) {
          result.errors.push(...templateResult.errors.map(formatCompilerError))
        }

        if (templateResult.tips.length > 0) {
          result.warnings.push(...templateResult.tips.map(formatCompilerError))
        }

        result.template = templateResult.code
      } catch (error) {
        result.errors.push({
          message: error instanceof Error ? error.message : '模板編譯錯誤',
          file: filename
        })
      }
    }

    // 編譯 style 區塊
    if (descriptor.styles.length > 0) {
      for (let i = 0; i < descriptor.styles.length; i++) {
        const style = descriptor.styles[i]
        try {
          const styleResult = compileStyle({
            source: style.content,
            filename,
            id: `${filename}-${i}`,
            scoped: style.scoped || false,
            modules: style.module != null,
            preprocessLang: style.lang as any,
            postcssOptions: {},
            postcssPlugins: []
          })

          if (styleResult.errors.length > 0) {
            result.errors.push(...styleResult.errors.map(formatCompilerError))
          }

          result.styles.push(styleResult.code)
        } catch (error) {
          result.errors.push({
            message: error instanceof Error ? error.message : '樣式編譯錯誤',
            file: filename
          })
        }
      }
    }

  } catch (error) {
    result.errors.push({
      message: error instanceof Error ? error.message : '未知編譯錯誤',
      file: filename
    })
  }

  return result
}

/**
 * 生成腳本程式碼
 */
function generateScriptCode(scriptContent: string, descriptor: SFCDescriptor): string {
  // 移除 export default，因為我們會在預覽環境中手動處理
  let processedScript = scriptContent
    .replace(/export\s+default\s+/, '')
    .trim()

  // 如果腳本以 { 開頭，表示是物件形式的組件定義
  if (processedScript.startsWith('{')) {
    return `const PreviewComponent = ${processedScript};`
  }

  // 如果是 defineComponent 或其他函數調用
  if (processedScript.includes('defineComponent') || processedScript.includes('createApp')) {
    return `const PreviewComponent = ${processedScript};`
  }

  // 如果是完整的組件定義
  return `const PreviewComponent = ${processedScript};`
}

/**
 * 生成預設腳本程式碼（當沒有 script 區塊時）
 */
function generateDefaultScript(descriptor: SFCDescriptor): string {
  return `const PreviewComponent = {
  name: 'PreviewComponent'
};`
}

/**
 * 格式化編譯器錯誤
 */
function formatCompilerError(error: CompilerError): CompileError {
  return {
    message: error.message,
    line: error.loc?.start.line,
    column: error.loc?.start.column,
    file: error.loc?.source
  }
}

/**
 * 驗證 Vue SFC 語法
 * @param source Vue SFC 原始碼
 * @returns 驗證結果
 */
export function validateVueComponent(source: string): {
  isValid: boolean
  errors: CompileError[]
  warnings: CompileError[]
} {
  try {
    const { descriptor, errors } = parse(source, {
      filename: 'validation.vue',
      sourceMap: false
    })

    const result = {
      isValid: errors.length === 0,
      errors: errors.map(formatCompilerError),
      warnings: [] as CompileError[]
    }

    // 基本語法檢查
    if (descriptor.template) {
      // 檢查模板語法
      if (!descriptor.template.content.trim()) {
        result.warnings.push({
          message: '模板內容為空'
        })
      }
    }

    if (descriptor.script || descriptor.scriptSetup) {
      // 檢查腳本語法
      const scriptContent = descriptor.script?.content || descriptor.scriptSetup?.content || ''
      if (!scriptContent.trim()) {
        result.warnings.push({
          message: '腳本內容為空'
        })
      }
    }

    return result
  } catch (error) {
    return {
      isValid: false,
      errors: [{
        message: error instanceof Error ? error.message : '語法驗證錯誤'
      }],
      warnings: []
    }
  }
}

/**
 * 提取組件依賴
 * @param source Vue SFC 原始碼
 * @returns 依賴列表
 */
export function extractComponentDependencies(source: string): string[] {
  const dependencies: string[] = []
  
  try {
    // 提取 import 語句
    const importRegex = /import\s+(?:[\w\s{},*]+\s+from\s+)?['"`]([^'"`]+)['"`]/g
    let match
    
    while ((match = importRegex.exec(source)) !== null) {
      const dependency = match[1]
      if (!dependency.startsWith('.') && !dependency.startsWith('/')) {
        dependencies.push(dependency)
      }
    }

    // 提取 require 語句
    const requireRegex = /require\s*\(\s*['"`]([^'"`]+)['"`]\s*\)/g
    while ((match = requireRegex.exec(source)) !== null) {
      const dependency = match[1]
      if (!dependency.startsWith('.') && !dependency.startsWith('/')) {
        dependencies.push(dependency)
      }
    }

  } catch (error) {
    console.warn('Failed to extract dependencies:', error)
  }

  return [...new Set(dependencies)] // 去重
}

/**
 * 生成組件預覽程式碼
 * @param compileResult 編譯結果
 * @returns 預覽程式碼
 */
export function generatePreviewCode(compileResult: CompileResult): string {
  const { script, template, styles } = compileResult

  return `
// 組件定義
${script}

// 模板渲染函數
if (typeof PreviewComponent === 'object' && PreviewComponent !== null) {
  ${template}
  
  // 將渲染函數附加到組件
  if (typeof render === 'function') {
    PreviewComponent.render = render;
  }
}

// 樣式
const styles = [${styles.map(style => `\`${style}\``).join(', ')}];
styles.forEach(style => {
  const styleEl = document.createElement('style');
  styleEl.textContent = style;
  document.head.appendChild(styleEl);
});

// 匯出組件
window.PreviewComponent = PreviewComponent;
`.trim()
}

/**
 * 檢查是否為有效的 Vue SFC
 * @param source 原始碼
 * @returns 是否為有效的 Vue SFC
 */
export function isValidVueSFC(source: string): boolean {
  // 基本檢查：是否包含 Vue SFC 的基本結構
  const hasTemplate = /<template[^>]*>[\s\S]*<\/template>/i.test(source)
  const hasScript = /<script[^>]*>[\s\S]*<\/script>/i.test(source)
  const hasStyle = /<style[^>]*>[\s\S]*<\/style>/i.test(source)

  // 至少要有 template 或 script 其中一個
  return hasTemplate || hasScript || hasStyle
}

/**
 * 清理和格式化 Vue SFC 程式碼
 * @param source 原始碼
 * @returns 格式化後的程式碼
 */
export function formatVueSFC(source: string): string {
  try {
    // 基本的格式化處理
    return source
      .replace(/(<template[^>]*>)\s*\n/g, '$1\n  ')
      .replace(/\n\s*(<\/template>)/g, '\n$1')
      .replace(/(<script[^>]*>)\s*\n/g, '$1\n')
      .replace(/\n\s*(<\/script>)/g, '\n$1')
      .replace(/(<style[^>]*>)\s*\n/g, '$1\n')
      .replace(/\n\s*(<\/style>)/g, '\n$1')
      .trim()
  } catch (error) {
    console.warn('Failed to format Vue SFC:', error)
    return source
  }
}