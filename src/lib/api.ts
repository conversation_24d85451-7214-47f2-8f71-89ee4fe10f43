// /src/lib/api.ts
import axios from 'axios';
import type { AxiosError, AxiosResponse } from 'axios';

// 定義 API 錯誤回應格式
export interface ApiError {
  message: string;
  code?: string;
  details?: any;
}

// 定義 API 回應格式
export interface ApiResponse<T = any> {
  data: T;
  success: boolean;
  message?: string;
}

// 建立 Axios 實例
const apiClient = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001',
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 30000, // 30秒超時
});

// 請求攔截器
apiClient.interceptors.request.use(
  (config) => {
    // 可以在這裡添加認證 token 等
    console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`);
    return config;
  },
  (error) => {
    console.error('❌ Request Error:', error);
    return Promise.reject(error);
  }
);

// 回應攔截器
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    console.log(`✅ API Response: ${response.status} ${response.config.url}`);
    return response;
  },
  (error: AxiosError) => {
    console.error('❌ API Error:', error);
    
    // 統一錯誤處理
    const apiError: ApiError = {
      message: '發生未知錯誤',
    };

    if (error.response) {
      // 伺服器回應錯誤
      const { status, data } = error.response;
      apiError.message = (data as any)?.message || `HTTP ${status} 錯誤`;
      apiError.code = (data as any)?.code;
      apiError.details = data;
      
      // 根據狀態碼提供更友善的錯誤訊息
      switch (status) {
        case 400:
          apiError.message = '請求參數錯誤';
          break;
        case 401:
          apiError.message = '未授權，請重新登入';
          break;
        case 403:
          apiError.message = '權限不足';
          break;
        case 404:
          apiError.message = '資源不存在';
          break;
        case 500:
          apiError.message = '伺服器內部錯誤';
          break;
        case 503:
          apiError.message = '服務暫時不可用';
          break;
      }
    } else if (error.request) {
      // 網路錯誤
      apiError.message = '網路連線錯誤，請檢查網路狀態';
    } else {
      // 其他錯誤
      apiError.message = error.message || '發生未知錯誤';
    }

    return Promise.reject(apiError);
  }
);

// API 方法封裝
export const api = {
  // GET 請求
  get: <T = any>(url: string, params?: any): Promise<T> => {
    return apiClient.get(url, { params }).then(response => response.data);
  },

  // POST 請求
  post: <T = any>(url: string, data?: any): Promise<T> => {
    return apiClient.post(url, data).then(response => response.data);
  },

  // PUT 請求
  put: <T = any>(url: string, data?: any): Promise<T> => {
    return apiClient.put(url, data).then(response => response.data);
  },

  // DELETE 請求
  delete: <T = any>(url: string): Promise<T> => {
    return apiClient.delete(url).then(response => response.data);
  },

  // PATCH 請求
  patch: <T = any>(url: string, data?: any): Promise<T> => {
    return apiClient.patch(url, data).then(response => response.data);
  },
};

export default apiClient;
