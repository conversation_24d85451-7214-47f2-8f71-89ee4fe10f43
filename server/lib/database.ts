import { PrismaClient } from '@prisma/client'

/**
 * UIGen Vue 資料庫連接管理
 * 提供統一的 Prisma 客戶端實例和連接管理
 */

// 全域 Prisma 客戶端實例
let prisma: PrismaClient

/**
 * 建立或取得 Prisma 客戶端實例
 * 使用單例模式確保整個應用程式只有一個資料庫連接
 */
export function getPrismaClient(): PrismaClient {
  if (!prisma) {
    prisma = new PrismaClient({
      log: process.env.NODE_ENV === 'development' ? ['query', 'info', 'warn', 'error'] : ['error'],
      datasources: {
        db: {
          url: process.env.DATABASE_URL || 'file:./prisma/dev.db'
        }
      }
    })

    // 在開發環境中啟用查詢日誌
    if (process.env.NODE_ENV === 'development') {
      console.log('🗄️  Database connection established')
    }
  }

  return prisma
}

/**
 * 關閉資料庫連接
 * 用於應用程式關閉時的清理工作
 */
export async function closeDatabaseConnection(): Promise<void> {
  if (prisma) {
    await prisma.$disconnect()
    console.log('🗄️  Database connection closed')
  }
}

/**
 * 檢查資料庫連接狀態
 */
export async function checkDatabaseConnection(): Promise<boolean> {
  try {
    const client = getPrismaClient()
    await client.$queryRaw`SELECT 1`
    return true
  } catch (error) {
    console.error('❌ Database connection failed:', error)
    return false
  }
}

/**
 * 初始化資料庫
 * 確保資料庫架構是最新的
 */
export async function initializeDatabase(): Promise<void> {
  try {
    const client = getPrismaClient()
    
    // 檢查連接
    await client.$connect()
    
    console.log('✅ Database initialized successfully')
  } catch (error) {
    console.error('❌ Database initialization failed:', error)
    throw error
  }
}

// 預設匯出 Prisma 客戶端實例
export default getPrismaClient()