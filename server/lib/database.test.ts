import { describe, it, expect, beforeAll, afterAll } from 'vitest'
import { getPrismaClient, checkDatabaseConnection, closeDatabaseConnection } from './database'

describe('Database Connection', () => {
  let prisma: ReturnType<typeof getPrismaClient>

  beforeAll(() => {
    prisma = getPrismaClient()
  })

  afterAll(async () => {
    await closeDatabaseConnection()
  })

  it('should create a Prisma client instance', () => {
    expect(prisma).toBeDefined()
    expect(typeof prisma.$connect).toBe('function')
    expect(typeof prisma.$disconnect).toBe('function')
  })

  it('should check database connection successfully', async () => {
    const isConnected = await checkDatabaseConnection()
    expect(isConnected).toBe(true)
  })

  it('should be able to query the database', async () => {
    // 測試基本查詢功能
    const result = await prisma.$queryRaw`SELECT 1 as test`
    expect(result).toBeDefined()
  })

  it('should handle multiple client requests with singleton pattern', () => {
    const client1 = getPrismaClient()
    const client2 = getPrismaClient()
    
    // 應該返回同一個實例
    expect(client1).toBe(client2)
  })
})