
// server/routes/projects.ts

import { Router } from 'express';
import prisma from '../lib/database';

// 建立 Express 路由器實例
// 用於定義專案相關的 API 路由
const router = Router();

/**
 * @swagger
 * tags:
 *   name: Projects
 *   description: 專案管理 API
 */

/**
 * @swagger
 * /api/projects:
 *   get:
 *     summary: 取得所有專案
 *     tags: [Projects]
 *     responses:
 *       200:
 *         description: 成功取得專案列表
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Project'
 */
// GET /api/projects - 取得所有專案
router.get('/', async (_req, res) => {
  try {
    // 從資料庫中查詢所有專案
    const projects = await prisma.project.findMany({
      include: {
        files: true, // 同時載入關聯的檔案
      },
    });
    // 以 JSON 格式回傳專案列表
    res.json(projects);
  } catch (error) {
    // 處理錯誤情況
    res.status(500).json({ error: '無法取得專案' });
  }
});

/**
 * @swagger
 * /api/projects:
 *   post:
 *     summary: 建立新專案
 *     tags: [Projects]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *     responses:
 *       201:
 *         description: 專案建立成功
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Project'
 */
// POST /api/projects - 建立新專案
router.post('/', async (req, res) => {
  try {
    // 從請求主體中獲取專案名稱和描述
    const { name, description } = req.body;
    
    // 輸入驗證
    if (name === undefined || name === null) {
      return res.status(400).json({ error: '專案名稱為必填項目' });
    }
    
    if (typeof name !== 'string') {
      return res.status(400).json({ error: '專案名稱必須為字串' });
    }
    
    // 清理和驗證名稱
    const cleanName = name.trim();
    
    // 檢查空字串
    if (cleanName.length === 0) {
      return res.status(400).json({ error: '專案名稱不能為空' });
    }
    
    // 檢查長度限制
    if (cleanName.length > 255) {
      return res.status(400).json({ error: '專案名稱過長，最多 255 個字元' });
    }
    
    // XSS 防護 - 清理惡意腳本
    const sanitizedName = cleanName.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
    const sanitizedDescription = description ? 
      String(description).replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') : 
      null;
    
    // 在資料庫中建立新專案
    const newProject = await prisma.project.create({
      data: {
        name: sanitizedName,
        description: sanitizedDescription,
      },
    });
    // 以 201 Created 狀態碼回傳新建立的專案
    res.status(201).json(newProject);
  } catch (error: any) {
    console.error('建立專案錯誤:', error);
    // 處理錯誤情況
    res.status(400).json({ error: '無法建立專案' });
  }
});

/**
 * @swagger
 * /api/projects/{id}:
 *   get:
 *     summary: 取得特定專案詳情
 *     tags: [Projects]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 專案 ID
 *     responses:
 *       200:
 *         description: 成功取得專案詳情
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Project'
 *       404:
 *         description: 專案不存在
 */
// GET /api/projects/:id - 取得特定專案詳情
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    const project = await prisma.project.findUnique({
      where: { id },
      include: {
        files: true, // 包含關聯的檔案
      },
    });

    if (!project) {
      return res.status(404).json({ error: '專案不存在' });
    }

    res.json(project);
  } catch (error) {
    console.error('取得專案詳情錯誤:', error);
    res.status(500).json({ error: '無法取得專案詳情' });
  }
});

/**
 * @swagger
 * /api/projects/{id}:
 *   put:
 *     summary: 更新專案資訊
 *     tags: [Projects]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 專案 ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *     responses:
 *       200:
 *         description: 專案更新成功
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Project'
 *       404:
 *         description: 專案不存在
 */
// PUT /api/projects/:id - 更新專案資訊
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description } = req.body;

    // 檢查專案是否存在
    const existingProject = await prisma.project.findUnique({
      where: { id },
    });

    if (!existingProject) {
      return res.status(404).json({ error: '專案不存在' });
    }

    // 準備更新資料
    const updateData: { name?: string; description?: string | null } = {};

    if (name !== undefined) {
      if (typeof name !== 'string') {
        return res.status(400).json({ error: '專案名稱必須為字串' });
      }
      
      const cleanName = name.trim();
      if (cleanName.length === 0) {
        return res.status(400).json({ error: '專案名稱不能為空' });
      }
      
      if (cleanName.length > 255) {
        return res.status(400).json({ error: '專案名稱過長，最多 255 個字元' });
      }
      
      updateData.name = cleanName.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
    }

    if (description !== undefined) {
      updateData.description = description ? 
        String(description).replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') : 
        null;
    }

    // 更新專案
    const updatedProject = await prisma.project.update({
      where: { id },
      data: updateData,
      include: {
        files: true,
      },
    });

    res.json(updatedProject);
  } catch (error) {
    console.error('更新專案錯誤:', error);
    res.status(500).json({ error: '無法更新專案' });
  }
});

/**
 * @swagger
 * /api/projects/{id}:
 *   delete:
 *     summary: 刪除專案
 *     tags: [Projects]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 專案 ID
 *     responses:
 *       200:
 *         description: 專案刪除成功
 *       404:
 *         description: 專案不存在
 */
// DELETE /api/projects/:id - 刪除專案
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // 檢查專案是否存在
    const existingProject = await prisma.project.findUnique({
      where: { id },
    });

    if (!existingProject) {
      return res.status(404).json({ error: '專案不存在' });
    }

    // 刪除專案（Prisma 會自動處理關聯的檔案刪除，如果設定了 cascade）
    await prisma.project.delete({
      where: { id },
    });

    res.json({ message: '專案刪除成功' });
  } catch (error) {
    console.error('刪除專案錯誤:', error);
    res.status(500).json({ error: '無法刪除專案' });
  }
});

export default router;
