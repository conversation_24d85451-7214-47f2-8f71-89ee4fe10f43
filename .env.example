# 應用程式設定
VITE_APP_NAME=UIGen Vue
VITE_APP_VERSION=1.0.0
VITE_APP_ENV=development

# API 設定
VITE_API_BASE_URL=http://localhost:3001
VITE_WS_URL=ws://localhost:3001

# 資料庫設定
DATABASE_URL="file:./prisma/dev.db"

# JWT 設定
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d

# AI 提供者設定
ANTHROPIC_API_KEY=your-anthropic-api-key
OPENAI_API_KEY=your-openai-api-key
GOOGLE_AI_API_KEY=your-google-ai-api-key

# AI 設定
DEFAULT_AI_PROVIDER=anthropic
AI_MODEL_CLAUDE=claude-3-sonnet-20240229
AI_MODEL_GPT=gpt-4
AI_MODEL_GEMINI=gemini-pro

# 速率限制設定
AI_RATE_LIMIT_PER_MINUTE=60
AI_RATE_LIMIT_PER_HOUR=1000
AI_COST_LIMIT_DAILY=10.00
AI_COST_LIMIT_MONTHLY=300.00

# 伺服器設定
PORT=3001
NODE_ENV=development

# 日誌設定
LOG_LEVEL=info
LOG_FILE=logs/app.log

# 安全性設定
CORS_ORIGIN=http://localhost:5173
SESSION_SECRET=your-session-secret-change-this

# 檔案上傳設定
MAX_FILE_SIZE=10MB
UPLOAD_DIR=uploads/