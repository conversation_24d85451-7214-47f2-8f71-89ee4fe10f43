const { defineWorkspace } = require('vitest/config')

module.exports = defineWorkspace([
  // Default tests (used by npm run test)
  {
    extends: './vitest.config.ts',
    test: {
      name: 'default',
      root: './',
    }
  },
  // Frontend tests
  {
    extends: './tests/config/vitest.config.frontend.ts',
    test: {
      name: 'frontend',
      root: './',
    }
  },
  // Server tests  
  {
    extends: './tests/config/vitest.config.server.ts',
    test: {
      name: 'server',
      root: './',
    }
  },
  // Integration tests
  {
    extends: './tests/config/vitest.config.integration.ts', 
    test: {
      name: 'integration',
      root: './',
    }
  },
  // CI environment tests (used in CI/CD pipelines)
  {
    extends: './tests/config/vitest.config.ci.ts',
    test: {
      name: 'ci',
      root: './',
    }
  },
  // Parallel execution tests (for high-performance test runs)
  {
    extends: './tests/config/vitest.config.parallel.ts',
    test: {
      name: 'parallel',
      root: './',
    }
  }
])