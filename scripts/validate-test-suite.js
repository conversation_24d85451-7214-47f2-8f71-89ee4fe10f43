#!/usr/bin/env node

/**
 * 測試套件驗證腳本
 * 執行完整的測試套件驗證，包括測試執行、覆蓋率檢查和效能分析
 */

const { execSync } = require('child_process')
const fs = require('fs')
const path = require('path')

// 設置顏色輸出
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
}

// 設置目錄路徑
const rootDir = path.resolve(__dirname, '..')
const testResultsDir = path.join(rootDir, 'test-results')
const coverageDir = path.join(rootDir, 'coverage')

// 確保目錄存在
if (!fs.existsSync(testResultsDir)) {
  fs.mkdirSync(testResultsDir, { recursive: true })
}

/**
 * 執行命令並返回輸出
 * @param {string} command 要執行的命令
 * @param {boolean} silent 是否靜默執行
 * @returns {string} 命令輸出
 */
function runCommand(command, silent = false) {
  try {
    if (!silent) {
      console.log(`${colors.dim}> ${command}${colors.reset}`)
    }
    return execSync(command, { encoding: 'utf-8', stdio: silent ? 'pipe' : 'inherit' })
  } catch (error) {
    if (!silent) {
      console.error(`${colors.red}命令執行失敗: ${command}${colors.reset}`)
      console.error(error.message)
    }
    return error.message
  }
}

/**
 * 格式化時間
 * @param {number} ms 毫秒
 * @returns {string} 格式化的時間
 */
function formatTime(ms) {
  if (ms < 1000) {
    return `${ms}ms`
  } else if (ms < 60000) {
    return `${(ms / 1000).toFixed(2)}s`
  } else {
    const minutes = Math.floor(ms / 60000)
    const seconds = ((ms % 60000) / 1000).toFixed(2)
    return `${minutes}m ${seconds}s`
  }
}

/**
 * 主函數
 */
async function main() {
  console.log(`\n${colors.bright}${colors.blue}=== 測試套件驗證 ===${colors.reset}\n`)
  
  // 記錄開始時間
  const startTime = Date.now()
  
  // 步驟 1: 執行前端測試
  console.log(`\n${colors.cyan}[1/5] 執行前端測試${colors.reset}`)
  const frontendTestStart = Date.now()
  runCommand('npm run test:frontend -- --reporter=junit --outputFile.junit=./test-results/frontend-junit.xml')
  const frontendTestDuration = Date.now() - frontendTestStart
  console.log(`${colors.green}✓ 前端測試完成 (${formatTime(frontendTestDuration)})${colors.reset}`)
  
  // 步驟 2: 執行後端測試
  console.log(`\n${colors.cyan}[2/5] 執行後端測試${colors.reset}`)
  const backendTestStart = Date.now()
  runCommand('npm run test:backend -- --reporter=junit --outputFile.junit=./test-results/backend-junit.xml')
  const backendTestDuration = Date.now() - backendTestStart
  console.log(`${colors.green}✓ 後端測試完成 (${formatTime(backendTestDuration)})${colors.reset}`)
  
  // 步驟 3: 執行整合測試
  console.log(`\n${colors.cyan}[3/5] 執行整合測試${colors.reset}`)
  const integrationTestStart = Date.now()
  runCommand('npm run test:integration -- --reporter=junit --outputFile.junit=./test-results/integration-junit.xml')
  const integrationTestDuration = Date.now() - integrationTestStart
  console.log(`${colors.green}✓ 整合測試完成 (${formatTime(integrationTestDuration)})${colors.reset}`)
  
  // 步驟 4: 執行覆蓋率測試
  console.log(`\n${colors.cyan}[4/5] 執行覆蓋率測試${colors.reset}`)
  const coverageStart = Date.now()
  runCommand('npm run test:coverage')
  const coverageDuration = Date.now() - coverageStart
  console.log(`${colors.green}✓ 覆蓋率測試完成 (${formatTime(coverageDuration)})${colors.reset}`)
  
  // 步驟 5: 生成測試品質報告
  console.log(`\n${colors.cyan}[5/5] 生成測試品質報告${colors.reset}`)
  
  // 使用 Node.js 執行 TestQualityReporter
  const reportScript = `
  const { TestQualityReporter } = require('../dist/tests/utils/test-quality-reporter');
  
  // 創建測試品質報告生成器
  const reporter = new TestQualityReporter();
  
  // 生成測試品質報告
  const report = reporter.generateQualityReport();
  
  // 生成 HTML 報告
  const htmlReportPath = reporter.generateHtmlReport();
  
  console.log('測試品質報告已生成:', htmlReportPath);
  console.log('總體品質分數:', report.quality.overallScore.toFixed(2));
  console.log('覆蓋率分數:', report.quality.coverageScore.toFixed(2));
  console.log('效能分數:', report.quality.performanceScore.toFixed(2));
  console.log('穩定性分數:', report.quality.stabilityScore.toFixed(2));
  `
  
  // 將腳本寫入臨時檔案
  const tempScriptPath = path.join(testResultsDir, 'generate-report.js')
  fs.writeFileSync(tempScriptPath, reportScript)
  
  // 執行腳本
  try {
    // 先編譯 TypeScript
    runCommand('npx tsc --project tsconfig.test.json')
    
    // 執行報告生成腳本
    runCommand(`node ${tempScriptPath}`)
  } catch (error) {
    console.error(`${colors.red}無法生成測試品質報告:${colors.reset}`, error)
  }
  
  // 清理臨時檔案
  if (fs.existsSync(tempScriptPath)) {
    fs.unlinkSync(tempScriptPath)
  }
  
  // 計算總執行時間
  const totalDuration = Date.now() - startTime
  
  // 輸出摘要
  console.log(`\n${colors.bright}${colors.blue}=== 測試套件驗證摘要 ===${colors.reset}`)
  console.log(`${colors.cyan}前端測試:${colors.reset} ${formatTime(frontendTestDuration)}`)
  console.log(`${colors.cyan}後端測試:${colors.reset} ${formatTime(backendTestDuration)}`)
  console.log(`${colors.cyan}整合測試:${colors.reset} ${formatTime(integrationTestDuration)}`)
  console.log(`${colors.cyan}覆蓋率測試:${colors.reset} ${formatTime(coverageDuration)}`)
  console.log(`${colors.cyan}總執行時間:${colors.reset} ${formatTime(totalDuration)}`)
  
  console.log(`\n${colors.green}測試套件驗證完成!${colors.reset}`)
  console.log(`${colors.dim}測試報告位於: ${path.join(testResultsDir, 'quality-report.html')}${colors.reset}`)
  console.log(`${colors.dim}覆蓋率報告位於: ${path.join(coverageDir, 'index.html')}${colors.reset}\n`)
}

// 執行主函數
main().catch(error => {
  console.error(`${colors.red}錯誤:${colors.reset}`, error)
  process.exit(1)
})