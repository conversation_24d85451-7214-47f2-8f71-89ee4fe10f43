{"name": "uigen-vue", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "concurrently \"npm run dev:frontend\" \"npm run dev:backend\"", "dev:frontend": "vite", "dev:backend": "nodemon", "build": "npm run build:frontend && npm run build:backend", "build:frontend": "vue-tsc --noEmit --skipLibCheck && vite build", "build:backend": "tsc --project tsconfig.server.json", "preview": "vite preview", "setup": "npm install && npx prisma generate && npx prisma db push", "db:reset": "npx prisma db push --force-reset", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix", "lint:check": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts", "format": "prettier --write .", "format:check": "prettier --check .", "test": "vitest run", "test:ui": "vitest --ui", "test:coverage": "vitest run --coverage", "test:frontend": "vitest run --project=frontend", "test:backend": "vitest run --project=server", "test:server": "vitest run --project=server", "test:integration": "vitest run --project=integration", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "test:e2e:debug": "playwright test --debug", "test:e2e:report": "playwright show-report", "test:ci": "vitest run --pool=forks --poolOptions.forks.maxForks=4", "test:ci:coverage": "vitest run --coverage --pool=forks --poolOptions.forks.maxForks=4", "test:ci:parallel": "vitest run --pool=forks --poolOptions.forks.maxForks=4", "test:ci:report": "vitest run --reporter=junit --outputFile.junit=./test-results/junit.xml", "test:parallel": "vitest run --pool=forks --poolOptions.forks.maxForks=4", "test:parallel:watch": "vitest --pool=forks --poolOptions.forks.maxForks=4", "test:parallel:coverage": "vitest run --coverage --pool=forks --poolOptions.forks.maxForks=4", "test:validate": "node scripts/validate-test-suite.js", "test:quality-report": "npm run test:coverage && node scripts/validate-test-suite.js", "server:dev": "nodemon --exec ts-node server/index.ts", "server:build": "tsc --project tsconfig.server.json", "server:start": "node dist/server/index.js"}, "dependencies": {"@anthropic-ai/sdk": "^0.56.0", "@google/generative-ai": "^0.24.1", "@heroicons/vue": "^2.2.0", "@monaco-editor/loader": "^1.5.0", "@prisma/client": "^6.12.0", "axios": "^1.10.0", "bcryptjs": "^3.0.2", "concurrently": "^9.2.0", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^5.1.0", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "monaco-editor": "^0.52.2", "morgan": "^1.10.1", "openai": "^5.10.1", "pinia": "^3.0.3", "prisma": "^6.12.0", "uuid": "^11.1.0", "vue": "^3.5.17", "vue-router": "^4.5.1", "ws": "^8.18.3", "yaml": "^2.8.0"}, "devDependencies": {"@faker-js/faker": "^9.9.0", "@playwright/test": "^1.54.1", "@rollup/rollup-darwin-arm64": "^4.46.0", "@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jsonwebtoken": "^9.0.10", "@types/morgan": "^1.9.10", "@types/node": "^24.0.15", "@types/supertest": "^6.0.3", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.8", "@types/uuid": "^10.0.0", "@types/ws": "^8.18.1", "@types/yaml": "^1.9.6", "@typescript-eslint/eslint-plugin": "^8.37.0", "@typescript-eslint/parser": "^8.37.0", "@vitejs/plugin-vue": "^6.0.0", "@vitest/ui": "^3.2.4", "@vue/eslint-config-typescript": "^14.6.0", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.21", "eslint": "^9.31.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.3", "eslint-plugin-vue": "^10.3.0", "fast-xml-parser": "^4.3.2", "jsdom": "^26.1.0", "nodemon": "^3.1.10", "prettier": "^3.6.2", "rollup": "^4.46.0", "supertest": "^7.1.3", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "tailwindcss": "^3.4.0", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "tsx": "^4.20.3", "typescript": "~5.8.3", "vite": "^7.0.4", "vitest": "^3.2.4", "vue-tsc": "^2.2.12"}}