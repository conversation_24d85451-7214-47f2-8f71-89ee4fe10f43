{"extends": "./tsconfig.json", "compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "node", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "baseUrl": ".", "types": ["node", "vitest/globals"], "paths": {"@/*": ["src/*"], "@shared/*": ["shared/*"], "@server/*": ["server/*"]}}, "include": ["tests/**/*", "server/**/*.test.ts", "shared/**/*"], "exclude": ["node_modules", "dist"]}