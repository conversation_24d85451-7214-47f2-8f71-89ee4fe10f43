name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        node-version: [18.x, 20.x]
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run linting
      run: npm run lint:check
    
    - name: Run formatting check
      run: npm run format:check
    
    - name: Run type checking
      run: npm run build
    
    - name: Setup test database
      run: npx prisma generate
    
    - name: Run tests with CI configuration
      run: npm run test:ci:parallel
    
    - name: Upload test results
      uses: actions/upload-artifact@v4
      with:
        name: test-results
        path: test-results/
        if-no-files-found: warn
    
    - name: Generate test coverage
      run: npm run test:ci:coverage
    
    - name: Upload coverage reports
      uses: codecov/codecov-action@v4
      with:
        directory: ./coverage/
        fail_ci_if_error: false
        verbose: true
      
  build:
    needs: test
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Use Node.js 20.x
      uses: actions/setup-node@v4
      with:
        node-version: 20.x
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Build application
      run: npm run build
    
    - name: Upload build artifacts
      uses: actions/upload-artifact@v4
      with:
        name: dist
        path: dist/
