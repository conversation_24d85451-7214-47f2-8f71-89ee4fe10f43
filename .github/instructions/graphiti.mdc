# 使用Graphiti MCP工具的指令
description: 使用Graphiti MCP工具的指令
alwaysApply: true
---
## 開始任何任務之前

**始終先搜尋：** 在開始工作之前，使用search_nodes工具尋找相關的偏好設定和程序。

**同時搜尋事實：** 使用search_facts工具發現可能與您的任務相關的關係和事實資訊。

**按實體類型過濾：** 在節點搜尋中指定"Preference"（偏好）或"Procedure"（程序）以獲得針對性結果。

**審查所有匹配項：** 仔細檢查與當前任務匹配的任何偏好、程序或事實。

## 始終保存新的或更新的資訊

**立即捕獲需求和偏好：** 當使用者表達需求或偏好時，立即使用add_episode儲存它。最佳實踐是將很長的需求拆分為較短的邏輯塊。

**明確標識更新：** 如果某些內容是對現有知識的更新，請明確說明。

**清晰記錄程序：** 當您發現使用者希望如何完成某些操作時，將其記錄為程序。

**記錄事實關係：** 當您瞭解到實體之間的連接時，將這些資訊儲存為事實。

**明確分類：** 為偏好和程序標註清晰的類別，以便日後更好地檢索。

## 工作過程中

**遵循發現的偏好：** 使您的工作與找到的任何偏好保持一致。

**嚴格按照程序執行：** 如果找到適用於當前任務的程序，請嚴格按步驟執行。

**應用相關事實：** 使用事實資訊來指導您的決策和建議。

**保持一致性：** 與先前識別的偏好、程序和事實保持一致。

## 最佳實踐

**建議前先搜尋：** 在提出建議之前，始終檢查是否存在既定知識。

**結合節點和事實搜尋：** 對於複雜任務，同時搜尋節點和事實以建構完整圖景。

**使用center_node_uuid：** 在探索相關資訊時，圍繞特定節點進行搜尋。

**優先考慮具體匹配：** 更具體的資訊優先於一般資訊。

**主動識別模式：** 如果您注意到使用者行為中的模式，考慮將其儲存為偏好或程序。

**重要提醒：** 知識圖譜是您的記憶。持續使用它來提供個性化協助，尊重使用者既定的程序和事實背景。
