import { defineConfig } from 'vitest/config'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue()],
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: ['./tests/setup.ts'],
    include: [
      'src/**/*.test.ts',
      'tests/unit/**/*.test.ts',
      'tests/server/**/*.test.ts',
      'tests/integration/**/*.test.ts',
      'server/**/*.test.ts'
    ],
    testTimeout: 10000,
    hookTimeout: 10000,
    // Use projects for different test types
    projects: [
      {
        name: 'frontend',
        environment: 'jsdom',
        setupFiles: ['./tests/setup/frontend-setup.ts'],
        include: ['src/**/*.test.ts', 'tests/unit/**/*.test.ts'],
        testTimeout: 10000,
        hookTimeout: 10000,
      },
      {
        name: 'server',
        environment: 'node',
        setupFiles: ['./tests/setup/server-setup.ts'],
        include: ['tests/server/**/*.test.ts', 'server/**/*.test.ts'],
        testTimeout: 15000,
        hookTimeout: 15000,
      },
      {
        name: 'integration',
        environment: 'node',
        setupFiles: ['./tests/setup/integration-setup.ts'],
        include: ['tests/integration/**/*.test.ts'],
        testTimeout: 30000,
        hookTimeout: 30000,
      }
    ],
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, './src'),
      '@shared': resolve(__dirname, './shared'),
      '@server': resolve(__dirname, './server'),
    },
  },
})