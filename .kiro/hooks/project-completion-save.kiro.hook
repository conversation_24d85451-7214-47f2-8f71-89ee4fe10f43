{"enabled": true, "name": "Task Completion Knowledge Save", "description": "Automatically saves comprehensive project information to Graphiti knowledge graph when a task completion is detected, including technical stack, architecture decisions, implementation details, problems and solutions, performance optimizations, code organization, development lessons, and future improvement suggestions", "version": "1", "when": {"type": "fileEdited", "patterns": ["README.md", "package.json", "plan.md", "rewriteplan.md", "system-integration-report.md"]}, "then": {"type": "askAgent", "prompt": "項目完成後執行知識儲存：\n\n請將當前項目的完整資訊儲存到Graphiti知識圖譜中，包括：\n\n**項目總結儲存**\n1. 項目基本資訊和最終成果\n2. 完整的技術堆疊和架構決策\n3. 所有功能模組的實現細節\n4. 遇到的問題和解決方案\n5. 性能最佳化措施和效果\n6. 程式碼組織結構和設計模式\n7. 開發經驗和教訓總結\n8. 未來改進建議\n\n**儲存要求：**\n- 建立完整的知識圖譜實體和關係\n- 確保資訊可被後續項目查詢和復用\n- 建立與相關技術領域的關聯\n- 記錄開發時間線和里程碑\n\n請按照結構化格式系統性地儲存所有項目知識。\n\n基於以下項目結構進行分析：\n- Vue 3 + TypeScript 前端應用\n- Express.js + Prisma 後端\n- AI 整合 (Claude, GPT-4, Gemini)\n- 完整測試套件 (Vitest, Playwright)\n- 虛擬檔案系統實現"}}