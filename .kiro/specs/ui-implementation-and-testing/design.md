# Design Document

## Overview

本設計文件描述 UIGen Vue 專案使用者介面實作和整合測試的技術架構。基於已完成的基礎架構（Vue 3 + Vite、Pinia 狀態管理、Express.js 後端、AI 整合系統），我們將實作核心 UI 組件並建立完整的測試體系，確保系統的穩定性和使用者體驗。

設計遵循 KISS 原則，避免過度設計，採用測試驅動開發（TDD）方法，先設計介面、編寫測試、再實作功能。

## Architecture

### 整體架構圖

```mermaid
graph TB
    subgraph "前端 Vue 3 應用"
        A[ChatInterface.vue] --> B[Pinia Chat Store]
        C[CodeEditor.vue] --> D[Pinia FileSystem Store]
        E[PreviewFrame.vue] --> F[Vue Compiler]
        G[FileTree.vue] --> D
        H[ProjectManager.vue] --> I[Pinia Project Store]
    end
    
    subgraph "後端服務"
        J[Express.js API] --> K[AI Provider Manager]
        J --> L[SQLite Database]
        M[WebSocket Server] --> N[Chat Stream Handler]
    end
    
    subgraph "測試層"
        O[Unit Tests] --> P[Vue Test Utils]
        Q[Integration Tests] --> R[API Testing]
        S[E2E Tests] --> T[Playwright]
    end
    
    A --> M
    C --> J
    H --> J
```

### 核心設計原則

1. **組件化設計**: 每個功能模組獨立開發，可重用和測試
2. **狀態集中管理**: 使用 Pinia 統一管理應用狀態
3. **測試驅動開發**: 先寫測試，再實作功能
4. **響應式優先**: 支援多種裝置和螢幕尺寸
5. **錯誤優雅處理**: 完整的錯誤邊界和使用者提示

## Components and Interfaces

### 1. ChatInterface 組件

**檔案位置**: `src/components/chat/ChatInterface.vue`

**介面設計**:
```typescript
interface ChatInterfaceProps {
  projectId?: string
}

interface ChatMessage {
  id: string
  type: 'user' | 'ai' | 'system'
  content: string
  timestamp: Date
  metadata?: {
    tokens?: number
    model?: string
  }
}

interface ChatInterfaceEmits {
  'message-sent': (message: string) => void
  'connection-status': (status: 'connected' | 'disconnected' | 'error') => void
}
```

**核心功能**:
- WebSocket 即時通訊
- 訊息歷史顯示
- 輸入框和發送按鈕
- 載入狀態指示器
- 錯誤處理和重連機制

### 2. CodeEditor 組件

**檔案位置**: `src/components/editor/CodeEditor.vue`

**介面設計**:
```typescript
interface CodeEditorProps {
  filePath: string
  language: 'vue' | 'typescript' | 'javascript' | 'css'
  readonly?: boolean
}

interface CodeEditorEmits {
  'content-changed': (content: string) => void
  'file-saved': (filePath: string, content: string) => void
  'validation-error': (errors: ValidationError[]) => void
}

interface ValidationError {
  line: number
  column: number
  message: string
  severity: 'error' | 'warning' | 'info'
}
```

**核心功能**:
- Monaco Editor 整合
- 語法高亮和智能提示
- 即時錯誤檢查
- 自動格式化
- 檔案儲存功能

### 3. PreviewFrame 組件

**檔案位置**: `src/components/preview/PreviewFrame.vue`

**介面設計**:
```typescript
interface PreviewFrameProps {
  componentCode: string
  dependencies?: string[]
}

interface PreviewFrameEmits {
  'compile-success': () => void
  'compile-error': (error: CompileError) => void
  'runtime-error': (error: RuntimeError) => void
}

interface CompileError {
  message: string
  line?: number
  column?: number
  file?: string
}
```

**核心功能**:
- Vue SFC 動態編譯
- 沙盒化執行環境
- 錯誤邊界處理
- 熱重載支援

### 4. FileTree 組件

**檔案位置**: `src/components/editor/FileTree.vue`

**介面設計**:
```typescript
interface FileTreeProps {
  projectId: string
}

interface FileNode {
  id: string
  name: string
  type: 'file' | 'folder'
  path: string
  children?: FileNode[]
  isModified?: boolean
}

interface FileTreeEmits {
  'file-selected': (filePath: string) => void
  'file-created': (parentPath: string, fileName: string) => void
  'file-deleted': (filePath: string) => void
  'file-renamed': (oldPath: string, newPath: string) => void
}
```

**核心功能**:
- 樹狀結構顯示
- 檔案操作選單
- 拖拽排序支援
- 變更狀態指示

### 5. ProjectManager 組件

**檔案位置**: `src/components/project/ProjectManager.vue`

**介面設計**:
```typescript
interface ProjectManagerProps {
  showCreateDialog?: boolean
}

interface Project {
  id: string
  name: string
  description?: string
  createdAt: Date
  updatedAt: Date
  fileCount: number
}

interface ProjectManagerEmits {
  'project-selected': (projectId: string) => void
  'project-created': (project: Project) => void
  'project-deleted': (projectId: string) => void
}
```

**核心功能**:
- 專案列表顯示
- 專案建立對話框
- 專案刪除確認
- 專案搜尋和篩選

## Data Models

### 1. Chat Store 資料模型

```typescript
interface ChatState {
  messages: ChatMessage[]
  isConnected: boolean
  isLoading: boolean
  currentModel: string
  connectionError?: string
}

interface ChatActions {
  sendMessage(content: string): Promise<void>
  connectWebSocket(): void
  disconnectWebSocket(): void
  clearMessages(): void
  retryConnection(): void
}
```

### 2. FileSystem Store 資料模型

```typescript
interface FileSystemState {
  files: Map<string, VirtualFile>
  currentFile?: string
  unsavedChanges: Set<string>
}

interface VirtualFile {
  path: string
  content: string
  language: string
  lastModified: Date
  isReadonly: boolean
}

interface FileSystemActions {
  createFile(path: string, content: string): void
  updateFile(path: string, content: string): void
  deleteFile(path: string): void
  saveFile(path: string): Promise<void>
  loadProject(projectId: string): Promise<void>
}
```

### 3. Project Store 資料模型

```typescript
interface ProjectState {
  currentProject?: Project
  projects: Project[]
  isLoading: boolean
}

interface ProjectActions {
  loadProjects(): Promise<void>
  createProject(name: string, description?: string): Promise<Project>
  selectProject(projectId: string): Promise<void>
  deleteProject(projectId: string): Promise<void>
  updateProject(projectId: string, updates: Partial<Project>): Promise<void>
}
```

## Error Handling

### 1. 前端錯誤處理策略

```typescript
// 全域錯誤處理器
interface ErrorHandler {
  handleApiError(error: ApiError): void
  handleValidationError(error: ValidationError[]): void
  handleNetworkError(error: NetworkError): void
  showUserMessage(message: string, type: 'success' | 'error' | 'warning'): void
}

// 錯誤邊界組件
interface ErrorBoundaryProps {
  fallback?: Component
  onError?: (error: Error, errorInfo: ErrorInfo) => void
}
```

### 2. WebSocket 錯誤處理

```typescript
interface WebSocketErrorHandler {
  onConnectionError(): void
  onMessageError(error: MessageError): void
  onReconnectAttempt(attempt: number): void
  onMaxReconnectAttemptsReached(): void
}
```

### 3. 編譯錯誤處理

```typescript
interface CompileErrorHandler {
  handleSyntaxError(error: SyntaxError): void
  handleTypeError(error: TypeError): void
  handleRuntimeError(error: RuntimeError): void
  displayErrorInEditor(error: CompileError): void
}
```

## Testing Strategy

### 1. 單元測試 (Vitest + Vue Test Utils)

**測試範圍**:
- 所有 Vue 組件的渲染和互動
- Pinia stores 的狀態管理邏輯
- 工具函數和組合式函數
- API 客戶端功能

**測試檔案結構**:
```
tests/unit/
├── components/
│   ├── chat/
│   │   └── ChatInterface.test.ts
│   ├── editor/
│   │   ├── CodeEditor.test.ts
│   │   └── FileTree.test.ts
│   └── preview/
│       └── PreviewFrame.test.ts
├── stores/
│   ├── chat.test.ts
│   ├── file-system.test.ts
│   └── project.test.ts
└── lib/
    ├── api.test.ts
    └── file-system.test.ts
```

### 2. 整合測試

**測試範圍**:
- 前後端 API 整合
- WebSocket 通訊
- 資料庫操作
- AI 服務整合

**測試檔案結構**:
```
tests/integration/
├── api/
│   ├── projects.test.ts
│   ├── files.test.ts
│   └── chat.test.ts
├── websocket/
│   └── chat-stream.test.ts
└── database/
    └── crud-operations.test.ts
```

### 3. 端對端測試 (Playwright)

**測試場景**:
- 完整的使用者工作流程
- 跨瀏覽器相容性
- 響應式設計驗證
- 效能測試

**測試檔案結構**:
```
tests/e2e/
├── user-workflows/
│   ├── create-project.spec.ts
│   ├── chat-with-ai.spec.ts
│   ├── edit-and-preview.spec.ts
│   └── file-management.spec.ts
├── responsive/
│   └── mobile-layout.spec.ts
└── performance/
    └── load-time.spec.ts
```

### 4. 測試工具配置

```typescript
// vitest.config.ts
export default defineConfig({
  test: {
    environment: 'jsdom',
    setupFiles: ['./tests/setup.ts'],
    coverage: {
      reporter: ['text', 'html', 'lcov'],
      threshold: {
        global: {
          branches: 80,
          functions: 80,
          lines: 80,
          statements: 80
        }
      }
    }
  }
})

// playwright.config.ts
export default defineConfig({
  testDir: './tests/e2e',
  use: {
    baseURL: 'http://localhost:5173',
    trace: 'on-first-retry',
    screenshot: 'only-on-failure'
  },
  projects: [
    { name: 'chromium', use: { ...devices['Desktop Chrome'] } },
    { name: 'firefox', use: { ...devices['Desktop Firefox'] } },
    { name: 'webkit', use: { ...devices['Desktop Safari'] } },
    { name: 'mobile', use: { ...devices['iPhone 12'] } }
  ]
})
```

## Implementation Approach

### 1. 開發順序

1. **介面設計階段**: 定義所有組件的 TypeScript 介面
2. **測試編寫階段**: 根據需求編寫單元測試和整合測試
3. **組件實作階段**: 實作 Vue 組件，確保通過測試
4. **整合測試階段**: 執行端對端測試，驗證完整功能
5. **優化階段**: 效能優化和使用者體驗改善

### 2. 技術實作細節

**Monaco Editor 整合**:
```typescript
// 使用 @monaco-editor/loader 動態載入
import loader from '@monaco-editor/loader'

const initializeMonaco = async () => {
  const monaco = await loader.init()
  
  // 設定 Vue 語言支援
  monaco.languages.register({ id: 'vue' })
  monaco.languages.setMonarchTokensProvider('vue', vueLanguageDefinition)
  
  // 設定 TypeScript 編譯選項
  monaco.languages.typescript.typescriptDefaults.setCompilerOptions({
    target: monaco.languages.typescript.ScriptTarget.ES2020,
    allowNonTsExtensions: true,
    moduleResolution: monaco.languages.typescript.ModuleResolutionKind.NodeJs
  })
}
```

**Vue SFC 編譯**:
```typescript
// 使用 @vue/compiler-sfc 動態編譯
import { compileTemplate, compileScript, compileStyle } from '@vue/compiler-sfc'

const compileVueComponent = async (source: string, filename: string) => {
  const { descriptor } = parse(source, { filename })
  
  const compiledScript = compileScript(descriptor, { id: filename })
  const compiledTemplate = compileTemplate({
    source: descriptor.template?.content || '',
    filename,
    id: filename
  })
  
  const compiledStyles = descriptor.styles.map(style => 
    compileStyle({
      source: style.content,
      filename,
      id: filename,
      scoped: style.scoped
    })
  )
  
  return {
    script: compiledScript.content,
    template: compiledTemplate.code,
    styles: compiledStyles.map(s => s.code)
  }
}
```

### 3. 響應式設計實作

```scss
// 使用 Tailwind CSS 響應式工具類
.chat-interface {
  @apply flex flex-col h-full;
  
  // 桌面版佈局
  @screen lg {
    @apply flex-row;
  }
  
  // 平板版佈局
  @screen md {
    .sidebar {
      @apply w-64;
    }
  }
  
  // 手機版佈局
  @screen sm {
    .sidebar {
      @apply fixed inset-y-0 left-0 z-50 w-full transform transition-transform;
      
      &.hidden {
        @apply -translate-x-full;
      }
    }
  }
}
```

## Performance Considerations

### 1. 程式碼分割

```typescript
// 路由層級的程式碼分割
const routes = [
  {
    path: '/',
    component: () => import('../views/Home.vue')
  },
  {
    path: '/chat',
    component: () => import('../views/Chat.vue')
  },
  {
    path: '/project/:id',
    component: () => import('../views/Project.vue')
  }
]
```

### 2. 虛擬滾動

```typescript
// 對於大量聊天訊息使用虛擬滾動
import { VirtualList } from '@tanstack/vue-virtual'

const ChatMessageList = defineComponent({
  setup() {
    const messages = ref<ChatMessage[]>([])
    
    return {
      messages,
      estimateSize: () => 60, // 預估每個訊息高度
      getItemKey: (index: number) => messages.value[index].id
    }
  }
})
```

### 3. 快取策略

```typescript
// API 回應快取
const apiCache = new Map<string, { data: any, timestamp: number }>()

const cachedApiCall = async (url: string, ttl = 300000) => {
  const cached = apiCache.get(url)
  if (cached && Date.now() - cached.timestamp < ttl) {
    return cached.data
  }
  
  const data = await fetch(url).then(r => r.json())
  apiCache.set(url, { data, timestamp: Date.now() })
  return data
}
```

這個設計文件提供了完整的技術架構和實作指導，遵循 KISS 原則，確保每個組件都有清晰的職責和介面定義，並採用測試驅動開發方法確保程式碼品質。