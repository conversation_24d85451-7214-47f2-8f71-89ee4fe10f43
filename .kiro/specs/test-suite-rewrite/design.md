# Design Document

## Overview

本設計文件詳細說明 UIGen Vue 專案測試套件重寫的技術架構和實作方案。設計採用分層架構，從測試基礎設施開始，逐步建立穩定的測試環境、工具函數、以及各類型測試的實作方案。

## Architecture

### 測試架構層級

```
┌─────────────────────────────────────────────────────────────┐
│                    CI/CD Integration Layer                   │
├─────────────────────────────────────────────────────────────┤
│                    Test Reporting Layer                     │
├─────────────────────────────────────────────────────────────┤
│  E2E Tests  │  Integration Tests  │  Unit Tests  │ Server Tests │
├─────────────────────────────────────────────────────────────┤
│                    Test Utilities Layer                     │
├─────────────────────────────────────────────────────────────┤
│                Test Environment Layer                       │
├─────────────────────────────────────────────────────────────┤
│                 Test Infrastructure                         │
└─────────────────────────────────────────────────────────────┘
```

### 核心組件設計

1. **Test Infrastructure**: 基礎測試配置和環境設置
2. **Test Environment Layer**: 不同測試類型的環境配置
3. **Test Utilities Layer**: 共用的測試工具和輔助函數
4. **Test Execution Layer**: 各類型測試的具體實作
5. **Test Reporting Layer**: 測試結果收集和報告生成
6. **CI/CD Integration**: 持續整合支援

## Components and Interfaces

### 1. Test Configuration System

#### Vitest 配置架構
```typescript
// vitest.config.ts - 主配置檔案
interface VitestConfig {
  test: {
    environment: 'node' | 'jsdom' | 'happy-dom'
    globals: boolean
    setupFiles: string[]
    coverage: CoverageConfig
    testTimeout: number
    hookTimeout: number
  }
  resolve: {
    alias: Record<string, string>
  }
}

// 分環境配置
interface EnvironmentConfig {
  server: VitestConfig    // Node.js 環境用於 server 測試
  frontend: VitestConfig  // jsdom 環境用於前端測試
  integration: VitestConfig // 整合測試配置
}
```

#### 配置檔案結構
```
tests/
├── config/
│   ├── vitest.config.server.ts     # Server 測試配置
│   ├── vitest.config.frontend.ts   # 前端測試配置
│   ├── vitest.config.integration.ts # 整合測試配置
│   └── vitest.config.e2e.ts        # E2E 測試配置
├── setup/
│   ├── server-setup.ts             # Server 測試環境設置
│   ├── frontend-setup.ts           # 前端測試環境設置
│   ├── database-setup.ts           # 資料庫測試設置
│   └── global-setup.ts             # 全域測試設置
└── utils/
    ├── test-helpers.ts              # 通用測試輔助函數
    ├── mock-factories.ts           # Mock 物件工廠
    ├── database-utils.ts           # 資料庫測試工具
    └── api-utils.ts                # API 測試工具
```

### 2. Database Testing System

#### 資料庫測試管理器
```typescript
interface DatabaseTestManager {
  // 測試資料庫初始化
  initializeTestDatabase(): Promise<void>
  
  // 測試資料清理
  cleanupTestData(): Promise<void>
  
  // 建立測試資料
  createTestData<T>(entity: string, data: Partial<T>): Promise<T>
  
  // 事務管理
  withTransaction<T>(callback: () => Promise<T>): Promise<T>
  
  // 資料庫狀態重置
  resetDatabase(): Promise<void>
}

// 測試資料工廠
interface TestDataFactory {
  createProject(overrides?: Partial<Project>): Promise<Project>
  createFile(projectId: string, overrides?: Partial<File>): Promise<File>
  createUser(overrides?: Partial<User>): Promise<User>
  
  // 批量建立
  createMultipleProjects(count: number): Promise<Project[]>
  createProjectWithFiles(fileCount: number): Promise<{project: Project, files: File[]}>
}
```

### 3. API Testing Framework

#### API 測試客戶端
```typescript
interface APITestClient {
  // HTTP 客戶端
  get<T>(url: string, options?: RequestOptions): Promise<APIResponse<T>>
  post<T>(url: string, data: any, options?: RequestOptions): Promise<APIResponse<T>>
  put<T>(url: string, data: any, options?: RequestOptions): Promise<APIResponse<T>>
  delete<T>(url: string, options?: RequestOptions): Promise<APIResponse<T>>
  
  // 測試伺服器管理
  startTestServer(): Promise<void>
  stopTestServer(): Promise<void>
  
  // 認證管理
  setAuthToken(token: string): void
  clearAuth(): void
}

// API 回應介面
interface APIResponse<T> {
  status: number
  statusText: string
  data: T
  headers: Record<string, string>
}
```

#### 測試伺服器設置
```typescript
interface TestServerConfig {
  port: number
  database: {
    url: string
    resetOnStart: boolean
  }
  middleware: {
    cors: boolean
    logging: boolean
    errorHandling: boolean
  }
  routes: string[]
}

class TestServerManager {
  private server: Express.Application
  private httpServer: Server
  
  async start(config: TestServerConfig): Promise<void>
  async stop(): Promise<void>
  async reset(): Promise<void>
  
  getBaseURL(): string
  isRunning(): boolean
}
```

### 4. WebSocket Testing System

#### WebSocket Mock 系統
```typescript
interface MockWebSocket {
  // 連接狀態模擬
  connect(): void
  disconnect(code?: number, reason?: string): void
  
  // 訊息處理
  send(data: string | ArrayBuffer): void
  receive(data: string | ArrayBuffer): void
  
  // 事件監聽
  on(event: 'open' | 'close' | 'message' | 'error', handler: Function): void
  off(event: string, handler: Function): void
  
  // 狀態查詢
  readyState: number
  url: string
}

// WebSocket 測試工具
interface WebSocketTestUtils {
  createMockWebSocket(url: string): MockWebSocket
  simulateConnectionError(): void
  simulateNetworkDelay(ms: number): void
  
  // 訊息驗證
  expectMessage(expectedData: any): Promise<void>
  expectConnectionState(state: number): void
  
  // 場景模擬
  simulateReconnection(): Promise<void>
  simulateServerError(): void
}
```

### 5. Test Utilities and Helpers

#### 通用測試工具
```typescript
interface TestHelpers {
  // 等待工具
  waitFor(condition: () => boolean, timeout?: number): Promise<void>
  waitForElement(selector: string, timeout?: number): Promise<Element>
  
  // 時間控制
  advanceTimers(ms: number): void
  runAllTimers(): void
  
  // 隨機資料生成
  generateRandomString(length: number): string
  generateRandomEmail(): string
  generateRandomId(): string
  
  // 檔案操作
  createTempFile(content: string): Promise<string>
  cleanupTempFiles(): Promise<void>
}

// Mock 工廠
interface MockFactory {
  createMockAIProvider(): MockAIProvider
  createMockFileSystem(): MockFileSystem
  createMockWebSocket(): MockWebSocket
  createMockDatabase(): MockDatabase
  
  // 資料 Mock
  createMockProject(): Project
  createMockFile(): File
  createMockUser(): User
}
```

### 6. Test Reporting System

#### 測試報告生成器
```typescript
interface TestReporter {
  // 報告生成
  generateReport(results: TestResults): Promise<TestReport>
  generateCoverageReport(coverage: CoverageData): Promise<CoverageReport>
  
  // 輸出格式
  exportToHTML(report: TestReport): Promise<string>
  exportToJSON(report: TestReport): Promise<string>
  exportToXML(report: TestReport): Promise<string>
  
  // CI 整合
  generateJUnitReport(results: TestResults): Promise<string>
  generateCoverageXML(coverage: CoverageData): Promise<string>
}

// 測試結果資料結構
interface TestResults {
  summary: {
    total: number
    passed: number
    failed: number
    skipped: number
    duration: number
  }
  suites: TestSuite[]
  coverage: CoverageData
}
```

## Data Models

### 測試配置模型
```typescript
interface TestConfig {
  environment: 'development' | 'test' | 'ci'
  database: {
    url: string
    resetBetweenTests: boolean
    seedData: boolean
  }
  server: {
    port: number
    host: string
    timeout: number
  }
  coverage: {
    threshold: number
    include: string[]
    exclude: string[]
  }
}

interface TestSuite {
  name: string
  file: string
  tests: TestCase[]
  duration: number
  status: 'passed' | 'failed' | 'skipped'
}

interface TestCase {
  name: string
  status: 'passed' | 'failed' | 'skipped'
  duration: number
  error?: TestError
  assertions: number
}
```

### Mock 資料模型
```typescript
interface MockData {
  projects: MockProject[]
  files: MockFile[]
  users: MockUser[]
  aiResponses: MockAIResponse[]
}

interface MockProject {
  id: string
  name: string
  description?: string
  createdAt: Date
  updatedAt: Date
  files?: MockFile[]
}

interface MockFile {
  id: string
  name: string
  content: string
  projectId: string
  createdAt: Date
  updatedAt: Date
}
```

## Error Handling

### 測試錯誤分類
```typescript
enum TestErrorType {
  ENVIRONMENT_ERROR = 'environment_error',
  DATABASE_ERROR = 'database_error',
  API_ERROR = 'api_error',
  WEBSOCKET_ERROR = 'websocket_error',
  ASSERTION_ERROR = 'assertion_error',
  TIMEOUT_ERROR = 'timeout_error',
  SETUP_ERROR = 'setup_error',
  TEARDOWN_ERROR = 'teardown_error'
}

interface TestError {
  type: TestErrorType
  message: string
  stack?: string
  context?: Record<string, any>
  suggestions?: string[]
}
```

### 錯誤處理策略
1. **環境錯誤**: 提供詳細的配置檢查和修復建議
2. **資料庫錯誤**: 自動重試和資料清理
3. **API 錯誤**: 請求/回應日誌和狀態碼分析
4. **WebSocket 錯誤**: 連接狀態追蹤和重連機制
5. **超時錯誤**: 動態超時調整和效能分析

## Testing Strategy

### 測試分層策略
1. **Unit Tests (70%)**
   - 個別函數和組件測試
   - Mock 外部依賴
   - 快速執行，高覆蓋率

2. **Integration Tests (20%)**
   - 組件間互動測試
   - 資料庫和 API 整合
   - 真實環境模擬

3. **E2E Tests (10%)**
   - 完整使用者流程
   - 跨瀏覽器測試
   - 關鍵功能驗證

### 測試執行策略
```typescript
interface TestExecutionPlan {
  phases: {
    setup: TestPhase
    unit: TestPhase
    integration: TestPhase
    e2e: TestPhase
    cleanup: TestPhase
  }
  
  parallel: {
    maxWorkers: number
    isolation: boolean
  }
  
  retry: {
    attempts: number
    conditions: string[]
  }
}
```

### 效能最佳化
1. **並行執行**: 獨立測試並行運行
2. **資源重用**: 測試伺服器和資料庫連接重用
3. **智能快取**: 編譯結果和依賴快取
4. **增量測試**: 只執行變更相關的測試
5. **資源清理**: 及時釋放測試資源

## Implementation Phases

### Phase 1: Infrastructure Setup
- 建立測試配置系統
- 設置環境分離
- 實作基礎工具函數

### Phase 2: Database Testing
- 建立資料庫測試管理器
- 實作測試資料工廠
- 修復外鍵約束問題

### Phase 3: API Testing Framework
- 建立 API 測試客戶端
- 實作測試伺服器管理
- 修復路由測試問題

### Phase 4: WebSocket Testing
- 實作 WebSocket Mock 系統
- 重寫連接和訊息測試
- 修復超時問題

### Phase 5: Test Suite Rewrite
- 重寫 server 測試
- 修復整合測試
- 更新單元測試

### Phase 6: Reporting and CI
- 實作測試報告系統
- 設置 CI/CD 整合
- 效能最佳化