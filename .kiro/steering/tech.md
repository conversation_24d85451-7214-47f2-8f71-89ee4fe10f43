# 技術堆疊

## 前端技術

- **Vue 3**: 漸進式 JavaScript 框架，使用 Composition API
- **TypeScript**: 類型安全的 JavaScript 超集
- **Vite**: 快速的建置工具和開發伺服器
- **Pinia**: Vue 官方狀態管理庫
- **Vue Router 4**: 官方路由解決方案
- **Tailwind CSS v4**: 實用優先的 CSS 框架

## 後端技術

- **Express.js**: Node.js Web 框架
- **Prisma**: 現代資料庫工具包
- **SQLite**: 輕量級關聯式資料庫
- **WebSocket**: 即時通訊支援
- **JWT**: JSON Web Token 認證

## AI 整合

- **Anthropic Claude** (claude-3-5-sonnet-20241022) - 首選
- **OpenAI GPT-4** (gpt-4o) - 備選方案 1  
- **Google Gemini** (gemini-1.5-pro) - 備選方案 2
- **模擬提供者** - 本地開發模式

## 測試框架

- **Vitest**: 快速的單元測試框架
- **Vue Test Utils**: Vue 組件測試工具
- **jsdom**: DOM 環境模擬

## 開發工具

- **ESLint**: 程式碼品質檢查
- **Prettier**: 程式碼格式化
- **TypeScript**: 靜態類型檢查

## 常用指令

### 開發
```bash
npm run dev              # 同時啟動前後端開發伺服器
npm run dev:frontend     # 僅啟動 Vue 前端
npm run dev:backend      # 僅啟動 Express 後端
```

### 建置
```bash
npm run build            # 建置前端生產版本
npm run build:backend    # 建置後端生產版本
```

### 測試
```bash
npm run test            # 執行所有測試
npm run test:frontend   # 執行前端測試
npm run test:backend    # 執行後端測試
npm run test:coverage   # 執行測試覆蓋率報告
```

### 程式碼品質
```bash
npm run lint            # 執行 ESLint 檢查並修復
npm run lint:check      # 僅檢查不修復
npm run format          # 格式化程式碼
npm run format:check    # 檢查格式化狀態
```

### 資料庫
```bash
npm run setup           # 完整專案設定
npm run db:reset        # 重置資料庫
```

## 環境需求

- Node.js 18+
- npm 或 yarn
- 至少一個 AI 提供者的 API 金鑰（可選，有模擬模式）