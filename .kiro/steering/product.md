# 產品概述

UIGen Vue 是一個 AI 驅動的 Vue 組件生成器，具有即時預覽功能。這是從 Next.js/React 版本移植而來的 Vue 3 實現。

## 核心功能

- **AI 組件生成**: 透過自然語言描述生成 Vue 3 組件
- **即時預覽**: 動態編譯和預覽生成的 Vue 組件  
- **智能聊天介面**: 透過自然語言描述來生成和修改組件
- **虛擬檔案系統**: 記憶體內檔案管理，支援多檔案專案
- **多 AI 提供者支援**: 支援 Anthropic Claude、OpenAI GPT-4、Google Gemini，並提供模擬模式

## 目標使用者

- Vue.js 開發者
- UI/UX 設計師
- 前端工程師
- 需要快速原型開發的團隊

## 核心價值主張

讓開發者能夠透過自然語言快速生成高品質的 Vue 組件，大幅提升開發效率和創意實現速度。