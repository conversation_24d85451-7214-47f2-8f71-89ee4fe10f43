import { describe, it, expect, beforeEach, afterEach } from 'vitest'
import { DatabaseTestManager } from '../utils/database-test-manager'
import { TestDataFactory } from '../utils/test-data-factory'

describe('TestDataFactory', () => {
  let databaseManager: DatabaseTestManager
  let testDataFactory: TestDataFactory

  beforeEach(async () => {
    databaseManager = new DatabaseTestManager()
    await databaseManager.initializeTestDatabase()
    testDataFactory = new TestDataFactory(databaseManager.getPrismaClient())
  })

  afterEach(async () => {
    await testDataFactory.cleanup()
    await databaseManager.cleanup()
  })

  describe('Project Creation', () => {
    it('should create a single project with default data', async () => {
      const project = await testDataFactory.createProject()

      expect(project).toBeDefined()
      expect(project.id).toBeDefined()
      expect(project.name).toBeDefined()
      expect(project.description).toBeDefined()
      expect(project.createdAt).toBeInstanceOf(Date)
      expect(project.updatedAt).toBeInstanceOf(Date)
    })

    it('should create project with custom overrides', async () => {
      const customData = {
        name: 'Custom Project Name',
        description: 'Custom project description'
      }

      const project = await testDataFactory.createProject({
        overrides: customData
      })

      expect(project.name).toBe(customData.name)
      expect(project.description).toBe(customData.description)
    })

    it('should create multiple projects', async () => {
      const projects = await testDataFactory.createProjects(3)

      expect(projects).toHaveLength(3)
      projects.forEach((project, index) => {
        expect(project.id).toBeDefined()
        expect(project.name).toContain(`Test Project ${index + 1}`)
      })
    })

    it('should track created projects for cleanup', async () => {
      await testDataFactory.createProjects(2)

      const stats = testDataFactory.getStatistics()
      expect(stats.project).toBe(2)

      // Verify projects exist in database
      const counts = await databaseManager.getTableCounts()
      expect(counts.projects).toBe(2)
    })
  })

  describe('File Creation', () => {
    it('should create file with project relationship', async () => {
      const project = await testDataFactory.createProject()
      const file = await testDataFactory.createFile(project.id)

      expect(file).toBeDefined()
      expect(file.id).toBeDefined()
      expect(file.name).toBeDefined()
      expect(file.content).toBeDefined()
      expect(file.projectId).toBe(project.id)
      expect(file.createdAt).toBeInstanceOf(Date)
      expect(file.updatedAt).toBeInstanceOf(Date)
    })

    it('should create file with custom data', async () => {
      const project = await testDataFactory.createProject()
      const customFileData = {
        name: 'custom.js',
        content: 'console.log("Custom content");'
      }

      const file = await testDataFactory.createFile(project.id, {
        overrides: customFileData
      })

      expect(file.name).toBe(customFileData.name)
      expect(file.content).toBe(customFileData.content)
    })

    it('should create multiple files for a project', async () => {
      const project = await testDataFactory.createProject()
      const files = await testDataFactory.createFiles(project.id, 5)

      expect(files).toHaveLength(5)
      files.forEach((file, index) => {
        expect(file.projectId).toBe(project.id)
        expect(file.name).toContain(`test-file-${index + 1}`)
      })
    })

    it('should generate realistic file content based on extension', async () => {
      const project = await testDataFactory.createProject()
      
      const jsFile = await testDataFactory.createFile(project.id, {
        overrides: { name: 'test.js' }
      })
      
      const cssFile = await testDataFactory.createFile(project.id, {
        overrides: { name: 'styles.css' }
      })

      expect(jsFile.content).toContain('function')
      expect(cssFile.content).toContain('{')
      expect(cssFile.content).toContain('color:')
    })
  })

  describe('Data Set Creation', () => {
    it('should create complete data set with relationships', async () => {
      const relations = {
        projects: 2,
        files: 4,
        withRelations: true
      }

      const batch = await testDataFactory.createDataSet(relations)

      expect(batch.projects).toHaveLength(2)
      expect(batch.files).toHaveLength(4)
      expect(batch.metadata.totalRecords).toBe(6)
      expect(batch.metadata.relations).toEqual(relations)

      // Verify relationships
      batch.files.forEach(file => {
        const parentProject = batch.projects.find(p => p.id === file.projectId)
        expect(parentProject).toBeDefined()
      })
    })

    it('should distribute files evenly across projects', async () => {
      const batch = await testDataFactory.createDataSet({
        projects: 2,
        files: 6
      })

      const project1Files = batch.files.filter(f => f.projectId === batch.projects[0].id)
      const project2Files = batch.files.filter(f => f.projectId === batch.projects[1].id)

      expect(project1Files.length).toBe(3)
      expect(project2Files.length).toBe(3)
    })

    it('should handle empty relations gracefully', async () => {
      const batch = await testDataFactory.createDataSet({})

      expect(batch.projects).toHaveLength(0)
      expect(batch.files).toHaveLength(0)
      expect(batch.metadata.totalRecords).toBe(0)
    })
  })

  describe('Project Structure Creation', () => {
    it('should create realistic project structure', async () => {
      const structure = await testDataFactory.createProjectStructure()

      expect(structure.project).toBeDefined()
      expect(structure.files).toHaveLength(6) // Based on the predefined structure

      // Check for expected files
      const fileNames = structure.files.map(f => f.name)
      expect(fileNames).toContain('package.json')
      expect(fileNames).toContain('README.md')
      expect(fileNames).toContain('src/index.js')
      expect(fileNames).toContain('src/components/App.jsx')
      expect(fileNames).toContain('src/styles/main.css')
      expect(fileNames).toContain('tests/app.test.js')

      // Verify content types
      const packageJson = structure.files.find(f => f.name === 'package.json')
      expect(packageJson?.content).toContain('"name"')
      expect(packageJson?.content).toContain('"version"')

      const readme = structure.files.find(f => f.name === 'README.md')
      expect(readme?.content).toContain('# ')
      expect(readme?.content).toContain('## Installation')
    })

    it('should create project structure with custom overrides', async () => {
      const customData = {
        name: 'My Custom App',
        description: 'A custom application'
      }

      const structure = await testDataFactory.createProjectStructure({
        overrides: customData
      })

      expect(structure.project.name).toBe(customData.name)
      expect(structure.project.description).toBe(customData.description)
    })
  })

  describe('Cleanup and Statistics', () => {
    it('should track statistics correctly', async () => {
      await testDataFactory.createProjects(3)
      const project = await testDataFactory.createProject()
      await testDataFactory.createFiles(project.id, 5)

      const stats = testDataFactory.getStatistics()
      expect(stats.project).toBe(4)
      expect(stats.file).toBe(5)
    })

    it('should cleanup all created records', async () => {
      await testDataFactory.createProjects(2)
      const project = await testDataFactory.createProject()
      await testDataFactory.createFiles(project.id, 3)

      // Verify data exists
      let counts = await databaseManager.getTableCounts()
      expect(counts.projects).toBe(3)
      expect(counts.files).toBe(3)

      // Cleanup
      await testDataFactory.cleanup()

      // Verify data is removed
      counts = await databaseManager.getTableCounts()
      expect(counts.projects).toBe(0)
      expect(counts.files).toBe(0)

      // Verify statistics are reset
      const stats = testDataFactory.getStatistics()
      expect(Object.keys(stats)).toHaveLength(0)
    })

    it('should reset factory state', async () => {
      await testDataFactory.createProject()
      
      const initialStats = testDataFactory.getStatistics()
      expect(initialStats.project).toBe(1)

      testDataFactory.reset()

      const resetStats = testDataFactory.getStatistics()
      expect(Object.keys(resetStats)).toHaveLength(0)
    })
  })

  describe('Content Generation', () => {
    it('should generate valid JavaScript content', async () => {
      const project = await testDataFactory.createProject()
      const jsFile = await testDataFactory.createFile(project.id, {
        overrides: { name: 'test.js' }
      })

      expect(jsFile.content).toContain('function')
      expect(jsFile.content).toContain('export default')
      expect(jsFile.content).toMatch(/console\.log\(['"][^'"]*['"]\)/)
    })

    it('should generate valid React component content', async () => {
      const project = await testDataFactory.createProject()
      const reactFile = await testDataFactory.createFile(project.id, {
        overrides: { name: 'Component.jsx' }
      })

      expect(reactFile.content).toContain('import React')
      expect(reactFile.content).toContain('const ')
      expect(reactFile.content).toContain('return (')
      expect(reactFile.content).toContain('export default')
    })

    it('should generate valid CSS content', async () => {
      const project = await testDataFactory.createProject()
      const cssFile = await testDataFactory.createFile(project.id, {
        overrides: { name: 'styles.css' }
      })

      expect(cssFile.content).toMatch(/\.[a-zA-Z-]+ \{/)
      expect(cssFile.content).toContain('color:')
      expect(cssFile.content).toContain('background-color:')
      expect(cssFile.content).toContain('padding:')
    })

    it('should generate valid JSON content', async () => {
      const project = await testDataFactory.createProject()
      const jsonFile = await testDataFactory.createFile(project.id, {
        overrides: { name: 'config.json' }
      })

      expect(() => JSON.parse(jsonFile.content)).not.toThrow()
      
      const parsed = JSON.parse(jsonFile.content)
      expect(parsed).toHaveProperty('name')
      expect(parsed).toHaveProperty('version')
    })

    it('should generate valid HTML content', async () => {
      const project = await testDataFactory.createProject()
      const htmlFile = await testDataFactory.createFile(project.id, {
        overrides: { name: 'index.html' }
      })

      expect(htmlFile.content).toContain('<!DOCTYPE html>')
      expect(htmlFile.content).toContain('<html lang="en">')
      expect(htmlFile.content).toContain('<head>')
      expect(htmlFile.content).toContain('<body>')
    })

    it('should generate valid Markdown content', async () => {
      const project = await testDataFactory.createProject()
      const mdFile = await testDataFactory.createFile(project.id, {
        overrides: { name: 'README.md' }
      })

      expect(mdFile.content).toMatch(/^# /)
      expect(mdFile.content).toContain('## ')
      expect(mdFile.content).toContain('### ')
      expect(mdFile.content).toMatch(/^- /m)
    })
  })

  describe('Error Handling', () => {
    it('should handle invalid project ID for file creation', async () => {
      await expect(
        testDataFactory.createFile('invalid-project-id')
      ).rejects.toThrow()
    })

    it('should handle cleanup errors gracefully', async () => {
      // Create some data
      await testDataFactory.createProject()
      
      // Mock prisma to simulate error
      const mockPrisma = {
        file: {
          deleteMany: vi.fn().mockRejectedValue(new Error('Delete failed'))
        },
        project: {
          deleteMany: vi.fn()
        }
      }
      
      ;(testDataFactory as any).prisma = mockPrisma

      await expect(testDataFactory.cleanup()).rejects.toThrow('Delete failed')
    })
  })
})