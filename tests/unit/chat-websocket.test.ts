import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { setActive<PERSON><PERSON>, create<PERSON><PERSON> } from 'pinia';
import { MockWebSocket } from '../utils/mock-websocket'
import { WebSocketTestUtils } from '../utils/websocket-test-utils'

// Mock the global WebSocket
global.WebSocket = MockWebSocket as any

// Import the actual chat store
import { useChatStore, type ChatMessage, type ConnectionStatus } from '../../src/stores/chat';

// Chat WebSocket Store implementation (simplified for testing)
class ChatWebSocketStore {
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { MockWebSocket } from '../utils/mock-websocket'
import { WebSocketTestUtils } from '../utils/websocket-test-utils'

// Mock the global WebSocket
global.WebSocket = MockWebSocket as any

// Chat WebSocket Store implementation (simplified for testing)
class ChatWebSocketStore {
  private ws: WebSocket | null = null
  private url: string
  private reconnectAttempts = 0
  private maxReconnectAttempts = 3
  private reconnectDelay = 1000
  private heartbeatInterval: NodeJS.Timeout | null = null
  private heartbeatTimeout: NodeJS.Timeout | null = null
  private isConnecting = false
  private isReconnecting = false
  
  // State
  public connectionState: 'disconnected' | 'connecting' | 'connected' | 'reconnecting' = 'disconnected'
  public messages: any[] = []
  public lastHeartbeat: Date | null = null
  public connectionError: string | null = null
  
  // Event handlers
  public onMessage: ((message: any) => void) | null = null
  public onConnectionChange: ((state: string) => void) | null = null
  public onError: ((error: string) => void) | null = null

  constructor(url: string) {
    this.url = url
  }

  // Connection management
  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.isConnecting || this.connectionState === 'connected') {
        resolve()
        return
      }

      this.isConnecting = true
      this.connectionState = 'connecting'
      this.connectionError = null
      this.onConnectionChange?.(this.connectionState)

      try {
        this.ws = new WebSocket(this.url)
        
        this.ws.onopen = () => {
          this.isConnecting = false
          this.connectionState = 'connected'
          this.reconnectAttempts = 0
          this.onConnectionChange?.(this.connectionState)
          this.startHeartbeat()
          resolve()
        }

        this.ws.onmessage = (event) => {
          const message = JSON.parse(event.data)
          
          if (message.type === 'pong') {
            this.handlePong()
          } else {
            this.messages.push(message)
            this.onMessage?.(message)
          }
        }

        this.ws.onclose = (event) => {
          this.handleDisconnection(event.code, event.reason)
        }

        this.ws.onerror = (event) => {
          this.isConnecting = false
          const error = 'WebSocket connection error'
          this.connectionError = error
          this.onError?.(error)
          reject(new Error(error))
        }

      } catch (error) {
        this.isConnecting = false
        this.connectionError = error.message
        this.onError?.(error.message)
        reject(error)
      }
    })
  }

  disconnect(): void {
    this.stopHeartbeat()
    
    if (this.ws) {
      this.ws.close(1000, 'Normal closure')
      this.ws = null
    }
    
    this.connectionState = 'disconnected'
    this.onConnectionChange?.(this.connectionState)
  }

  // Message handling
  sendMessage(message: any): boolean {
    if (this.connectionState !== 'connected' || !this.ws) {
      return false
    }

    try {
      this.ws.send(JSON.stringify(message))
      return true
    } catch (error) {
      this.onError?.(`Failed to send message: ${error.message}`)
      return false
    }
  }

  // Heartbeat mechanism
  private startHeartbeat(): void {
    this.stopHeartbeat()
    
    this.heartbeatInterval = setInterval(() => {
      if (this.connectionState === 'connected' && this.ws) {
        this.sendPing()
      }
    }, 30000) // 30 seconds
  }

  private stopHeartbeat(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval)
      this.heartbeatInterval = null
    }
    
    if (this.heartbeatTimeout) {
      clearTimeout(this.heartbeatTimeout)
      this.heartbeatTimeout = null
    }
  }

  private sendPing(): void {
    if (this.ws && this.connectionState === 'connected') {
      this.ws.send(JSON.stringify({ type: 'ping', timestamp: Date.now() }))
      this.lastHeartbeat = new Date()
      
      // Set timeout for pong response
      this.heartbeatTimeout = setTimeout(() => {
        this.handleHeartbeatTimeout()
      }, 10000) // 10 seconds timeout
    }
  }

  private handlePong(): void {
    if (this.heartbeatTimeout) {
      clearTimeout(this.heartbeatTimeout)
      this.heartbeatTimeout = null
    }
  }

  private handleHeartbeatTimeout(): void {
    this.onError?.('Heartbeat timeout - connection may be lost')
    this.handleDisconnection(1006, 'Heartbeat timeout')
  }

  // Reconnection logic
  private handleDisconnection(code: number, reason: string): void {
    this.stopHeartbeat()
    this.ws = null
    
    if (code === 1000) {
      // Normal closure
      this.connectionState = 'disconnected'
      this.onConnectionChange?.(this.connectionState)
      return
    }

    // Abnormal closure - attempt reconnection
    if (this.reconnectAttempts < this.maxReconnectAttempts && !this.isReconnecting) {
      this.attemptReconnection()
    } else {
      this.connectionState = 'disconnected'
      this.connectionError = `Connection lost: ${reason} (code: ${code})`
      this.onConnectionChange?.(this.connectionState)
      this.onError?.(this.connectionError)
    }
  }

  private attemptReconnection(): void {
    this.isReconnecting = true
    this.connectionState = 'reconnecting'
    this.onConnectionChange?.(this.connectionState)

    setTimeout(() => {
      this.reconnectAttempts++
      this.isReconnecting = false
      
      this.connect().catch(() => {
        // Reconnection failed, will try again if attempts remain
      })
    }, this.reconnectDelay * Math.pow(2, this.reconnectAttempts)) // Exponential backoff
  }

  // Utility methods
  getConnectionState(): string {
    return this.connectionState
  }

  getMessages(): any[] {
    return [...this.messages]
  }

  clearMessages(): void {
    this.messages = []
  }

  isConnected(): boolean {
    return this.connectionState === 'connected'
  }
}

describe('Chat WebSocket Store Unit Tests', () => {
  let chatStore: ChatWebSocketStore
  let wsTestUtils: WebSocketTestUtils
  let mockWebSocket: MockWebSocket

  beforeEach(() => {
    // Reset WebSocket mock
    vi.clearAllMocks()
    
    // Initialize test utilities
    wsTestUtils = new WebSocketTestUtils()
    
    // Create chat store instance
    chatStore = new ChatWebSocketStore('ws://localhost:8080/chat')
    
    // Get reference to mock WebSocket for direct manipulation
    mockWebSocket = null
  })

  afterEach(() => {
    if (chatStore) {
      chatStore.disconnect()
    }
    wsTestUtils.cleanup()
  })

  describe('Connection Management', () => {
    it('should initialize with disconnected state', () => {
      expect(chatStore.getConnectionState()).toBe('disconnected')
      expect(chatStore.isConnected()).toBe(false)
      expect(chatStore.getMessages()).toHaveLength(0)
    })

    it('should connect successfully', async () => {
      const connectionStates: string[] = []
      chatStore.onConnectionChange = (state) => connectionStates.push(state)

      const connectPromise = chatStore.connect()
      
      // Simulate successful connection
      await wsTestUtils.waitFor(() => chatStore.getConnectionState() === 'connecting', 1000)
      expect(connectionStates).toContain('connecting')
      
      // Trigger connection open
      const ws = wsTestUtils.getLastCreatedWebSocket()
      if (ws) {
        ws.simulateOpen()
      }
      
      await connectPromise
      
      expect(chatStore.getConnectionState()).toBe('connected')
      expect(chatStore.isConnected()).toBe(true)
      expect(connectionStates).toContain('connected')
    })

    it('should handle connection errors', async () => {
      const errors: string[] = []
      chatStore.onError = (error) => errors.push(error)

      const connectPromise = chatStore.connect()
      
      // Simulate connection error
      const ws = wsTestUtils.getLastCreatedWebSocket()
      if (ws) {
        ws.simulateError(new Error('Connection failed'))
      }
      
      await expect(connectPromise).rejects.toThrow('WebSocket connection error')
      expect(errors).toContain('WebSocket connection error')
      expect(chatStore.getConnectionState()).toBe('disconnected')
    })

    it('should disconnect gracefully', async () => {
      // First connect
      await chatStore.connect()
      const ws = wsTestUtils.getLastCreatedWebSocket()
      if (ws) {
        ws.simulateOpen()
      }
      
      await wsTestUtils.waitFor(() => chatStore.isConnected(), 1000)
      
      // Then disconnect
      const connectionStates: string[] = []
      chatStore.onConnectionChange = (state) => connectionStates.push(state)
      
      chatStore.disconnect()
      
      expect(chatStore.getConnectionState()).toBe('disconnected')
      expect(chatStore.isConnected()).toBe(false)
      expect(connectionStates).toContain('disconnected')
    })

    it('should prevent multiple simultaneous connections', async () => {
      const connectPromise1 = chatStore.connect()
      const connectPromise2 = chatStore.connect()
      
      const ws = wsTestUtils.getLastCreatedWebSocket()
      if (ws) {
        ws.simulateOpen()
      }
      
      await Promise.all([connectPromise1, connectPromise2])
      
      expect(chatStore.isConnected()).toBe(true)
      // Should only have one WebSocket instance
      expect(wsTestUtils.getCreatedWebSocketCount()).toBe(1)
    })
  })

  describe('Message Processing', () => {
    beforeEach(async () => {
      await chatStore.connect()
      const ws = wsTestUtils.getLastCreatedWebSocket()
      if (ws) {
        ws.simulateOpen()
      }
      await wsTestUtils.waitFor(() => chatStore.isConnected(), 1000)
    })

    it('should send messages when connected', () => {
      const testMessage = { type: 'chat', content: 'Hello World', userId: 'user123' }
      
      const result = chatStore.sendMessage(testMessage)
      
      expect(result).toBe(true)
      
      const ws = wsTestUtils.getLastCreatedWebSocket()
      if (ws) {
        const sentMessages = ws.getSentMessages()
        expect(sentMessages).toHaveLength(1)
        expect(JSON.parse(sentMessages[0])).toEqual(testMessage)
      }
    })

    it('should not send messages when disconnected', () => {
      chatStore.disconnect()
      
      const testMessage = { type: 'chat', content: 'Hello World' }
      const result = chatStore.sendMessage(testMessage)
      
      expect(result).toBe(false)
    })

    it('should receive and process messages', async () => {
      const receivedMessages: any[] = []
      chatStore.onMessage = (message) => receivedMessages.push(message)
      
      const testMessage = { type: 'chat', content: 'Hello from server', userId: 'server' }
      
      const ws = wsTestUtils.getLastCreatedWebSocket()
      if (ws) {
        ws.simulateMessage(JSON.stringify(testMessage))
      }
      
      await wsTestUtils.waitFor(() => receivedMessages.length > 0, 1000)
      
      expect(receivedMessages).toHaveLength(1)
      expect(receivedMessages[0]).toEqual(testMessage)
      expect(chatStore.getMessages()).toContain(testMessage)
    })

    it('should handle multiple messages', async () => {
      const receivedMessages: any[] = []
      chatStore.onMessage = (message) => receivedMessages.push(message)
      
      const messages = [
        { type: 'chat', content: 'Message 1', userId: 'user1' },
        { type: 'chat', content: 'Message 2', userId: 'user2' },
        { type: 'notification', content: 'System message' }
      ]
      
      const ws = wsTestUtils.getLastCreatedWebSocket()
      if (ws) {
        messages.forEach(msg => {
          ws.simulateMessage(JSON.stringify(msg))
        })
      }
      
      await wsTestUtils.waitFor(() => receivedMessages.length === 3, 1000)
      
      expect(receivedMessages).toHaveLength(3)
      expect(chatStore.getMessages()).toHaveLength(3)
      messages.forEach((msg, index) => {
        expect(receivedMessages[index]).toEqual(msg)
      })
    })

    it('should clear messages', async () => {
      // Add some messages first
      const ws = wsTestUtils.getLastCreatedWebSocket()
      if (ws) {
        ws.simulateMessage(JSON.stringify({ type: 'chat', content: 'Test message' }))
      }
      
      await wsTestUtils.waitFor(() => chatStore.getMessages().length > 0, 1000)
      expect(chatStore.getMessages()).toHaveLength(1)
      
      // Clear messages
      chatStore.clearMessages()
      expect(chatStore.getMessages()).toHaveLength(0)
    })
  })

  describe('Heartbeat Mechanism', () => {
    beforeEach(async () => {
      await chatStore.connect()
      const ws = wsTestUtils.getLastCreatedWebSocket()
      if (ws) {
        ws.simulateOpen()
      }
      await wsTestUtils.waitFor(() => chatStore.isConnected(), 1000)
    })

    it('should send ping messages periodically', async () => {
      // Fast-forward time to trigger heartbeat
      vi.useFakeTimers()
      
      const ws = wsTestUtils.getLastCreatedWebSocket()
      expect(ws).toBeDefined()
      
      // Advance time by 30 seconds to trigger heartbeat
      vi.advanceTimersByTime(30000)
      
      if (ws) {
        const sentMessages = ws.getSentMessages()
        const pingMessages = sentMessages.filter(msg => {
          try {
            const parsed = JSON.parse(msg)
            return parsed.type === 'ping'
          } catch {
            return false
          }
        })
        
        expect(pingMessages.length).toBeGreaterThan(0)
        expect(chatStore.lastHeartbeat).toBeDefined()
      }
      
      vi.useRealTimers()
    })

    it('should handle pong responses', async () => {
      vi.useFakeTimers()
      
      const ws = wsTestUtils.getLastCreatedWebSocket()
      
      // Trigger heartbeat
      vi.advanceTimersByTime(30000)
      
      // Simulate pong response
      if (ws) {
        ws.simulateMessage(JSON.stringify({ type: 'pong', timestamp: Date.now() }))
      }
      
      // Should not trigger timeout
      vi.advanceTimersByTime(15000)
      expect(chatStore.isConnected()).toBe(true)
      
      vi.useRealTimers()
    })

    it('should handle heartbeat timeout', async () => {
      vi.useFakeTimers()
      
      const errors: string[] = []
      chatStore.onError = (error) => errors.push(error)
      
      const ws = wsTestUtils.getLastCreatedWebSocket()
      
      // Trigger heartbeat
      vi.advanceTimersByTime(30000)
      
      // Don't send pong response, let it timeout
      vi.advanceTimersByTime(15000)
      
      expect(errors.some(error => error.includes('Heartbeat timeout'))).toBe(true)
      
      vi.useRealTimers()
    })

    it('should stop heartbeat on disconnection', () => {
      vi.useFakeTimers()
      
      // Disconnect
      chatStore.disconnect()
      
      // Advance time - should not send ping
      vi.advanceTimersByTime(60000)
      
      const ws = wsTestUtils.getLastCreatedWebSocket()
      if (ws) {
        const sentMessages = ws.getSentMessages()
        const pingMessages = sentMessages.filter(msg => {
          try {
            const parsed = JSON.parse(msg)
            return parsed.type === 'ping'
          } catch {
            return false
          }
        })
        
        expect(pingMessages).toHaveLength(0)
      }
      
      vi.useRealTimers()
    })
  })

  describe('Reconnection Logic', () => {
    beforeEach(async () => {
      await chatStore.connect()
      const ws = wsTestUtils.getLastCreatedWebSocket()
      if (ws) {
        ws.simulateOpen()
      }
      await wsTestUtils.waitFor(() => chatStore.isConnected(), 1000)
    })

    it('should attempt reconnection on abnormal disconnection', async () => {
      vi.useFakeTimers()
      
      const connectionStates: string[] = []
      chatStore.onConnectionChange = (state) => connectionStates.push(state)
      
      const ws = wsTestUtils.getLastCreatedWebSocket()
      if (ws) {
        // Simulate abnormal disconnection
        ws.simulateClose(1006, 'Connection lost')
      }
      
      // Should enter reconnecting state
      expect(connectionStates).toContain('reconnecting')
      
      // Advance time to trigger reconnection
      vi.advanceTimersByTime(2000)
      
      // Should attempt to create new WebSocket
      expect(wsTestUtils.getCreatedWebSocketCount()).toBeGreaterThan(1)
      
      vi.useRealTimers()
    })

    it('should not reconnect on normal closure', async () => {
      const connectionStates: string[] = []
      chatStore.onConnectionChange = (state) => connectionStates.push(state)
      
      const ws = wsTestUtils.getLastCreatedWebSocket()
      if (ws) {
        // Simulate normal closure
        ws.simulateClose(1000, 'Normal closure')
      }
      
      await wsTestUtils.waitFor(() => chatStore.getConnectionState() === 'disconnected', 1000)
      
      expect(connectionStates).toContain('disconnected')
      expect(connectionStates).not.toContain('reconnecting')
    })

    it('should use exponential backoff for reconnection attempts', async () => {
      vi.useFakeTimers()
      
      const ws1 = wsTestUtils.getLastCreatedWebSocket()
      if (ws1) {
        ws1.simulateClose(1006, 'Connection lost')
      }
      
      // First reconnection attempt (delay: 1000ms * 2^0 = 1000ms)
      vi.advanceTimersByTime(1000)
      expect(wsTestUtils.getCreatedWebSocketCount()).toBe(2)
      
      const ws2 = wsTestUtils.getLastCreatedWebSocket()
      if (ws2) {
        ws2.simulateError(new Error('Connection failed'))
      }
      
      // Second reconnection attempt (delay: 1000ms * 2^1 = 2000ms)
      vi.advanceTimersByTime(2000)
      expect(wsTestUtils.getCreatedWebSocketCount()).toBe(3)
      
      vi.useRealTimers()
    })

    it('should stop reconnecting after max attempts', async () => {
      vi.useFakeTimers()
      
      const errors: string[] = []
      chatStore.onError = (error) => errors.push(error)
      
      // Simulate multiple failed reconnection attempts
      for (let i = 0; i < 4; i++) {
        const ws = wsTestUtils.getLastCreatedWebSocket()
        if (ws) {
          if (i === 0) {
            ws.simulateClose(1006, 'Connection lost')
          } else {
            ws.simulateError(new Error('Connection failed'))
          }
        }
        
        // Advance time for next attempt
        vi.advanceTimersByTime(1000 * Math.pow(2, i))
      }
      
      // Should have stopped trying and reported error
      expect(chatStore.getConnectionState()).toBe('disconnected')
      expect(errors.some(error => error.includes('Connection lost'))).toBe(true)
      
      vi.useRealTimers()
    })

    it('should reset reconnection attempts on successful connection', async () => {
      vi.useFakeTimers()
      
      // First disconnection and reconnection
      const ws1 = wsTestUtils.getLastCreatedWebSocket()
      if (ws1) {
        ws1.simulateClose(1006, 'Connection lost')
      }
      
      vi.advanceTimersByTime(1000)
      
      const ws2 = wsTestUtils.getLastCreatedWebSocket()
      if (ws2) {
        ws2.simulateOpen()
      }
      
      await wsTestUtils.waitFor(() => chatStore.isConnected(), 1000)
      
      // Second disconnection - should start from first attempt again
      if (ws2) {
        ws2.simulateClose(1006, 'Connection lost again')
      }
      
      vi.advanceTimersByTime(1000) // Should use initial delay, not exponential
      expect(wsTestUtils.getCreatedWebSocketCount()).toBe(3)
      
      vi.useRealTimers()
    })
  })

  describe('Error Handling', () => {
    it('should handle JSON parsing errors gracefully', async () => {
      await chatStore.connect()
      const ws = wsTestUtils.getLastCreatedWebSocket()
      if (ws) {
        ws.simulateOpen()
      }
      await wsTestUtils.waitFor(() => chatStore.isConnected(), 1000)
      
      const errors: string[] = []
      chatStore.onError = (error) => errors.push(error)
      
      // Send invalid JSON
      if (ws) {
        ws.simulateMessage('invalid json {')
      }
      
      // Should not crash, but may log error
      expect(chatStore.isConnected()).toBe(true)
    })

    it('should handle send message errors', async () => {
      await chatStore.connect()
      const ws = wsTestUtils.getLastCreatedWebSocket()
      if (ws) {
        ws.simulateOpen()
      }
      await wsTestUtils.waitFor(() => chatStore.isConnected(), 1000)
      
      const errors: string[] = []
      chatStore.onError = (error) => errors.push(error)
      
      // Force WebSocket to throw error on send
      if (ws) {
        ws.simulateSendError(new Error('Send failed'))
      }
      
      const result = chatStore.sendMessage({ type: 'test' })
      
      expect(result).toBe(false)
      expect(errors.some(error => error.includes('Failed to send message'))).toBe(true)
    })

    it('should handle connection state edge cases', () => {
      // Test sending message in various states
      expect(chatStore.sendMessage({ type: 'test' })).toBe(false) // disconnected
      
      // Test multiple disconnections
      chatStore.disconnect()
      chatStore.disconnect() // Should not throw
      
      expect(chatStore.getConnectionState()).toBe('disconnected')
    })
  })

  describe('State Management', () => {
    it('should maintain correct connection state throughout lifecycle', async () => {
      const states: string[] = []
      chatStore.onConnectionChange = (state) => states.push(state)
      
      // Initial state
      expect(chatStore.getConnectionState()).toBe('disconnected')
      
      // Connect
      const connectPromise = chatStore.connect()
      expect(states).toContain('connecting')
      
      const ws = wsTestUtils.getLastCreatedWebSocket()
      if (ws) {
        ws.simulateOpen()
      }
      
      await connectPromise
      expect(states).toContain('connected')
      expect(chatStore.isConnected()).toBe(true)
      
      // Disconnect
      chatStore.disconnect()
      expect(states).toContain('disconnected')
      expect(chatStore.isConnected()).toBe(false)
      
      // Verify state progression
      expect(states).toEqual(['connecting', 'connected', 'disconnected'])
    })

    it('should provide accurate message history', async () => {
      await chatStore.connect()
      const ws = wsTestUtils.getLastCreatedWebSocket()
      if (ws) {
        ws.simulateOpen()
      }
      await wsTestUtils.waitFor(() => chatStore.isConnected(), 1000)
      
      const messages = [
        { type: 'chat', content: 'Message 1' },
        { type: 'chat', content: 'Message 2' },
        { type: 'notification', content: 'System message' }
      ]
      
      // Send messages
      if (ws) {
        messages.forEach(msg => {
          ws.simulateMessage(JSON.stringify(msg))
        })
      }
      
      await wsTestUtils.waitFor(() => chatStore.getMessages().length === 3, 1000)
      
      // Verify message history
      const storedMessages = chatStore.getMessages()
      expect(storedMessages).toHaveLength(3)
      expect(storedMessages).toEqual(messages)
      
      // Verify immutability
      storedMessages.push({ type: 'fake', content: 'Should not affect store' })
      expect(chatStore.getMessages()).toHaveLength(3)
    })

    it('should track connection errors', async () => {
      const connectPromise = chatStore.connect()
      
      const ws = wsTestUtils.getLastCreatedWebSocket()
      if (ws) {
        ws.simulateError(new Error('Test connection error'))
      }
      
      await expect(connectPromise).rejects.toThrow()
      
      expect(chatStore.connectionError).toBe('WebSocket connection error')
      expect(chatStore.getConnectionState()).toBe('disconnected')
    })
  })
})