import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { TestServerManager } from '../utils/test-server-manager'
import { APITestClient } from '../utils/api-test-client'

describe('TestServerManager', () => {
  let serverManager: TestServerManager
  let client: APITestClient

  beforeEach(() => {
    serverManager = new TestServerManager({
      database: {
        resetOnStart: false, // 避免在單元測試中重置資料庫
        seedData: false
      },
      middleware: {
        cors: true,
        logging: false,
        errorHandling: true
      }
    })
  })

  afterEach(async () => {
    if (serverManager.isServerRunning()) {
      await serverManager.stop()
    }
  })

  describe('Server Lifecycle', () => {
    it('should start server successfully', async () => {
      await serverManager.start()

      expect(serverManager.isServerRunning()).toBe(true)
      expect(serverManager.getPort()).toBeGreaterThan(0)
      expect(serverManager.getBaseURL()).toMatch(/^http:\/\/localhost:\d+$/)
    })

    it('should stop server successfully', async () => {
      await serverManager.start()
      expect(serverManager.isServerRunning()).toBe(true)

      await serverManager.stop()
      expect(serverManager.isServerRunning()).toBe(false)
    })

    it('should not start server twice', async () => {
      await serverManager.start()
      
      // 第二次啟動應該不會有問題
      await serverManager.start()
      expect(serverManager.isServerRunning()).toBe(true)
    })

    it('should handle stop when server not running', async () => {
      expect(serverManager.isServerRunning()).toBe(false)
      
      // 停止未運行的伺服器應該不會有問題
      await serverManager.stop()
      expect(serverManager.isServerRunning()).toBe(false)
    })
  })

  describe('Server Configuration', () => {
    it('should use custom configuration', async () => {
      const customManager = new TestServerManager({
        middleware: {
          cors: false,
          logging: true,
          errorHandling: false
        }
      })

      await customManager.start()
      expect(customManager.isServerRunning()).toBe(true)
      
      await customManager.stop()
    })

    it('should provide Express app instance', () => {
      const app = serverManager.getApp()
      expect(app).toBeDefined()
      expect(typeof app.use).toBe('function')
      expect(typeof app.get).toBe('function')
    })
  })

  describe('Health Check', () => {
    it('should respond to health check endpoint', async () => {
      await serverManager.start()
      client = new APITestClient(serverManager.getApp())

      const response = await client.get('/health')

      expect(response.status).toBe(200)
      expect(response.data.status).toBe('ok')
      expect(response.data.environment).toBe('test')
      expect(response.data.timestamp).toBeDefined()
    })

    it('should wait for server ready', async () => {
      const startPromise = serverManager.start()
      
      // 在伺服器啟動的同時等待準備就緒
      await Promise.all([
        startPromise,
        serverManager.waitForReady(10000)
      ])

      expect(serverManager.isServerRunning()).toBe(true)
    })

    it('should timeout when server not ready', async () => {
      // 不啟動伺服器，直接等待準備就緒應該超時
      await expect(serverManager.waitForReady(100)).rejects.toThrow('Server not ready within 100ms')
    })
  })

  describe('Error Handling', () => {
    it('should handle server start timeout', async () => {
      // 模擬啟動超時
      const timeoutManager = new TestServerManager({
        timeout: 1 // 很短的超時時間
      })

      // 這個測試可能需要根據實際實作調整
      await expect(timeoutManager.start()).rejects.toThrow()
    }, 10000)

    it('should throw error when getting URL from stopped server', () => {
      expect(() => serverManager.getBaseURL()).toThrow('Server is not running')
    })

    it('should throw error when getting port from stopped server', () => {
      expect(() => serverManager.getPort()).toThrow('Server is not running')
    })
  })

  describe('Server Reset', () => {
    it('should reset server state', async () => {
      await serverManager.start()
      
      // 重置應該不會影響伺服器運行狀態
      await serverManager.reset()
      expect(serverManager.isServerRunning()).toBe(true)
    })
  })

  describe('Middleware Setup', () => {
    it('should handle CORS requests', async () => {
      await serverManager.start()
      client = new APITestClient(serverManager.getApp())

      const response = await client.get('/health', {
        headers: {
          'Origin': 'http://localhost:3000'
        }
      })

      expect(response.status).toBe(200)
      // CORS 標頭應該存在
      expect(response.headers['access-control-allow-origin']).toBeDefined()
    })

    it('should parse JSON request bodies', async () => {
      await serverManager.start()
      client = new APITestClient(serverManager.getApp())

      // 假設有一個接受 POST 的端點
      const testData = { test: 'data' }
      
      // 這裡需要根據實際的路由設置來測試
      // 目前只測試伺服器能正常處理 JSON 請求
      const response = await client.post('/health', testData)
      
      // 健康檢查端點可能不接受 POST，但至少應該有適當的錯誤回應
      expect([200, 404, 405]).toContain(response.status)
    })
  })

  describe('Error Middleware', () => {
    it('should handle 404 errors', async () => {
      await serverManager.start()
      client = new APITestClient(serverManager.getApp())

      const response = await client.get('/nonexistent-endpoint')

      expect(response.status).toBe(404)
    })

    it('should handle server errors gracefully', async () => {
      await serverManager.start()
      
      // 添加一個會拋出錯誤的路由
      serverManager.getApp().get('/test-error', (req, res, next) => {
        throw new Error('Test error')
      })

      client = new APITestClient(serverManager.getApp())
      const response = await client.get('/test-error')

      expect(response.status).toBe(500)
    })
  })
})