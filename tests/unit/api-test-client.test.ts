import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import express from 'express'
import { APITestClient } from '../utils/api-test-client'

describe('APITestClient', () => {
  let app: express.Express
  let client: APITestClient

  beforeEach(() => {
    app = express()
    app.use(express.json())
    client = new APITestClient(app)

    // Setup test routes
    app.get('/test', (req, res) => {
      res.json({ message: 'GET success', headers: req.headers })
    })

    app.post('/test', (req, res) => {
      res.status(201).json({ message: 'POST success', body: req.body })
    })

    app.put('/test/:id', (req, res) => {
      res.json({ message: 'PUT success', id: req.params.id, body: req.body })
    })

    app.patch('/test/:id', (req, res) => {
      res.json({ message: 'PATCH success', id: req.params.id, body: req.body })
    })

    app.delete('/test/:id', (req, res) => {
      res.status(204).send()
    })

    app.get('/auth-required', (req, res) => {
      const auth = req.headers.authorization
      if (!auth || !auth.startsWith('Bearer ')) {
        return res.status(401).json({ error: 'Unauthorized' })
      }
      res.json({ message: 'Authenticated', token: auth })
    })

    app.get('/error', (req, res) => {
      res.status(400).json({ error: 'Bad Request', message: 'Test error' })
    })

    app.get('/server-error', (req, res) => {
      res.status(500).json({ error: 'Internal Server Error' })
    })
  })

  describe('HTTP Methods', () => {
    it('should make GET requests successfully', async () => {
      const response = await client.get('/test')

      expect(response.status).toBe(200)
      expect(response.data.message).toBe('GET success')
      expect(response.headers).toBeDefined()
    })

    it('should make POST requests with data', async () => {
      const testData = { name: 'Test', value: 123 }
      const response = await client.post('/test', testData)

      expect(response.status).toBe(201)
      expect(response.data.message).toBe('POST success')
      expect(response.data.body).toEqual(testData)
    })

    it('should make PUT requests with data', async () => {
      const testData = { name: 'Updated Test' }
      const response = await client.put('/test/123', testData)

      expect(response.status).toBe(200)
      expect(response.data.message).toBe('PUT success')
      expect(response.data.id).toBe('123')
      expect(response.data.body).toEqual(testData)
    })

    it('should make PATCH requests with data', async () => {
      const testData = { name: 'Patched Test' }
      const response = await client.patch('/test/456', testData)

      expect(response.status).toBe(200)
      expect(response.data.message).toBe('PATCH success')
      expect(response.data.id).toBe('456')
      expect(response.data.body).toEqual(testData)
    })

    it('should make DELETE requests', async () => {
      const response = await client.delete('/test/789')

      expect(response.status).toBe(204)
    })
  })

  describe('Authentication', () => {
    it('should set and use auth token', async () => {
      const token = 'test-token-123'
      client.setAuthToken(token)

      const response = await client.get('/auth-required')

      expect(response.status).toBe(200)
      expect(response.data.message).toBe('Authenticated')
      expect(response.data.token).toBe(`Bearer ${token}`)
    })

    it('should handle unauthorized requests', async () => {
      const response = await client.get('/auth-required')

      expect(response.status).toBe(401)
      expect(response.data.error).toBe('Unauthorized')
    })

    it('should clear auth token', async () => {
      client.setAuthToken('test-token')
      client.clearAuth()

      const response = await client.get('/auth-required')

      expect(response.status).toBe(401)
    })
  })

  describe('Headers', () => {
    it('should set default headers', async () => {
      const response = await client.get('/test')

      expect(response.data.headers['content-type']).toContain('application/json')
    })

    it('should set custom headers', async () => {
      const customHeaders = { 'X-Custom-Header': 'test-value' }
      const response = await client.get('/test', { headers: customHeaders })

      expect(response.data.headers['x-custom-header']).toBe('test-value')
    })

    it('should merge custom headers with defaults', async () => {
      client.setDefaultHeaders({ 'X-Default': 'default-value' })
      const response = await client.get('/test', { 
        headers: { 'X-Custom': 'custom-value' } 
      })

      expect(response.data.headers['x-default']).toBe('default-value')
      expect(response.data.headers['x-custom']).toBe('custom-value')
    })
  })

  describe('Response Validation', () => {
    it('should validate response status', async () => {
      const response = await client.get('/test')

      expect(() => client.expectStatus(response, 200)).not.toThrow()
      expect(() => client.expectStatus(response, 404)).toThrow('Expected status 404 but got 200')
    })

    it('should validate response properties', async () => {
      const response = await client.get('/test')

      expect(() => client.expectResponseToHaveProperty(response, 'message')).not.toThrow()
      expect(() => client.expectResponseToHaveProperty(response, 'nonexistent')).toThrow()
    })

    it('should validate response structure', async () => {
      const response = await client.get('/test')
      const expectedStructure = { message: 'string', headers: 'object' }

      expect(() => client.expectResponseStructure(response, expectedStructure)).not.toThrow()
    })

    it('should validate error responses', async () => {
      const response = await client.get('/error')

      expect(() => client.expectErrorResponse(response, 400)).not.toThrow()
      expect(() => client.expectErrorResponse(response, 400, 'Test error')).not.toThrow()
      expect(() => client.expectErrorResponse(response, 500)).toThrow()
    })
  })

  describe('Error Handling', () => {
    it('should handle 4xx errors', async () => {
      const response = await client.get('/error')

      expect(response.status).toBe(400)
      expect(response.data.error).toBe('Bad Request')
      expect(response.data.message).toBe('Test error')
    })

    it('should handle 5xx errors', async () => {
      const response = await client.get('/server-error')

      expect(response.status).toBe(500)
      expect(response.data.error).toBe('Internal Server Error')
    })

    it('should handle network timeouts', async () => {
      app.get('/slow', (req, res) => {
        setTimeout(() => res.json({ message: 'slow response' }), 2000)
      })

      await expect(client.get('/slow', { timeout: 100 })).rejects.toThrow()
    })
  })

  describe('Response Format', () => {
    it('should format response correctly', async () => {
      const response = await client.get('/test')

      expect(response).toHaveProperty('status')
      expect(response).toHaveProperty('statusText')
      expect(response).toHaveProperty('data')
      expect(response).toHaveProperty('headers')
      expect(response).toHaveProperty('body')
    })

    it('should provide correct status text', async () => {
      const responses = await Promise.all([
        client.get('/test'),
        client.post('/test', {}),
        client.get('/error'),
        client.get('/server-error')
      ])

      expect(responses[0].statusText).toBe('OK')
      expect(responses[1].statusText).toBe('Created')
      expect(responses[2].statusText).toBe('Bad Request')
      expect(responses[3].statusText).toBe('Internal Server Error')
    })
  })

  describe('Request Options', () => {
    it('should handle request timeout', async () => {
      app.get('/timeout-test', (req, res) => {
        setTimeout(() => res.json({ message: 'delayed' }), 1000)
      })

      await expect(client.get('/timeout-test', { timeout: 100 })).rejects.toThrow()
    })

    it('should handle empty request body', async () => {
      const response = await client.post('/test')

      expect(response.status).toBe(201)
      expect(response.data.body).toEqual({})
    })

    it('should handle different content types', async () => {
      const response = await client.post('/test', 'plain text', {
        headers: { 'Content-Type': 'text/plain' }
      })

      expect(response.status).toBe(201)
    })
  })
})