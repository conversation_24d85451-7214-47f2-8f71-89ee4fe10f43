// 測試工具函數的單元測試
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { 
  waitFor, 
  timeUtils, 
  randomUtils, 
  cleanupUtils,
  asyncUtils,
  assertUtils 
} from './test-helpers'
import { 
  projectMockFactory, 
  fileMockFactory, 
  aiProviderMockFactory,
  webSocketMockFactory,
  httpMockFactory 
} from './mock-factories'
import { 
  projectDataGenerator, 
  fileDataGenerator, 
  apiResponseGenerator 
} from './test-data-generators'

describe('Test Utilities', () => {
  describe('waitFor', () => {
    it('should resolve when condition becomes true', async () => {
      let counter = 0
      const condition = () => {
        counter++
        return counter >= 3
      }

      await waitFor(condition, 1000, 50)
      expect(counter).toBeGreaterThanOrEqual(3)
    })

    it('should timeout when condition never becomes true', async () => {
      const condition = () => false
      
      await expect(waitFor(condition, 100, 10))
        .rejects.toThrow('Condition not met within 100ms')
    })
  })

  describe('timeUtils', () => {
    beforeEach(() => {
      timeUtils.useFakeTimers()
    })

    afterEach(() => {
      timeUtils.useRealTimers()
    })

    it('should advance timers correctly', () => {
      const callback = vi.fn()
      setTimeout(callback, 1000)

      expect(callback).not.toHaveBeenCalled()
      timeUtils.advanceTimers(1000)
      expect(callback).toHaveBeenCalled()
    })
  })

  describe('randomUtils', () => {
    it('should generate random string of correct length', () => {
      const str = randomUtils.generateRandomString(10)
      expect(str).toHaveLength(10)
      expect(typeof str).toBe('string')
    })

    it('should generate valid email format', () => {
      const email = randomUtils.generateRandomEmail()
      expect(email).toMatch(/^[^@]+@[^@]+\.[^@]+$/)
    })

    it('should generate unique IDs', () => {
      const id1 = randomUtils.generateRandomId()
      const id2 = randomUtils.generateRandomId()
      expect(id1).not.toBe(id2)
    })

    it('should generate numbers within range', () => {
      const num = randomUtils.generateRandomNumber(10, 20)
      expect(num).toBeGreaterThanOrEqual(10)
      expect(num).toBeLessThanOrEqual(20)
    })
  })

  describe('cleanupUtils', () => {
    it('should clear all mocks', () => {
      const mockFn = vi.fn()
      mockFn()
      expect(mockFn).toHaveBeenCalledTimes(1)
      
      cleanupUtils.clearAllMocks()
      expect(mockFn).toHaveBeenCalledTimes(0)
    })
  })

  describe('asyncUtils', () => {
    it('should delay execution', async () => {
      const start = Date.now()
      await asyncUtils.delay(100)
      const end = Date.now()
      expect(end - start).toBeGreaterThanOrEqual(90) // 允許一些誤差
    })

    it('should timeout promises', async () => {
      const slowPromise = new Promise(resolve => setTimeout(resolve, 1000))
      
      await expect(asyncUtils.waitForPromise(slowPromise, 100))
        .rejects.toThrow('Promise timeout after 100ms')
    })
  })

  describe('Mock Factories', () => {
    describe('projectMockFactory', () => {
      it('should create project with default values', () => {
        const project = projectMockFactory.createProject()
        
        expect(project).toHaveProperty('id')
        expect(project).toHaveProperty('name')
        expect(project).toHaveProperty('description')
        expect(project).toHaveProperty('createdAt')
        expect(project).toHaveProperty('files')
        expect(Array.isArray(project.files)).toBe(true)
      })

      it('should override default values', () => {
        const customName = '自訂專案名稱'
        const project = projectMockFactory.createProject({ name: customName })
        
        expect(project.name).toBe(customName)
      })

      it('should create multiple projects', () => {
        const projects = projectMockFactory.createProjects(3)
        
        expect(projects).toHaveLength(3)
        expect(projects[0].name).toContain('測試專案-1')
        expect(projects[1].name).toContain('測試專案-2')
      })
    })

    describe('fileMockFactory', () => {
      it('should create file with default values', () => {
        const file = fileMockFactory.createFile()
        
        expect(file).toHaveProperty('id')
        expect(file).toHaveProperty('name')
        expect(file).toHaveProperty('content')
        expect(file).toHaveProperty('projectId')
        expect(file.name).toMatch(/\.vue$/)
      })
    })

    describe('aiProviderMockFactory', () => {
      it('should create AI provider with mock functions', () => {
        const provider = aiProviderMockFactory.createProvider()
        
        expect(provider).toHaveProperty('generateContent')
        expect(provider).toHaveProperty('generateStream')
        expect(provider).toHaveProperty('healthCheck')
        expect(vi.isMockFunction(provider.generateContent)).toBe(true)
      })
    })

    describe('webSocketMockFactory', () => {
      it('should create WebSocket with trigger methods', () => {
        const ws = webSocketMockFactory.createWebSocket()
        
        expect(ws).toHaveProperty('send')
        expect(ws).toHaveProperty('triggerOpen')
        expect(ws).toHaveProperty('triggerMessage')
        expect(ws).toHaveProperty('triggerClose')
        expect(vi.isMockFunction(ws.send)).toBe(true)
      })
    })

    describe('httpMockFactory', () => {
      it('should create success response', () => {
        const data = { message: 'success' }
        const response = httpMockFactory.createSuccessResponse(data)
        
        expect(response.status).toBe(200)
        expect(response.data).toEqual(data)
      })

      it('should create error response', () => {
        const message = 'Error occurred'
        const response = httpMockFactory.createErrorResponse(message, 400)
        
        expect(response.status).toBe(400)
        expect(response.data.error).toBe(message)
      })
    })
  })

  describe('Data Generators', () => {
    describe('projectDataGenerator', () => {
      it('should generate valid project data', () => {
        const project = projectDataGenerator.generateValidProject()
        
        expect(project).toHaveProperty('name')
        expect(project).toHaveProperty('description')
        expect(project.name).toBeTruthy()
        expect(project.description).toBeTruthy()
      })

      it('should generate invalid project data', () => {
        const project = projectDataGenerator.generateInvalidProject()
        
        expect(project.name).toBe('')
        expect(project.description.length).toBeGreaterThan(1000)
      })
    })

    describe('fileDataGenerator', () => {
      it('should generate Vue file', () => {
        const file = fileDataGenerator.generateVueFile('TestComponent')
        
        expect(file.name).toBe('TestComponent.vue')
        expect(file.content).toContain('<template>')
        expect(file.content).toContain('<script setup')
        expect(file.content).toContain('<style scoped>')
      })

      it('should generate TypeScript file', () => {
        const file = fileDataGenerator.generateTsFile('testUtils')
        
        expect(file.name).toBe('testUtils.ts')
        expect(file.content).toContain('export function')
        expect(file.content).toContain('export const')
        expect(file.content).toContain('export interface')
      })
    })

    describe('apiResponseGenerator', () => {
      it('should generate success response', () => {
        const data = { id: 1, name: 'test' }
        const response = apiResponseGenerator.generateSuccessResponse(data)
        
        expect(response.success).toBe(true)
        expect(response.data).toEqual(data)
        expect(response).toHaveProperty('timestamp')
      })

      it('should generate paginated response', () => {
        const items = Array.from({ length: 25 }, (_, i) => ({ id: i + 1 }))
        const response = apiResponseGenerator.generatePaginatedResponse(items, 2, 10)
        
        expect(response.data).toHaveLength(10)
        expect(response.pagination.page).toBe(2)
        expect(response.pagination.total).toBe(25)
        expect(response.pagination.totalPages).toBe(3)
        expect(response.pagination.hasNext).toBe(true)
        expect(response.pagination.hasPrev).toBe(true)
      })
    })
  })
})