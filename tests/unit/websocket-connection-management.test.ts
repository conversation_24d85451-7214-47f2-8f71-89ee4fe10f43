import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { MockWebSocket } from '../utils/mock-websocket'
import { WebSocketTestUtils } from '../utils/websocket-test-utils'

// Mock the global WebSocket
global.WebSocket = MockWebSocket as any

// WebSocket Connection Manager (simplified for testing)
class WebSocketConnectionManager {
  private ws: WebSocket | null = null
  private url: string
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5
  private reconnectDelay = 1000
  private heartbeatInterval: NodeJS.Timeout | null = null
  private heartbeatTimeout: NodeJS.Timeout | null = null
  private connectionTimeout: NodeJS.Timeout | null = null
  private isConnecting = false
  private isReconnecting = false
  
  // State
  public connectionState: 'disconnected' | 'connecting' | 'connected' | 'reconnecting' | 'error' = 'disconnected'
  public lastHeartbeat: Date | null = null
  public connectionError: string | null = null
  public connectionStartTime: Date | null = null
  public connectionDuration: number = 0
  
  // Statistics
  public totalReconnectAttempts = 0
  public successfulConnections = 0
  public failedConnections = 0
  public heartbeatsSent = 0
  public heartbeatsReceived = 0
  
  // Event handlers
  public onStateChange: ((state: string) => void) | null = null
  public onError: ((error: string) => void) | null = null
  public onHeartbeat: ((sent: boolean) => void) | null = null
  public onReconnectAttempt: ((attempt: number) => void) | null = null

  constructor(url: string, options: {
    maxReconnectAttempts?: number
    reconnectDelay?: number
    heartbeatInterval?: number
    connectionTimeout?: number
  } = {}) {
    this.url = url
    this.maxReconnectAttempts = options.maxReconnectAttempts || 5
    this.reconnectDelay = options.reconnectDelay || 1000
  }

  // Connection management
  async connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.isConnecting || this.connectionState === 'connected') {
        resolve()
        return
      }

      this.isConnecting = true
      this.connectionState = 'connecting'
      this.connectionError = null
      this.connectionStartTime = new Date()
      this.onStateChange?.(this.connectionState)

      // Set connection timeout
      this.connectionTimeout = setTimeout(() => {
        this.handleConnectionTimeout()
        reject(new Error('Connection timeout'))
      }, 10000)

      try {
        this.ws = new WebSocket(this.url)
        
        this.ws.onopen = () => {
          this.clearConnectionTimeout()
          this.isConnecting = false
          this.connectionState = 'connected'
          this.reconnectAttempts = 0
          this.successfulConnections++
          this.connectionDuration = Date.now() - (this.connectionStartTime?.getTime() || 0)
          this.onStateChange?.(this.connectionState)
          this.startHeartbeat()
          resolve()
        }

        this.ws.onmessage = (event) => {
          this.handleMessage(event)
        }

        this.ws.onclose = (event) => {
          this.clearConnectionTimeout()
          this.handleDisconnection(event.code, event.reason)
        }

        this.ws.onerror = (event) => {
          this.clearConnectionTimeout()
          this.isConnecting = false
          this.failedConnections++
          const error = 'WebSocket connection error'
          this.connectionError = error
          this.connectionState = 'error'
          this.onStateChange?.(this.connectionState)
          this.onError?.(error)
          reject(new Error(error))
        }

      } catch (error) {
        this.clearConnectionTimeout()
        this.isConnecting = false
        this.failedConnections++
        this.connectionError = error.message
        this.connectionState = 'error'
        this.onStateChange?.(this.connectionState)
        this.onError?.(error.message)
        reject(error)
      }
    })
  }

  disconnect(): void {
    this.clearConnectionTimeout()
    this.stopHeartbeat()
    
    if (this.ws) {
      this.ws.close(1000, 'Normal closure')
      this.ws = null
    }
    
    this.connectionState = 'disconnected'
    this.onStateChange?.(this.connectionState)
  }

  // Message handling
  private handleMessage(event: MessageEvent): void {
    try {
      const message = JSON.parse(event.data)
      
      if (message.type === 'pong') {
        this.handlePong()
      }
      // Handle other message types as needed
    } catch (error) {
      console.warn('Failed to parse WebSocket message:', error)
    }
  }

  // Heartbeat mechanism
  private startHeartbeat(): void {
    this.stopHeartbeat()
    
    this.heartbeatInterval = setInterval(() => {
      if (this.connectionState === 'connected' && this.ws) {
        this.sendPing()
      }
    }, 30000) // 30 seconds
  }

  private stopHeartbeat(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval)
      this.heartbeatInterval = null
    }
    
    if (this.heartbeatTimeout) {
      clearTimeout(this.heartbeatTimeout)
      this.heartbeatTimeout = null
    }
  }

  private sendPing(): void {
    if (this.ws && this.connectionState === 'connected') {
      try {
        this.ws.send(JSON.stringify({ type: 'ping', timestamp: Date.now() }))
        this.lastHeartbeat = new Date()
        this.heartbeatsSent++
        this.onHeartbeat?.(true)
        
        // Set timeout for pong response
        this.heartbeatTimeout = setTimeout(() => {
          this.handleHeartbeatTimeout()
        }, 10000) // 10 seconds timeout
      } catch (error) {
        this.onError?.(`Failed to send ping: ${error.message}`)
      }
    }
  }

  private handlePong(): void {
    if (this.heartbeatTimeout) {
      clearTimeout(this.heartbeatTimeout)
      this.heartbeatTimeout = null
    }
    this.heartbeatsReceived++
    this.onHeartbeat?.(false)
  }

  private handleHeartbeatTimeout(): void {
    this.onError?.('Heartbeat timeout - connection may be lost')
    this.handleDisconnection(1006, 'Heartbeat timeout')
  }

  // Connection timeout handling
  private clearConnectionTimeout(): void {
    if (this.connectionTimeout) {
      clearTimeout(this.connectionTimeout)
      this.connectionTimeout = null
    }
  }

  private handleConnectionTimeout(): void {
    this.isConnecting = false
    this.failedConnections++
    this.connectionError = 'Connection timeout'
    this.connectionState = 'error'
    this.onStateChange?.(this.connectionState)
    this.onError?.(this.connectionError)
  }

  // Reconnection logic
  private handleDisconnection(code: number, reason: string): void {
    this.stopHeartbeat()
    this.ws = null
    
    if (code === 1000) {
      // Normal closure
      this.connectionState = 'disconnected'
      this.onStateChange?.(this.connectionState)
      return
    }

    // Abnormal closure - attempt reconnection
    if (this.reconnectAttempts < this.maxReconnectAttempts && !this.isReconnecting) {
      this.attemptReconnection()
    } else {
      this.connectionState = 'disconnected'
      this.connectionError = `Connection lost: ${reason} (code: ${code})`
      this.onStateChange?.(this.connectionState)
      this.onError?.(this.connectionError)
    }
  }

  private attemptReconnection(): void {
    this.isReconnecting = true
    this.connectionState = 'reconnecting'
    this.totalReconnectAttempts++
    this.onStateChange?.(this.connectionState)
    this.onReconnectAttempt?.(this.reconnectAttempts + 1)

    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts) // Exponential backoff
    
    setTimeout(() => {
      this.reconnectAttempts++
      this.isReconnecting = false
      
      this.connect().catch(() => {
        // Reconnection failed, will try again if attempts remain
      })
    }, delay)
  }

  // Utility methods
  getConnectionState(): string {
    return this.connectionState
  }

  isConnected(): boolean {
    return this.connectionState === 'connected'
  }

  getConnectionStats() {
    return {
      successfulConnections: this.successfulConnections,
      failedConnections: this.failedConnections,
      totalReconnectAttempts: this.totalReconnectAttempts,
      heartbeatsSent: this.heartbeatsSent,
      heartbeatsReceived: this.heartbeatsReceived,
      connectionDuration: this.connectionDuration,
      currentState: this.connectionState
    }
  }

  resetStats(): void {
    this.successfulConnections = 0
    this.failedConnections = 0
    this.totalReconnectAttempts = 0
    this.heartbeatsSent = 0
    this.heartbeatsReceived = 0
    this.connectionDuration = 0
  }

  // Manual reconnection
  forceReconnect(): void {
    this.reconnectAttempts = 0
    this.disconnect()
    setTimeout(() => {
      this.connect()
    }, 100)
  }
}

describe('WebSocket Connection Management Unit Tests', () => {
  let connectionManager: WebSocketConnectionManager
  let wsTestUtils: WebSocketTestUtils

  beforeEach(() => {
    vi.clearAllMocks()
    wsTestUtils = new WebSocketTestUtils()
    connectionManager = new WebSocketConnectionManager('ws://localhost:8080/test')
  })

  afterEach(() => {
    if (connectionManager) {
      connectionManager.disconnect()
    }
    wsTestUtils.cleanup()
  })

  describe('Connection State Management', () => {
    it('should initialize with disconnected state', () => {
      expect(connectionManager.getConnectionState()).toBe('disconnected')
      expect(connectionManager.isConnected()).toBe(false)
    })

    it('should transition through connection states correctly', async () => {
      const states: string[] = []
      connectionManager.onStateChange = (state) => states.push(state)

      const connectPromise = connectionManager.connect()
      
      // Should be connecting
      expect(states).toContain('connecting')
      expect(connectionManager.getConnectionState()).toBe('connecting')
      
      // Simulate successful connection
      const ws = wsTestUtils.getLastCreatedWebSocket()
      if (ws) {
        ws.simulateOpen()
      }
      
      await connectPromise
      
      expect(states).toContain('connected')
      expect(connectionManager.isConnected()).toBe(true)
    })

    it('should handle connection errors', async () => {
      const errors: string[] = []
      const states: string[] = []
      connectionManager.onError = (error) => errors.push(error)
      connectionManager.onStateChange = (state) => states.push(state)

      const connectPromise = connectionManager.connect()
      
      const ws = wsTestUtils.getLastCreatedWebSocket()
      if (ws) {
        ws.simulateError(new Error('Connection failed'))
      }
      
      await expect(connectPromise).rejects.toThrow('WebSocket connection error')
      expect(errors).toContain('WebSocket connection error')
      expect(states).toContain('error')
    })

    it('should handle connection timeout', async () => {
      vi.useFakeTimers()
      
      const errors: string[] = []
      const states: string[] = []
      connectionManager.onError = (error) => errors.push(error)
      connectionManager.onStateChange = (state) => states.push(state)

      const connectPromise = connectionManager.connect()
      
      // Fast-forward past connection timeout
      vi.advanceTimersByTime(15000)
      
      await expect(connectPromise).rejects.toThrow('Connection timeout')
      expect(errors).toContain('Connection timeout')
      expect(states).toContain('error')
      
      vi.useRealTimers()
    })

    it('should prevent multiple simultaneous connections', async () => {
      const connectPromise1 = connectionManager.connect()
      const connectPromise2 = connectionManager.connect()
      
      const ws = wsTestUtils.getLastCreatedWebSocket()
      if (ws) {
        ws.simulateOpen()
      }
      
      await Promise.all([connectPromise1, connectPromise2])
      
      expect(connectionManager.isConnected()).toBe(true)
      expect(wsTestUtils.getCreatedWebSocketCount()).toBe(1)
    })

    it('should disconnect gracefully', async () => {
      await connectionManager.connect()
      const ws = wsTestUtils.getLastCreatedWebSocket()
      if (ws) {
        ws.simulateOpen()
      }
      
      await wsTestUtils.waitFor(() => connectionManager.isConnected(), 1000)
      
      const states: string[] = []
      connectionManager.onStateChange = (state) => states.push(state)
      
      connectionManager.disconnect()
      
      expect(connectionManager.getConnectionState()).toBe('disconnected')
      expect(states).toContain('disconnected')
    })
  })

  describe('Heartbeat Mechanism', () => {
    beforeEach(async () => {
      await connectionManager.connect()
      const ws = wsTestUtils.getLastCreatedWebSocket()
      if (ws) {
        ws.simulateOpen()
      }
      await wsTestUtils.waitFor(() => connectionManager.isConnected(), 1000)
    })

    it('should send periodic heartbeat pings', async () => {
      vi.useFakeTimers()
      
      const heartbeats: boolean[] = []
      connectionManager.onHeartbeat = (sent) => heartbeats.push(sent)
      
      // Fast-forward to trigger heartbeat
      vi.advanceTimersByTime(30000)
      
      expect(heartbeats).toContain(true) // ping sent
      expect(connectionManager.lastHeartbeat).toBeDefined()
      
      const stats = connectionManager.getConnectionStats()
      expect(stats.heartbeatsSent).toBeGreaterThan(0)
      
      vi.useRealTimers()
    })

    it('should handle pong responses', async () => {
      vi.useFakeTimers()
      
      const heartbeats: boolean[] = []
      connectionManager.onHeartbeat = (sent) => heartbeats.push(sent)
      
      const ws = wsTestUtils.getLastCreatedWebSocket()
      
      // Trigger heartbeat
      vi.advanceTimersByTime(30000)
      
      // Simulate pong response
      if (ws) {
        ws.simulateMessage(JSON.stringify({ type: 'pong', timestamp: Date.now() }))
      }
      
      expect(heartbeats).toContain(false) // pong received
      
      const stats = connectionManager.getConnectionStats()
      expect(stats.heartbeatsReceived).toBeGreaterThan(0)
      
      vi.useRealTimers()
    })

    it('should handle heartbeat timeout', async () => {
      vi.useFakeTimers()
      
      const errors: string[] = []
      connectionManager.onError = (error) => errors.push(error)
      
      // Trigger heartbeat
      vi.advanceTimersByTime(30000)
      
      // Don't send pong, let it timeout
      vi.advanceTimersByTime(15000)
      
      expect(errors.some(error => error.includes('Heartbeat timeout'))).toBe(true)
      
      vi.useRealTimers()
    })

    it('should stop heartbeat on disconnection', () => {
      vi.useFakeTimers()
      
      connectionManager.disconnect()
      
      const initialHeartbeats = connectionManager.getConnectionStats().heartbeatsSent
      
      // Advance time - should not send more pings
      vi.advanceTimersByTime(60000)
      
      const finalHeartbeats = connectionManager.getConnectionStats().heartbeatsSent
      expect(finalHeartbeats).toBe(initialHeartbeats)
      
      vi.useRealTimers()
    })
  })

  describe('Reconnection Logic', () => {
    beforeEach(async () => {
      await connectionManager.connect()
      const ws = wsTestUtils.getLastCreatedWebSocket()
      if (ws) {
        ws.simulateOpen()
      }
      await wsTestUtils.waitFor(() => connectionManager.isConnected(), 1000)
    })

    it('should attempt reconnection on abnormal disconnection', async () => {
      vi.useFakeTimers()
      
      const states: string[] = []
      const reconnectAttempts: number[] = []
      connectionManager.onStateChange = (state) => states.push(state)
      connectionManager.onReconnectAttempt = (attempt) => reconnectAttempts.push(attempt)
      
      const ws = wsTestUtils.getLastCreatedWebSocket()
      if (ws) {
        ws.simulateClose(1006, 'Connection lost')
      }
      
      expect(states).toContain('reconnecting')
      
      // Advance time to trigger reconnection
      vi.advanceTimersByTime(2000)
      
      expect(reconnectAttempts).toContain(1)
      expect(wsTestUtils.getCreatedWebSocketCount()).toBeGreaterThan(1)
      
      vi.useRealTimers()
    })

    it('should not reconnect on normal closure', async () => {
      const states: string[] = []
      connectionManager.onStateChange = (state) => states.push(state)
      
      const ws = wsTestUtils.getLastCreatedWebSocket()
      if (ws) {
        ws.simulateClose(1000, 'Normal closure')
      }
      
      await wsTestUtils.waitFor(() => connectionManager.getConnectionState() === 'disconnected', 1000)
      
      expect(states).toContain('disconnected')
      expect(states).not.toContain('reconnecting')
    })

    it('should use exponential backoff for reconnection delays', async () => {
      vi.useFakeTimers()
      
      const reconnectAttempts: number[] = []
      connectionManager.onReconnectAttempt = (attempt) => reconnectAttempts.push(attempt)
      
      const ws1 = wsTestUtils.getLastCreatedWebSocket()
      if (ws1) {
        ws1.simulateClose(1006, 'Connection lost')
      }
      
      // First attempt (delay: 1000ms * 2^0 = 1000ms)
      vi.advanceTimersByTime(1000)
      expect(reconnectAttempts).toContain(1)
      
      const ws2 = wsTestUtils.getLastCreatedWebSocket()
      if (ws2) {
        ws2.simulateError(new Error('Connection failed'))
      }
      
      // Second attempt (delay: 1000ms * 2^1 = 2000ms)
      vi.advanceTimersByTime(2000)
      expect(reconnectAttempts).toContain(2)
      
      vi.useRealTimers()
    })

    it('should stop reconnecting after max attempts', async () => {
      vi.useFakeTimers()
      
      const errors: string[] = []
      connectionManager.onError = (error) => errors.push(error)
      
      // Simulate multiple failed reconnection attempts
      for (let i = 0; i < 6; i++) {
        const ws = wsTestUtils.getLastCreatedWebSocket()
        if (ws) {
          if (i === 0) {
            ws.simulateClose(1006, 'Connection lost')
          } else {
            ws.simulateError(new Error('Connection failed'))
          }
        }
        
        // Advance time for next attempt
        vi.advanceTimersByTime(1000 * Math.pow(2, i))
      }
      
      expect(connectionManager.getConnectionState()).toBe('disconnected')
      expect(errors.some(error => error.includes('Connection lost'))).toBe(true)
      
      vi.useRealTimers()
    })

    it('should reset reconnection attempts on successful connection', async () => {
      vi.useFakeTimers()
      
      // First disconnection and reconnection
      const ws1 = wsTestUtils.getLastCreatedWebSocket()
      if (ws1) {
        ws1.simulateClose(1006, 'Connection lost')
      }
      
      vi.advanceTimersByTime(1000)
      
      const ws2 = wsTestUtils.getLastCreatedWebSocket()
      if (ws2) {
        ws2.simulateOpen()
      }
      
      await wsTestUtils.waitFor(() => connectionManager.isConnected(), 1000)
      
      // Second disconnection - should start from first attempt again
      if (ws2) {
        ws2.simulateClose(1006, 'Connection lost again')
      }
      
      vi.advanceTimersByTime(1000) // Should use initial delay
      expect(wsTestUtils.getCreatedWebSocketCount()).toBe(3)
      
      vi.useRealTimers()
    })

    it('should support manual reconnection', async () => {
      const initialConnections = wsTestUtils.getCreatedWebSocketCount()
      
      connectionManager.forceReconnect()
      
      await wsTestUtils.waitFor(() => wsTestUtils.getCreatedWebSocketCount() > initialConnections, 1000)
      
      const ws = wsTestUtils.getLastCreatedWebSocket()
      if (ws) {
        ws.simulateOpen()
      }
      
      await wsTestUtils.waitFor(() => connectionManager.isConnected(), 1000)
      expect(connectionManager.isConnected()).toBe(true)
    })
  })

  describe('Connection Statistics', () => {
    it('should track connection statistics', async () => {
      // Initial stats
      let stats = connectionManager.getConnectionStats()
      expect(stats.successfulConnections).toBe(0)
      expect(stats.failedConnections).toBe(0)
      
      // Successful connection
      await connectionManager.connect()
      const ws = wsTestUtils.getLastCreatedWebSocket()
      if (ws) {
        ws.simulateOpen()
      }
      await wsTestUtils.waitFor(() => connectionManager.isConnected(), 1000)
      
      stats = connectionManager.getConnectionStats()
      expect(stats.successfulConnections).toBe(1)
      expect(stats.connectionDuration).toBeGreaterThan(0)
      
      // Failed connection
      connectionManager.disconnect()
      const failPromise = connectionManager.connect()
      const ws2 = wsTestUtils.getLastCreatedWebSocket()
      if (ws2) {
        ws2.simulateError(new Error('Connection failed'))
      }
      
      await expect(failPromise).rejects.toThrow()
      
      stats = connectionManager.getConnectionStats()
      expect(stats.failedConnections).toBe(1)
    })

    it('should track heartbeat statistics', async () => {
      vi.useFakeTimers()
      
      await connectionManager.connect()
      const ws = wsTestUtils.getLastCreatedWebSocket()
      if (ws) {
        ws.simulateOpen()
      }
      await wsTestUtils.waitFor(() => connectionManager.isConnected(), 1000)
      
      // Trigger heartbeats
      vi.advanceTimersByTime(30000)
      if (ws) {
        ws.simulateMessage(JSON.stringify({ type: 'pong' }))
      }
      
      vi.advanceTimersByTime(30000)
      if (ws) {
        ws.simulateMessage(JSON.stringify({ type: 'pong' }))
      }
      
      const stats = connectionManager.getConnectionStats()
      expect(stats.heartbeatsSent).toBe(2)
      expect(stats.heartbeatsReceived).toBe(2)
      
      vi.useRealTimers()
    })

    it('should reset statistics', async () => {
      await connectionManager.connect()
      const ws = wsTestUtils.getLastCreatedWebSocket()
      if (ws) {
        ws.simulateOpen()
      }
      await wsTestUtils.waitFor(() => connectionManager.isConnected(), 1000)
      
      let stats = connectionManager.getConnectionStats()
      expect(stats.successfulConnections).toBe(1)
      
      connectionManager.resetStats()
      
      stats = connectionManager.getConnectionStats()
      expect(stats.successfulConnections).toBe(0)
      expect(stats.failedConnections).toBe(0)
      expect(stats.heartbeatsSent).toBe(0)
      expect(stats.heartbeatsReceived).toBe(0)
    })
  })

  describe('Error Handling', () => {
    it('should handle send errors gracefully', async () => {
      await connectionManager.connect()
      const ws = wsTestUtils.getLastCreatedWebSocket()
      if (ws) {
        ws.simulateOpen()
      }
      await wsTestUtils.waitFor(() => connectionManager.isConnected(), 1000)
      
      const errors: string[] = []
      connectionManager.onError = (error) => errors.push(error)
      
      // Force send error during ping
      if (ws) {
        ws.simulateSendError(new Error('Send failed'))
      }
      
      vi.useFakeTimers()
      vi.advanceTimersByTime(30000) // Trigger heartbeat
      
      expect(errors.some(error => error.includes('Failed to send ping'))).toBe(true)
      
      vi.useRealTimers()
    })

    it('should handle malformed messages gracefully', async () => {
      await connectionManager.connect()
      const ws = wsTestUtils.getLastCreatedWebSocket()
      if (ws) {
        ws.simulateOpen()
      }
      await wsTestUtils.waitFor(() => connectionManager.isConnected(), 1000)
      
      // Send malformed JSON
      if (ws) {
        ws.simulateMessage('invalid json {')
      }
      
      // Should not crash
      expect(connectionManager.isConnected()).toBe(true)
    })

    it('should handle edge cases in state management', () => {
      // Multiple disconnections
      connectionManager.disconnect()
      connectionManager.disconnect() // Should not throw
      
      expect(connectionManager.getConnectionState()).toBe('disconnected')
      
      // Reset stats when not connected
      connectionManager.resetStats() // Should not throw
    })
  })
})