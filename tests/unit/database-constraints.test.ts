// 資料庫約束測試
import { describe, it, expect, beforeEach, afterEach } from 'vitest'
import { getDatabaseTestManager } from '../utils/database-test-manager'
import { getTestDataFactory } from '../utils/test-data-factory'

describe('Database Constraints', () => {
  const dbManager = getDatabaseTestManager()
  const dataFactory = getTestDataFactory()

  beforeEach(async () => {
    await dbManager.initializeTestDatabase()
    await dbManager.cleanupTestData()
  })

  afterEach(async () => {
    await dbManager.cleanupTestData()
  })

  describe('Foreign Key Constraints', () => {
    it('should enforce foreign key constraint when creating file without project', async () => {
      // 嘗試建立檔案但不提供有效的 projectId
      const nonExistentProjectId = 'non-existent-project-id'
      
      await expect(
        dataFactory.createFile(nonExistentProjectId, {
          name: 'test.vue',
          content: '<template><div>test</div></template>'
        })
      ).rejects.toThrow()
    })

    it('should allow creating file with valid project reference', async () => {
      // 先建立專案
      const project = await dataFactory.createProject({
        name: '測試專案',
        description: '用於測試外鍵約束'
      })

      // 再建立檔案
      const file = await dataFactory.createFile(project.id, {
        name: 'test.vue',
        content: '<template><div>test</div></template>'
      })

      expect(file).toBeDefined()
      expect(file.projectId).toBe(project.id)
      expect(file.name).toBe('test.vue')
    })

    it('should cascade delete files when project is deleted', async () => {
      // 建立專案和檔案
      const { project, files } = await dataFactory.createProjectWithFiles(3)
      
      // 確認檔案存在
      expect(files).toHaveLength(3)
      
      // 刪除專案
      await dbManager.getPrismaClient().project.delete({
        where: { id: project.id }
      })
      
      // 確認檔案也被刪除 (cascade delete)
      const remainingFiles = await dbManager.getPrismaClient().file.findMany({
        where: { projectId: project.id }
      })
      
      expect(remainingFiles).toHaveLength(0)
    })
  })

  describe('Unique Constraints', () => {
    it('should enforce unique constraint on file name within project', async () => {
      const project = await dataFactory.createProject()
      
      // 建立第一個檔案
      await dataFactory.createFile(project.id, {
        name: 'duplicate.vue',
        content: '<template><div>first</div></template>'
      })
      
      // 嘗試建立同名檔案應該失敗
      await expect(
        dataFactory.createFile(project.id, {
          name: 'duplicate.vue',
          content: '<template><div>second</div></template>'
        })
      ).rejects.toThrow()
    })

    it('should allow same file name in different projects', async () => {
      const project1 = await dataFactory.createProject({ name: '專案1' })
      const project2 = await dataFactory.createProject({ name: '專案2' })
      
      // 在不同專案中建立同名檔案應該成功
      const file1 = await dataFactory.createFile(project1.id, {
        name: 'same-name.vue',
        content: '<template><div>project1</div></template>'
      })
      
      const file2 = await dataFactory.createFile(project2.id, {
        name: 'same-name.vue',
        content: '<template><div>project2</div></template>'
      })
      
      expect(file1.name).toBe('same-name.vue')
      expect(file2.name).toBe('same-name.vue')
      expect(file1.projectId).toBe(project1.id)
      expect(file2.projectId).toBe(project2.id)
    })

    it('should handle unique constraint validation in factory', async () => {
      const project = await dataFactory.createProject()
      
      // 建立第一個檔案
      await dataFactory.createUniqueFile(project.id, 'unique-test.vue')
      
      // 嘗試建立同名檔案應該拋出自定義錯誤
      await expect(
        dataFactory.createUniqueFile(project.id, 'unique-test.vue')
      ).rejects.toThrow('File with name "unique-test.vue" already exists')
    })
  })

  describe('Data Integrity', () => {
    it('should maintain referential integrity during complex operations', async () => {
      const { projects, files } = await dataFactory.createComplexTestData()
      
      expect(projects).toHaveLength(2)
      expect(files).toHaveLength(3)
      
      // 驗證所有檔案都有有效的專案引用
      for (const file of files) {
        const project = projects.find(p => p.id === file.projectId)
        expect(project).toBeDefined()
      }
    })

    it('should handle concurrent data creation correctly', async () => {
      const project = await dataFactory.createProject()
      
      // 並發建立多個檔案
      const filePromises = Array.from({ length: 5 }, (_, i) =>
        dataFactory.createFile(project.id, {
          name: `concurrent-${i}.vue`,
          content: `<template><div>concurrent ${i}</div></template>`
        })
      )
      
      const files = await Promise.all(filePromises)
      
      expect(files).toHaveLength(5)
      files.forEach((file, index) => {
        expect(file.name).toBe(`concurrent-${index}.vue`)
        expect(file.projectId).toBe(project.id)
      })
    })
  })

  describe('Database Statistics', () => {
    it('should provide accurate database statistics', async () => {
      // 初始狀態
      let stats = await dbManager.getDatabaseStats()
      expect(stats.projectCount).toBe(0)
      expect(stats.fileCount).toBe(0)
      
      // 建立測試資料
      await dataFactory.createProjectWithFiles(3)
      await dataFactory.createProjectWithFiles(2)
      
      // 檢查統計
      stats = await dbManager.getDatabaseStats()
      expect(stats.projectCount).toBe(2)
      expect(stats.fileCount).toBe(5)
    })
  })
})