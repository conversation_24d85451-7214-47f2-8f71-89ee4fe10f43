import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { TestReporter, TestResults, CoverageData } from '../utils/test-reporter'
import { CoverageCollector } from '../utils/coverage-collector'
import { PerformanceMonitor } from '../utils/performance-monitor'
import { writeFile, mkdir } from 'fs/promises'
import { existsSync } from 'fs'

// Mock file system operations
vi.mock('fs/promises')
vi.mock('fs')

describe('TestReporter', () => {
  let reporter: TestReporter
  let mockResults: TestResults
  let mockCoverage: CoverageData

  beforeEach(() => {
    reporter = new TestReporter({
      outputDir: 'test-reports',
      formats: ['html', 'json', 'xml'],
      includePerformance: true,
      includeCoverage: true
    })

    mockResults = {
      summary: {
        total: 100,
        passed: 85,
        failed: 10,
        skipped: 5,
        duration: 45000,
        startTime: new Date('2024-01-01T10:00:00Z'),
        endTime: new Date('2024-01-01T10:00:45Z'),
        environment: 'test',
        nodeVersion: '18.0.0',
        vitestVersion: '1.0.0'
      },
      suites: [
        {
          name: 'Unit Tests',
          file: 'tests/unit/example.test.ts',
          tests: [
            {
              name: 'should pass basic test',
              status: 'passed',
              duration: 50,
              assertions: 3,
              startTime: new Date('2024-01-01T10:00:00Z'),
              endTime: new Date('2024-01-01T10:00:00.050Z')
            },
            {
              name: 'should fail with error',
              status: 'failed',
              duration: 100,
              assertions: 2,
              startTime: new Date('2024-01-01T10:00:01Z'),
              endTime: new Date('2024-01-01T10:00:01.100Z'),
              error: {
                type: 'AssertionError',
                message: 'Expected true but got false',
                stack: 'Error stack trace...'
              }
            }
          ],
          duration: 150,
          status: 'failed',
          startTime: new Date('2024-01-01T10:00:00Z'),
          endTime: new Date('2024-01-01T10:00:00.150Z')
        }
      ],
      performance: {
        totalExecutionTime: 45000,
        averageTestTime: 450,
        slowestTests: [
          { name: 'slow test', file: 'test.ts', duration: 2000, memoryDelta: 1024, category: 'integration', timestamp: new Date() }
        ],
        fastestTests: [
          { name: 'fast test', file: 'test.ts', duration: 10, memoryDelta: 100, category: 'unit', timestamp: new Date() }
        ],
        memoryUsage: {
          heapUsed: 50 * 1024 * 1024,
          heapTotal: 100 * 1024 * 1024,
          external: 5 * 1024 * 1024,
          rss: 150 * 1024 * 1024,
          peak: 75 * 1024 * 1024,
          gc: { collections: 5, duration: 100 }
        },
        testDistribution: {
          unit: 60,
          integration: 30,
          server: 10,
          total: 100
        },
        regressionAlerts: [],
        trends: {
          executionTime: [],
          memoryUsage: [],
          testCount: [],
          regressionCount: []
        }
      }
    }

    mockCoverage = {
      lines: { total: 1000, covered: 850, percentage: 85 },
      functions: { total: 200, covered: 180, percentage: 90 },
      branches: { total: 300, covered: 240, percentage: 80 },
      statements: { total: 1200, covered: 1080, percentage: 90 },
      files: [
        {
          path: 'src/example.ts',
          lines: { total: 100, covered: 85, percentage: 85 },
          functions: { total: 10, covered: 9, percentage: 90 },
          branches: { total: 20, covered: 16, percentage: 80 },
          statements: { total: 120, covered: 108, percentage: 90 },
          uncoveredLines: [15, 23, 45]
        }
      ],
      thresholds: {
        lines: 80,
        functions: 80,
        branches: 70,
        statements: 80
      }
    }

    // Mock file system
    vi.mocked(existsSync).mockReturnValue(false)
    vi.mocked(mkdir).mockResolvedValue(undefined)
    vi.mocked(writeFile).mockResolvedValue(undefined)
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('generateReport', () => {
    it('should generate comprehensive test report', async () => {
      const report = await reporter.generateReport(mockResults)

      expect(report).toBeDefined()
      expect(report.metadata.generatedAt).toBeInstanceOf(Date)
      expect(report.metadata.version).toBe('1.0.0')
      expect(report.metadata.environment).toBe('test')
      expect(report.results).toBe(mockResults)
      expect(report.insights).toBeDefined()
    })

    it('should generate correct insights', async () => {
      const report = await reporter.generateReport(mockResults)

      expect(report.insights.overallHealth).toBe('fair') // 85% pass rate
      expect(report.insights.recommendations).toContain('Improve test pass rate (currently 85.0%)')
      expect(report.insights.trends.passRate).toBe(85)
      expect(report.insights.trends.averageDuration).toBe(450)
    })

    it('should handle excellent health rating', async () => {
      const excellentResults = {
        ...mockResults,
        summary: {
          ...mockResults.summary,
          passed: 98,
          failed: 2,
          duration: 30000 // Faster execution
        }
      }

      const report = await reporter.generateReport(excellentResults)
      expect(report.insights.overallHealth).toBe('excellent')
    })
  })

  describe('generateCoverageReport', () => {
    it('should generate coverage report with analysis', async () => {
      const coverageReport = await reporter.generateCoverageReport(mockCoverage)

      expect(coverageReport).toBeDefined()
      expect(coverageReport.metadata.type).toBe('coverage')
      expect(coverageReport.coverage).toBe(mockCoverage)
      expect(coverageReport.analysis).toBeDefined()
      expect(coverageReport.recommendations).toBeDefined()
    })

    it('should provide coverage analysis', async () => {
      const coverageReport = await reporter.generateCoverageReport(mockCoverage)

      expect(coverageReport.analysis.overallScore).toBeCloseTo(86.25) // Average of all percentages
      expect(coverageReport.analysis.fileDistribution.total).toBe(1)
      expect(coverageReport.analysis.fileDistribution.wellCovered).toBe(1) // 85% > 80%
      expect(coverageReport.analysis.fileDistribution.poorlyCovered).toBe(0) // None < 50%
    })

    it('should generate coverage recommendations', async () => {
      const poorCoverage = {
        ...mockCoverage,
        lines: { total: 1000, covered: 600, percentage: 60 }, // Below threshold
        functions: { total: 200, covered: 140, percentage: 70 } // Below threshold
      }

      const coverageReport = await reporter.generateCoverageReport(poorCoverage)

      expect(coverageReport.recommendations).toContain('Increase line coverage from 60.0% to 80%')
      expect(coverageReport.recommendations).toContain('Increase function coverage from 70.0% to 80%')
    })
  })

  describe('exportToHTML', () => {
    it('should export report to HTML format', async () => {
      const report = await reporter.generateReport(mockResults)
      const filePath = await reporter.exportToHTML(report)

      expect(filePath).toBe('test-reports/test-report.html')
      expect(mkdir).toHaveBeenCalledWith('test-reports', { recursive: true })
      expect(writeFile).toHaveBeenCalledWith(
        'test-reports/test-report.html',
        expect.stringContaining('<!DOCTYPE html>'),
        'utf-8'
      )
    })

    it('should generate valid HTML content', async () => {
      const report = await reporter.generateReport(mockResults)
      await reporter.exportToHTML(report)

      const htmlContent = vi.mocked(writeFile).mock.calls[0][1] as string
      
      expect(htmlContent).toContain('<title>Test Report')
      expect(htmlContent).toContain('Overall Health')
      expect(htmlContent).toContain('85') // Pass count
      expect(htmlContent).toContain('10') // Fail count
      expect(htmlContent).toContain('5') // Skip count
      expect(htmlContent).toContain('45.00s') // Duration
    })

    it('should include coverage information when available', async () => {
      const resultsWithCoverage = { ...mockResults, coverage: mockCoverage }
      const report = await reporter.generateReport(resultsWithCoverage)
      await reporter.exportToHTML(report)

      const htmlContent = vi.mocked(writeFile).mock.calls[0][1] as string
      
      expect(htmlContent).toContain('Code Coverage')
      expect(htmlContent).toContain('85.0%') // Line coverage
      expect(htmlContent).toContain('90.0%') // Function coverage
    })
  })

  describe('exportToJSON', () => {
    it('should export report to JSON format', async () => {
      const report = await reporter.generateReport(mockResults)
      const filePath = await reporter.exportToJSON(report)

      expect(filePath).toBe('test-reports/test-report.json')
      expect(writeFile).toHaveBeenCalledWith(
        'test-reports/test-report.json',
        expect.stringContaining('"metadata"'),
        'utf-8'
      )
    })

    it('should generate valid JSON content', async () => {
      const report = await reporter.generateReport(mockResults)
      await reporter.exportToJSON(report)

      const jsonContent = vi.mocked(writeFile).mock.calls[0][1] as string
      const parsedJson = JSON.parse(jsonContent)
      
      expect(parsedJson.metadata).toBeDefined()
      expect(parsedJson.results).toBeDefined()
      expect(parsedJson.insights).toBeDefined()
    })
  })

  describe('exportToXML', () => {
    it('should export report to XML format', async () => {
      const report = await reporter.generateReport(mockResults)
      const filePath = await reporter.exportToXML(report)

      expect(filePath).toBe('test-reports/test-report.xml')
      expect(writeFile).toHaveBeenCalledWith(
        'test-reports/test-report.xml',
        expect.stringContaining('<?xml version="1.0"'),
        'utf-8'
      )
    })

    it('should generate valid XML content', async () => {
      const report = await reporter.generateReport(mockResults)
      await reporter.exportToXML(report)

      const xmlContent = vi.mocked(writeFile).mock.calls[0][1] as string
      
      expect(xmlContent).toContain('<testReport>')
      expect(xmlContent).toContain('<metadata>')
      expect(xmlContent).toContain('<summary>')
      expect(xmlContent).toContain('<testSuites>')
    })

    it('should escape XML special characters', async () => {
      const resultsWithSpecialChars = {
        ...mockResults,
        suites: [{
          ...mockResults.suites[0],
          name: 'Test with <special> & "characters"',
          tests: [{
            ...mockResults.suites[0].tests[0],
            name: 'Test with <tags> & "quotes"'
          }]
        }]
      }

      const report = await reporter.generateReport(resultsWithSpecialChars)
      await reporter.exportToXML(report)

      const xmlContent = vi.mocked(writeFile).mock.calls[0][1] as string
      
      expect(xmlContent).toContain('&lt;special&gt;')
      expect(xmlContent).toContain('&amp;')
      expect(xmlContent).toContain('&quot;')
    })
  })

  describe('generateJUnitReport', () => {
    it('should generate JUnit XML format', async () => {
      const filePath = await reporter.generateJUnitReport(mockResults)

      expect(filePath).toBe('test-reports/junit-report.xml')
      expect(writeFile).toHaveBeenCalledWith(
        'test-reports/junit-report.xml',
        expect.stringContaining('<testsuites'),
        'utf-8'
      )
    })

    it('should include test failure information', async () => {
      await reporter.generateJUnitReport(mockResults)

      const xmlContent = vi.mocked(writeFile).mock.calls[0][1] as string
      
      expect(xmlContent).toContain('<failure')
      expect(xmlContent).toContain('Expected true but got false')
      expect(xmlContent).toContain('AssertionError')
    })
  })

  describe('generateCoverageXML', () => {
    it('should generate coverage XML report', async () => {
      const filePath = await reporter.generateCoverageXML(mockCoverage)

      expect(filePath).toBe('test-reports/coverage-report.xml')
      expect(writeFile).toHaveBeenCalledWith(
        'test-reports/coverage-report.xml',
        expect.stringContaining('<coverage>'),
        'utf-8'
      )
    })

    it('should include coverage summary and file details', async () => {
      await reporter.generateCoverageXML(mockCoverage)

      const xmlContent = vi.mocked(writeFile).mock.calls[0][1] as string
      
      expect(xmlContent).toContain('<summary>')
      expect(xmlContent).toContain('<files>')
      expect(xmlContent).toContain('src/example.ts')
      expect(xmlContent).toContain('85.00') // Line coverage percentage
    })
  })
})

describe('CoverageCollector', () => {
  let collector: CoverageCollector

  beforeEach(() => {
    collector = new CoverageCollector({
      include: ['src/**/*.ts'],
      exclude: ['src/**/*.test.ts'],
      thresholds: {
        lines: 80,
        functions: 80,
        branches: 70,
        statements: 80
      }
    })
  })

  describe('checkThresholds', () => {
    it('should pass when coverage meets thresholds', () => {
      const coverage: CoverageData = {
        lines: { total: 100, covered: 85, percentage: 85 },
        functions: { total: 20, covered: 18, percentage: 90 },
        branches: { total: 30, covered: 25, percentage: 83 },
        statements: { total: 120, covered: 100, percentage: 83 },
        files: [],
        thresholds: { lines: 80, functions: 80, branches: 70, statements: 80 }
      }

      const result = collector.checkThresholds(coverage)

      expect(result.passed).toBe(true)
      expect(result.failures).toHaveLength(0)
      expect(result.summary.lines).toBe(true)
      expect(result.summary.functions).toBe(true)
      expect(result.summary.branches).toBe(true)
      expect(result.summary.statements).toBe(true)
    })

    it('should fail when coverage below thresholds', () => {
      const coverage: CoverageData = {
        lines: { total: 100, covered: 70, percentage: 70 }, // Below 80%
        functions: { total: 20, covered: 15, percentage: 75 }, // Below 80%
        branches: { total: 30, covered: 18, percentage: 60 }, // Below 70%
        statements: { total: 120, covered: 90, percentage: 75 }, // Below 80%
        files: [],
        thresholds: { lines: 80, functions: 80, branches: 70, statements: 80 }
      }

      const result = collector.checkThresholds(coverage)

      expect(result.passed).toBe(false)
      expect(result.failures).toHaveLength(4)
      expect(result.failures[0].type).toBe('lines')
      expect(result.failures[0].actual).toBe(70)
      expect(result.failures[0].expected).toBe(80)
    })
  })

  describe('getCoverageStats', () => {
    it('should calculate coverage statistics', () => {
      const coverage: CoverageData = {
        lines: { total: 200, covered: 170, percentage: 85 },
        functions: { total: 40, covered: 36, percentage: 90 },
        branches: { total: 60, covered: 48, percentage: 80 },
        statements: { total: 240, covered: 216, percentage: 90 },
        files: [
          {
            path: 'src/good.ts',
            lines: { total: 100, covered: 90, percentage: 90 },
            functions: { total: 20, covered: 19, percentage: 95 },
            branches: { total: 30, covered: 27, percentage: 90 },
            statements: { total: 120, covered: 114, percentage: 95 },
            uncoveredLines: [5, 10]
          },
          {
            path: 'src/poor.ts',
            lines: { total: 100, covered: 40, percentage: 40 },
            functions: { total: 20, covered: 8, percentage: 40 },
            branches: { total: 30, covered: 12, percentage: 40 },
            statements: { total: 120, covered: 48, percentage: 40 },
            uncoveredLines: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
          }
        ],
        thresholds: { lines: 80, functions: 80, branches: 70, statements: 80 }
      }

      const stats = collector.getCoverageStats(coverage)

      expect(stats.totalFiles).toBe(2)
      expect(stats.wellCoveredFiles).toBe(1) // good.ts has >80% average
      expect(stats.poorlyCoveredFiles).toBe(1) // poor.ts has <50% average
      expect(stats.overallScore).toBeCloseTo(86.25) // Average of all percentages
      expect(stats.hotspots).toHaveLength(1) // poor.ts should be a hotspot
    })
  })
})

describe('PerformanceMonitor', () => {
  let monitor: PerformanceMonitor

  beforeEach(() => {
    monitor = new PerformanceMonitor({
      regressionThresholds: {
        low: 10,
        medium: 25,
        high: 50,
        critical: 100
      },
      slowTestThreshold: 1000
    })
  })

  describe('recordTestPerformance', () => {
    it('should record test performance data', () => {
      monitor.recordTestPerformance('test1', 'file1.test.ts', 500, 'unit')
      monitor.recordTestPerformance('test2', 'file2.test.ts', 1500, 'integration')

      // Performance data should be recorded internally
      expect(() => monitor.recordTestPerformance('test3', 'file3.test.ts', 200, 'unit')).not.toThrow()
    })
  })

  describe('startMonitoring', () => {
    it('should create performance session', () => {
      const session = monitor.startMonitoring()

      expect(session).toBeDefined()
      expect(typeof session.getDuration).toBe('function')
      expect(typeof session.end).toBe('function')
    })
  })
})