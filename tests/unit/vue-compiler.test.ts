import { describe, it, expect, vi, beforeEach } from 'vitest'
import {
  compileVueComponent,
  validateVueComponent,
  extractComponentDependencies,
  generatePreviewCode,
  isValidVueSFC,
  formatVueSFC
} from '../../src/lib/vue-compiler'

// Mock @vue/compiler-sfc
vi.mock('@vue/compiler-sfc', () => ({
  parse: vi.fn(),
  compileTemplate: vi.fn(),
  compileScript: vi.fn(),
  compileStyle: vi.fn()
}))

import { parse, compileTemplate, compileScript, compileStyle } from '@vue/compiler-sfc'

describe('vue-compiler', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('compileVueComponent', () => {
    it('should compile a basic Vue SFC successfully', async () => {
      const mockDescriptor = {
        template: {
          content: '<div>{{ message }}</div>'
        },
        script: {
          content: 'export default { data() { return { message: "Hello" } } }'
        },
        styles: [{
          content: '.test { color: red; }',
          scoped: false,
          module: null,
          lang: 'css'
        }]
      }

      vi.mocked(parse).mockReturnValue({
        descriptor: mockDescriptor,
        errors: []
      })

      vi.mocked(compileScript).mockReturnValue({
        content: 'const __default__ = { data() { return { message: "Hello" } } }'
      })

      vi.mocked(compileTemplate).mockReturnValue({
        code: 'function render() { return h("div", message) }',
        errors: [],
        tips: []
      })

      vi.mocked(compileStyle).mockReturnValue({
        code: '.test { color: red; }',
        errors: []
      })

      const source = `
<template>
  <div>{{ message }}</div>
</template>

<script>
export default {
  data() {
    return {
      message: 'Hello'
    }
  }
}
</script>

<style>
.test {
  color: red;
}
</style>
      `.trim()

      const result = await compileVueComponent(source, 'TestComponent.vue')

      expect(result.errors).toHaveLength(0)
      expect(result.script).toContain('PreviewComponent')
      expect(result.template).toBe('function render() { return h("div", message) }')
      expect(result.styles).toHaveLength(1)
      expect(result.styles[0]).toBe('.test { color: red; }')
    })

    it('should handle parse errors', async () => {
      const parseError = {
        message: 'Template syntax error',
        loc: {
          start: { line: 1, column: 5 },
          source: 'TestComponent.vue'
        }
      }

      vi.mocked(parse).mockReturnValue({
        descriptor: {} as any,
        errors: [parseError]
      })

      const result = await compileVueComponent('<template><div></template>', 'TestComponent.vue')

      expect(result.errors).toHaveLength(1)
      expect(result.errors[0].message).toBe('Template syntax error')
      expect(result.errors[0].line).toBe(1)
      expect(result.errors[0].column).toBe(5)
    })

    it('should handle script compilation errors', async () => {
      const mockDescriptor = {
        script: {
          content: 'export default { invalid syntax }'
        },
        template: null,
        styles: []
      }

      vi.mocked(parse).mockReturnValue({
        descriptor: mockDescriptor,
        errors: []
      })

      vi.mocked(compileScript).mockImplementation(() => {
        throw new Error('Script compilation failed')
      })

      const result = await compileVueComponent('<script>invalid</script>', 'TestComponent.vue')

      expect(result.errors).toHaveLength(1)
      expect(result.errors[0].message).toBe('Script compilation failed')
    })

    it('should handle template compilation errors', async () => {
      const mockDescriptor = {
        template: {
          content: '<div>{{ invalid.syntax }}</div>'
        },
        script: null,
        styles: []
      }

      const templateError = {
        message: 'Invalid template syntax',
        loc: {
          start: { line: 1, column: 10 }
        }
      }

      vi.mocked(parse).mockReturnValue({
        descriptor: mockDescriptor,
        errors: []
      })

      vi.mocked(compileTemplate).mockReturnValue({
        code: '',
        errors: [templateError],
        tips: []
      })

      const result = await compileVueComponent('<template><div>{{ invalid }}</div></template>')

      expect(result.errors).toHaveLength(1)
      expect(result.errors[0].message).toBe('Invalid template syntax')
    })

    it('should handle style compilation errors', async () => {
      const mockDescriptor = {
        template: null,
        script: null,
        styles: [{
          content: '.invalid { color: ; }',
          scoped: false,
          module: null,
          lang: 'css'
        }]
      }

      const styleError = {
        message: 'Invalid CSS syntax'
      }

      vi.mocked(parse).mockReturnValue({
        descriptor: mockDescriptor,
        errors: []
      })

      vi.mocked(compileStyle).mockReturnValue({
        code: '',
        errors: [styleError]
      })

      const result = await compileVueComponent('<style>.invalid { color: ; }</style>')

      expect(result.errors).toHaveLength(1)
      expect(result.errors[0].message).toBe('Invalid CSS syntax')
    })

    it('should generate default script when no script block exists', async () => {
      const mockDescriptor = {
        template: {
          content: '<div>Hello</div>'
        },
        script: null,
        scriptSetup: null,
        styles: []
      }

      vi.mocked(parse).mockReturnValue({
        descriptor: mockDescriptor,
        errors: []
      })

      vi.mocked(compileTemplate).mockReturnValue({
        code: 'function render() { return h("div", "Hello") }',
        errors: [],
        tips: []
      })

      const result = await compileVueComponent('<template><div>Hello</div></template>')

      expect(result.script).toContain('const PreviewComponent = {')
      expect(result.script).toContain('name: \'PreviewComponent\'')
    })

    it('should handle multiple styles', async () => {
      const mockDescriptor = {
        template: null,
        script: null,
        styles: [
          {
            content: '.style1 { color: red; }',
            scoped: false,
            module: null,
            lang: 'css'
          },
          {
            content: '.style2 { color: blue; }',
            scoped: true,
            module: null,
            lang: 'css'
          }
        ]
      }

      vi.mocked(parse).mockReturnValue({
        descriptor: mockDescriptor,
        errors: []
      })

      vi.mocked(compileStyle)
        .mockReturnValueOnce({
          code: '.style1 { color: red; }',
          errors: []
        })
        .mockReturnValueOnce({
          code: '.style2[data-v-123] { color: blue; }',
          errors: []
        })

      const result = await compileVueComponent('<style>.style1 { color: red; }</style><style scoped>.style2 { color: blue; }</style>')

      expect(result.styles).toHaveLength(2)
      expect(result.styles[0]).toBe('.style1 { color: red; }')
      expect(result.styles[1]).toBe('.style2[data-v-123] { color: blue; }')
    })

    it('should handle template warnings', async () => {
      const mockDescriptor = {
        template: {
          content: '<div>{{ message }}</div>'
        },
        script: null,
        styles: []
      }

      const templateWarning = {
        message: 'Unused variable',
        loc: {
          start: { line: 1, column: 8 }
        }
      }

      vi.mocked(parse).mockReturnValue({
        descriptor: mockDescriptor,
        errors: []
      })

      vi.mocked(compileTemplate).mockReturnValue({
        code: 'function render() { return h("div", message) }',
        errors: [],
        tips: [templateWarning]
      })

      const result = await compileVueComponent('<template><div>{{ message }}</div></template>')

      expect(result.warnings).toHaveLength(1)
      expect(result.warnings[0].message).toBe('Unused variable')
    })
  })

  describe('validateVueComponent', () => {
    it('should validate a correct Vue SFC', () => {
      const mockDescriptor = {
        template: {
          content: '<div>Hello</div>'
        },
        script: {
          content: 'export default { name: "Test" }'
        }
      }

      vi.mocked(parse).mockReturnValue({
        descriptor: mockDescriptor,
        errors: []
      })

      const result = validateVueComponent('<template><div>Hello</div></template><script>export default { name: "Test" }</script>')

      expect(result.isValid).toBe(true)
      expect(result.errors).toHaveLength(0)
    })

    it('should detect parse errors', () => {
      const parseError = {
        message: 'Invalid syntax',
        loc: {
          start: { line: 1, column: 1 }
        }
      }

      vi.mocked(parse).mockReturnValue({
        descriptor: {} as any,
        errors: [parseError]
      })

      const result = validateVueComponent('<template><div></template>')

      expect(result.isValid).toBe(false)
      expect(result.errors).toHaveLength(1)
      expect(result.errors[0].message).toBe('Invalid syntax')
    })

    it('should warn about empty template', () => {
      const mockDescriptor = {
        template: {
          content: '   '
        },
        script: {
          content: 'export default {}'
        }
      }

      vi.mocked(parse).mockReturnValue({
        descriptor: mockDescriptor,
        errors: []
      })

      const result = validateVueComponent('<template>   </template><script>export default {}</script>')

      expect(result.isValid).toBe(true)
      expect(result.warnings).toHaveLength(1)
      expect(result.warnings[0].message).toBe('模板內容為空')
    })

    it('should warn about empty script', () => {
      const mockDescriptor = {
        template: {
          content: '<div>Hello</div>'
        },
        script: {
          content: '   '
        }
      }

      vi.mocked(parse).mockReturnValue({
        descriptor: mockDescriptor,
        errors: []
      })

      const result = validateVueComponent('<template><div>Hello</div></template><script>   </script>')

      expect(result.isValid).toBe(true)
      expect(result.warnings).toHaveLength(1)
      expect(result.warnings[0].message).toBe('腳本內容為空')
    })

    it('should handle validation exceptions', () => {
      vi.mocked(parse).mockImplementation(() => {
        throw new Error('Parse failed')
      })

      const result = validateVueComponent('<template><div>Test</div></template>')

      expect(result.isValid).toBe(false)
      expect(result.errors).toHaveLength(1)
      expect(result.errors[0].message).toBe('Parse failed')
    })
  })

  describe('extractComponentDependencies', () => {
    it('should extract import dependencies', () => {
      const source = `
import Vue from 'vue'
import { ref } from 'vue'
import axios from 'axios'
import './local-file.js'
import '/absolute-path.js'
      `

      const dependencies = extractComponentDependencies(source)

      expect(dependencies).toContain('vue')
      expect(dependencies).toContain('axios')
      expect(dependencies).not.toContain('./local-file.js')
      expect(dependencies).not.toContain('/absolute-path.js')
    })

    it('should extract require dependencies', () => {
      const source = `
const Vue = require('vue')
const axios = require('axios')
const localFile = require('./local-file')
      `

      const dependencies = extractComponentDependencies(source)

      expect(dependencies).toContain('vue')
      expect(dependencies).toContain('axios')
      expect(dependencies).not.toContain('./local-file')
    })

    it('should deduplicate dependencies', () => {
      const source = `
import Vue from 'vue'
import { ref } from 'vue'
const axios = require('axios')
import axios from 'axios'
      `

      const dependencies = extractComponentDependencies(source)

      expect(dependencies.filter(dep => dep === 'vue')).toHaveLength(1)
      expect(dependencies.filter(dep => dep === 'axios')).toHaveLength(1)
    })

    it('should handle malformed source gracefully', () => {
      const source = 'invalid javascript syntax {'

      const dependencies = extractComponentDependencies(source)

      expect(Array.isArray(dependencies)).toBe(true)
      expect(dependencies).toHaveLength(0)
    })
  })

  describe('generatePreviewCode', () => {
    it('should generate complete preview code', () => {
      const compileResult = {
        script: 'const PreviewComponent = { name: "Test" };',
        template: 'function render() { return h("div", "Hello"); }',
        styles: ['.test { color: red; }', '.scoped { color: blue; }'],
        errors: [],
        warnings: []
      }

      const previewCode = generatePreviewCode(compileResult)

      expect(previewCode).toContain('const PreviewComponent = { name: "Test" };')
      expect(previewCode).toContain('function render() { return h("div", "Hello"); }')
      expect(previewCode).toContain('.test { color: red; }')
      expect(previewCode).toContain('.scoped { color: blue; }')
      expect(previewCode).toContain('window.PreviewComponent = PreviewComponent')
    })

    it('should handle empty styles', () => {
      const compileResult = {
        script: 'const PreviewComponent = {};',
        template: 'function render() { return h("div"); }',
        styles: [],
        errors: [],
        warnings: []
      }

      const previewCode = generatePreviewCode(compileResult)

      expect(previewCode).toContain('const styles = [];')
      expect(previewCode).toContain('window.PreviewComponent = PreviewComponent')
    })
  })

  describe('isValidVueSFC', () => {
    it('should recognize valid Vue SFC with template', () => {
      const source = '<template><div>Hello</div></template>'
      expect(isValidVueSFC(source)).toBe(true)
    })

    it('should recognize valid Vue SFC with script', () => {
      const source = '<script>export default { name: "Test" }</script>'
      expect(isValidVueSFC(source)).toBe(true)
    })

    it('should recognize valid Vue SFC with style', () => {
      const source = '<style>.test { color: red; }</style>'
      expect(isValidVueSFC(source)).toBe(true)
    })

    it('should recognize complete Vue SFC', () => {
      const source = `
<template>
  <div>Hello</div>
</template>

<script>
export default {
  name: 'Test'
}
</script>

<style>
.test {
  color: red;
}
</style>
      `
      expect(isValidVueSFC(source)).toBe(true)
    })

    it('should reject invalid content', () => {
      expect(isValidVueSFC('just plain text')).toBe(false)
      expect(isValidVueSFC('<div>not a vue sfc</div>')).toBe(false)
      expect(isValidVueSFC('')).toBe(false)
    })

    it('should handle case insensitive tags', () => {
      expect(isValidVueSFC('<TEMPLATE><div>Hello</div></TEMPLATE>')).toBe(true)
      expect(isValidVueSFC('<Script>export default {}</Script>')).toBe(true)
      expect(isValidVueSFC('<STYLE>.test{}</STYLE>')).toBe(true)
    })
  })

  describe('formatVueSFC', () => {
    it('should format Vue SFC properly', () => {
      const source = `<template>
<div>Hello</div>
</template>

<script>
export default {
  name: 'Test'
}
</script>

<style>
.test {
  color: red;
}
</style>`

      const formatted = formatVueSFC(source)

      expect(formatted).toContain('<template>\n  ')
      expect(formatted).toContain('\n</template>')
      expect(formatted).toContain('<script>\n')
      expect(formatted).toContain('\n</script>')
      expect(formatted).toContain('<style>\n')
      expect(formatted).toContain('\n</style>')
    })

    it('should handle malformed source gracefully', () => {
      const source = '<template><div>unclosed'

      const formatted = formatVueSFC(source)

      // Should return original source if formatting fails
      expect(formatted).toBe(source)
    })

    it('should trim whitespace', () => {
      const source = `  
      
<template><div>Hello</div></template>

      
      `

      const formatted = formatVueSFC(source)

      expect(formatted.startsWith('  ')).toBe(false)
      expect(formatted.endsWith('  ')).toBe(false)
    })
  })
})