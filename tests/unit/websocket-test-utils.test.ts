import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { WebSocketTestUtils } from '../utils/websocket-test-utils'
import { MockWebSocket } from '../utils/mock-websocket'

describe('WebSocketTestUtils', () => {
  let testUtils: WebSocketTestUtils

  beforeEach(() => {
    testUtils = new WebSocketTestUtils()
  })

  afterEach(() => {
    testUtils.reset()
  })

  describe('WebSocket Creation', () => {
    it('should create mock WebSocket with default config', () => {
      const ws = testUtils.createMockWebSocket()
      
      expect(ws).toBeInstanceOf(MockWebSocket)
      expect(ws.url).toBe('ws://localhost:8080/test')
      expect(ws.readyState).toBe(MockWebSocket.CONNECTING)
    })

    it('should create mock WebSocket with custom config', () => {
      const config = {
        url: 'ws://example.com:9000/custom',
        protocols: ['chat', 'echo'],
        connectionDelay: 100,
        networkDelay: 50,
        shouldFailConnection: false
      }

      const ws = testUtils.createMockWebSocket(config)
      
      expect(ws.url).toBe(config.url)
      expect(ws.protocol).toBe('chat') // First protocol in array
    })

    it('should configure connection failure', () => {
      const ws = testUtils.createMockWebSocket({
        shouldFailConnection: true
      })

      return new Promise<void>((resolve) => {
        ws.onerror = () => {
          expect(ws.readyState).toBe(MockWebSocket.CLOSED)
          resolve()
        }
      })
    })
  })

  describe('State Management', () => {
    let ws: MockWebSocket

    beforeEach(() => {
      ws = testUtils.createMockWebSocket()
    })

    it('should wait for WebSocket to reach OPEN state', async () => {
      await testUtils.waitForState(MockWebSocket.OPEN, 1000)
      expect(ws.readyState).toBe(MockWebSocket.OPEN)
    })

    it('should timeout when waiting for unreachable state', async () => {
      // Close the WebSocket immediately
      ws.close()
      
      await expect(
        testUtils.waitForState(MockWebSocket.OPEN, 100)
      ).rejects.toThrow('Timeout waiting for WebSocket state')
    })

    it('should wait for WebSocket to close', async () => {
      await testUtils.waitForState(MockWebSocket.OPEN)
      
      ws.close()
      await testUtils.waitForState(MockWebSocket.CLOSED, 1000)
      
      expect(ws.readyState).toBe(MockWebSocket.CLOSED)
    })
  })

  describe('Message Testing', () => {
    let ws: MockWebSocket

    beforeEach(async () => {
      ws = testUtils.createMockWebSocket()
      await testUtils.waitForState(MockWebSocket.OPEN)
    })

    it('should wait for specific message', async () => {
      const expectedMessage = 'Hello from server'
      
      // Simulate receiving the message after a delay
      setTimeout(() => {
        ws.simulateMessage(expectedMessage)
      }, 50)

      const messageEvent = await testUtils.waitForMessage(expectedMessage, 1000)
      expect(messageEvent.data).toBe(expectedMessage)
    })

    it('should wait for message matching regex', async () => {
      const messagePattern = /^Hello \w+$/
      
      setTimeout(() => {
        ws.simulateMessage('Hello World')
      }, 50)

      const messageEvent = await testUtils.waitForMessage(messagePattern, 1000)
      expect(messageEvent.data).toBe('Hello World')
    })

    it('should timeout when waiting for message that never comes', async () => {
      await expect(
        testUtils.waitForMessage('nonexistent message', 100)
      ).rejects.toThrow('Timeout waiting for message')
    })

    it('should send message and wait for response', async () => {
      const message = 'ping'
      const expectedResponse = /pong/

      // Set up auto-response
      ws.onmessage = (event) => {
        if (event.data === 'ping') {
          setTimeout(() => {
            ws.simulateMessage('pong')
          }, 10)
        }
      }

      const response = await testUtils.sendAndWaitForResponse(message, expectedResponse, 1000)
      expect(response?.data).toBe('pong')
    })

    it('should send message without waiting for response', async () => {
      const message = 'one-way message'
      
      const response = await testUtils.sendAndWaitForResponse(message)
      expect(response).toBeNull()
      
      // Verify message was sent
      testUtils.expectMessageSent(message)
    })
  })

  describe('Error Simulation', () => {
    let ws: MockWebSocket

    beforeEach(async () => {
      ws = testUtils.createMockWebSocket()
      await testUtils.waitForState(MockWebSocket.OPEN)
    })

    it('should simulate connection error', () => {
      const errorListener = vi.fn()
      ws.addEventListener('error', errorListener)

      testUtils.simulateConnectionError()

      expect(errorListener).toHaveBeenCalledOnce()
    })

    it('should simulate network delay', async () => {
      testUtils.simulateNetworkDelay(100)
      
      const startTime = Date.now()
      
      const messagePromise = new Promise<void>((resolve) => {
        ws.onmessage = () => {
          const elapsed = Date.now() - startTime
          expect(elapsed).toBeGreaterThanOrEqual(90)
          resolve()
        }
      })

      ws.simulateMessage('delayed message')
      await messagePromise
    })

    it('should simulate server disconnect', async () => {
      const closeListener = vi.fn()
      ws.addEventListener('close', closeListener)

      testUtils.simulateServerDisconnect(1001, 'Server restart')

      await testUtils.waitForState(MockWebSocket.CLOSED)
      
      expect(closeListener).toHaveBeenCalledOnce()
      const closeEvent = closeListener.mock.calls[0][0]
      expect(closeEvent.code).toBe(1001)
      expect(closeEvent.reason).toBe('Server restart')
    })
  })

  describe('Message Exchange Testing', () => {
    let ws: MockWebSocket

    beforeEach(async () => {
      ws = testUtils.createMockWebSocket()
      await testUtils.waitForState(MockWebSocket.OPEN)
    })

    it('should test successful message exchanges', async () => {
      const testCases = [
        {
          name: 'Simple text message',
          message: 'hello',
          expectedResponse: 'hello', // Echo response
          shouldFail: false
        },
        {
          name: 'JSON message',
          message: JSON.stringify({ type: 'ping' }),
          expectedResponse: JSON.stringify({ type: 'ping' }),
          shouldFail: false
        }
      ]

      // Set up echo behavior
      ws.onmessage = (event) => {
        setTimeout(() => {
          ws.simulateMessage(event.data)
        }, 10)
      }

      const results = await testUtils.testMessageExchange(testCases)
      
      expect(results).toHaveLength(2)
      expect(results[0].success).toBe(true)
      expect(results[1].success).toBe(true)
    })

    it('should test failed message exchanges', async () => {
      const testCases = [
        {
          name: 'Expected failure',
          message: 'fail me',
          shouldFail: true,
          timeout: 100
        }
      ]

      const results = await testUtils.testMessageExchange(testCases)
      
      expect(results).toHaveLength(1)
      expect(results[0].success).toBe(true) // Success because it was expected to fail
    })
  })

  describe('Connection Scenarios', () => {
    it('should test connection scenario', async () => {
      const scenario = {
        name: 'Connect and disconnect',
        steps: [
          { action: 'connect' as const, timeout: 1000 },
          { action: 'expect' as const, expectedState: MockWebSocket.OPEN },
          { action: 'send' as const, data: 'test message' },
          { action: 'wait' as const, timeout: 100 },
          { action: 'disconnect' as const, data: { code: 1000, reason: 'Normal close' } },
          { action: 'expect' as const, expectedState: MockWebSocket.CLOSED }
        ]
      }

      const result = await testUtils.testConnectionScenario(scenario)
      expect(result.success).toBe(true)
    })

    it('should handle scenario failures', async () => {
      const scenario = {
        name: 'Failing scenario',
        steps: [
          { action: 'connect' as const, timeout: 1000 },
          { action: 'expect' as const, expectedState: MockWebSocket.CLOSED } // This should fail
        ]
      }

      const result = await testUtils.testConnectionScenario(scenario)
      expect(result.success).toBe(false)
      expect(result.error).toContain('Expected state')
    })
  })

  describe('History Tracking', () => {
    let ws: MockWebSocket

    beforeEach(async () => {
      ws = testUtils.createMockWebSocket()
      await testUtils.waitForState(MockWebSocket.OPEN)
    })

    it('should track message history', async () => {
      // Send some messages
      ws.send('message 1')
      ws.send('message 2')
      
      // Simulate receiving messages
      ws.simulateMessage('response 1')
      ws.simulateMessage('response 2')

      await new Promise(resolve => setTimeout(resolve, 50))

      const messageHistory = testUtils.getMessageHistory()
      
      expect(messageHistory).toHaveLength(4)
      expect(messageHistory.filter(m => m.type === 'sent')).toHaveLength(2)
      expect(messageHistory.filter(m => m.type === 'received')).toHaveLength(2)
    })

    it('should track connection history', async () => {
      // Connection should already be open
      ws.close()
      await testUtils.waitForState(MockWebSocket.CLOSED)

      const connectionHistory = testUtils.getConnectionHistory()
      
      expect(connectionHistory.length).toBeGreaterThan(0)
      expect(connectionHistory.some(h => h.event === 'open')).toBe(true)
      expect(connectionHistory.some(h => h.event === 'close')).toBe(true)
    })

    it('should clear history', async () => {
      ws.send('test message')
      ws.simulateMessage('test response')

      await new Promise(resolve => setTimeout(resolve, 50))

      expect(testUtils.getMessageHistory()).toHaveLength(2)
      
      testUtils.clearHistory()
      
      expect(testUtils.getMessageHistory()).toHaveLength(0)
      expect(testUtils.getConnectionHistory()).toHaveLength(0)
    })
  })

  describe('Assertions', () => {
    let ws: MockWebSocket

    beforeEach(async () => {
      ws = testUtils.createMockWebSocket()
      await testUtils.waitForState(MockWebSocket.OPEN)
    })

    it('should assert WebSocket state', () => {
      expect(() => {
        testUtils.expectState(MockWebSocket.OPEN)
      }).not.toThrow()

      expect(() => {
        testUtils.expectState(MockWebSocket.CLOSED)
      }).toThrow('Expected WebSocket state')
    })

    it('should assert message was sent', async () => {
      const message = 'test message'
      ws.send(message)

      await new Promise(resolve => setTimeout(resolve, 50))

      expect(() => {
        testUtils.expectMessageSent(message)
      }).not.toThrow()

      expect(() => {
        testUtils.expectMessageSent('nonexistent message')
      }).toThrow('Expected message')
    })

    it('should assert message was received', async () => {
      const message = 'received message'
      ws.simulateMessage(message)

      await new Promise(resolve => setTimeout(resolve, 50))

      expect(() => {
        testUtils.expectMessageReceived(message)
      }).not.toThrow()

      expect(() => {
        testUtils.expectMessageReceived('nonexistent message')
      }).toThrow('Expected message')
    })
  })

  describe('Heartbeat Testing', () => {
    let ws: MockWebSocket

    beforeEach(async () => {
      ws = testUtils.createMockWebSocket()
      await testUtils.waitForState(MockWebSocket.OPEN)
    })

    it('should create and manage heartbeat test', async () => {
      const heartbeat = testUtils.createHeartbeatTest(100) // 100ms interval

      heartbeat.start()

      // Wait for a few heartbeats
      await new Promise(resolve => setTimeout(resolve, 350))

      heartbeat.stop()

      const stats = heartbeat.getStats()
      expect(stats.sent).toBeGreaterThanOrEqual(3) // Should have sent at least 3 pings
      expect(stats.missed).toBe(0) // No missed pings in normal operation
    })

    it('should track missed heartbeats when connection is closed', async () => {
      const heartbeat = testUtils.createHeartbeatTest(50)

      heartbeat.start()

      // Close connection after first heartbeat
      setTimeout(() => {
        ws.close()
      }, 75)

      // Wait for more heartbeat attempts
      await new Promise(resolve => setTimeout(resolve, 200))

      heartbeat.stop()

      const stats = heartbeat.getStats()
      expect(stats.sent).toBeGreaterThan(0)
      // Note: missed count depends on timing, so we just check it's tracked
    })
  })

  describe('Utility Methods', () => {
    it('should get current WebSocket instance', () => {
      expect(testUtils.getWebSocket()).toBeNull()

      const ws = testUtils.createMockWebSocket()
      expect(testUtils.getWebSocket()).toBe(ws)
    })

    it('should reset test utilities', async () => {
      const ws = testUtils.createMockWebSocket()
      await testUtils.waitForState(MockWebSocket.OPEN)
      
      ws.send('test message')
      
      expect(testUtils.getWebSocket()).not.toBeNull()
      expect(testUtils.getMessageHistory()).toHaveLength(1)

      testUtils.reset()

      expect(testUtils.getWebSocket()).toBeNull()
      expect(testUtils.getMessageHistory()).toHaveLength(0)
    })
  })

  describe('Error Handling', () => {
    it('should handle operations without WebSocket instance', async () => {
      await expect(
        testUtils.waitForState(MockWebSocket.OPEN)
      ).rejects.toThrow('No WebSocket instance available')

      await expect(
        testUtils.waitForMessage('test')
      ).rejects.toThrow('No WebSocket instance available')

      await expect(
        testUtils.sendAndWaitForResponse('test')
      ).rejects.toThrow('No WebSocket instance available')

      expect(() => {
        testUtils.expectState(MockWebSocket.OPEN)
      }).toThrow('No WebSocket instance available')
    })

    it('should handle sending on non-open WebSocket', async () => {
      const ws = testUtils.createMockWebSocket()
      // Don't wait for connection to open

      await expect(
        testUtils.sendAndWaitForResponse('test message')
      ).rejects.toThrow('WebSocket is not open')
    })

    it('should handle message timeout gracefully', async () => {
      const ws = testUtils.createMockWebSocket()
      await testUtils.waitForState(MockWebSocket.OPEN)

      await expect(
        testUtils.waitForMessage('never-coming-message', 100)
      ).rejects.toThrow('Timeout waiting for message')
    })

    it('should handle connection state timeout', async () => {
      const ws = testUtils.createMockWebSocket()
      ws.close() // Close immediately

      await expect(
        testUtils.waitForState(MockWebSocket.OPEN, 100)
      ).rejects.toThrow('Timeout waiting for WebSocket state')
    })
  })

  describe('Complex Scenarios', () => {
    it('should handle reconnection scenario', async () => {
      // First connection
      let ws = testUtils.createMockWebSocket()
      await testUtils.waitForState(MockWebSocket.OPEN)

      // Send a message
      ws.send('initial message')
      testUtils.expectMessageSent('initial message')

      // Simulate disconnect
      testUtils.simulateServerDisconnect(1006, 'Connection lost')
      await testUtils.waitForState(MockWebSocket.CLOSED)

      // Reconnect
      ws = testUtils.createMockWebSocket()
      await testUtils.waitForState(MockWebSocket.OPEN)

      // Send another message
      ws.send('reconnected message')
      testUtils.expectMessageSent('reconnected message')
    })

    it('should handle multiple concurrent message exchanges', async () => {
      const ws = testUtils.createMockWebSocket()
      await testUtils.waitForState(MockWebSocket.OPEN)

      // Set up echo behavior with delay
      ws.onmessage = (event) => {
        setTimeout(() => {
          ws.simulateMessage(`echo: ${event.data}`)
        }, Math.random() * 50) // Random delay up to 50ms
      }

      const messages = ['msg1', 'msg2', 'msg3', 'msg4', 'msg5']
      const promises = messages.map(msg => 
        testUtils.sendAndWaitForResponse(msg, new RegExp(`echo: ${msg}`), 1000)
      )

      const responses = await Promise.all(promises)
      
      expect(responses).toHaveLength(5)
      responses.forEach((response, index) => {
        expect(response?.data).toBe(`echo: ${messages[index]}`)
      })
    })
  })
})
