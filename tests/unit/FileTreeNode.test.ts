import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import FileTreeNode from '../../src/components/editor/FileTreeNode.vue'
import type { FileNode } from '../../src/types/file-tree'

// Mock the ContextMenu component
vi.mock('../../src/components/ui/ContextMenu.vue', () => ({
  default: {
    name: 'ContextMenu',
    template: '<div class="mock-context-menu"><slot /></div>',
    props: ['x', 'y', 'items'],
    emits: ['select', 'close']
  }
}))

// Mock window.prompt and window.confirm
const mockPrompt = vi.fn()
const mockConfirm = vi.fn()
const mockAlert = vi.fn()

Object.defineProperty(window, 'prompt', {
  value: mockPrompt,
  writable: true
})

Object.defineProperty(window, 'confirm', {
  value: mockConfirm,
  writable: true
})

Object.defineProperty(window, 'alert', {
  value: mockAlert,
  writable: true
})

describe('FileTreeNode', () => {
  let pinia: ReturnType<typeof createPinia>

  beforeEach(() => {
    pinia = createPinia()
    setActivePinia(pinia)
    vi.clearAllMocks()
  })

  const createFileNode = (overrides: Partial<FileNode> = {}): FileNode => ({
    id: 'test-file',
    name: 'test.vue',
    type: 'file',
    path: '/test.vue',
    isModified: false,
    size: 100,
    lastModified: new Date(),
    ...overrides
  })

  const createFolderNode = (overrides: Partial<FileNode> = {}): FileNode => ({
    id: 'test-folder',
    name: 'components',
    type: 'folder',
    path: '/components',
    children: [],
    isExpanded: false,
    ...overrides
  })

  const createWrapper = (node: FileNode, props = {}) => {
    return mount(FileTreeNode, {
      props: {
        node,
        level: 0,
        selectedFile: '',
        unsavedChanges: new Set(),
        ...props
      },
      global: {
        plugins: [pinia]
      }
    })
  }

  describe('Rendering', () => {
    it('should render file node correctly', () => {
      const fileNode = createFileNode()
      const wrapper = createWrapper(fileNode)
      
      expect(wrapper.find('.node-name').text()).toBe('test.vue')
      expect(wrapper.find('.node-icon').text()).toBe('🟢') // Vue file icon
      expect(wrapper.find('.expand-button').exists()).toBe(false)
      expect(wrapper.find('.expand-spacer').exists()).toBe(true)
    })

    it('should render folder node correctly', () => {
      const folderNode = createFolderNode()
      const wrapper = createWrapper(folderNode)
      
      expect(wrapper.find('.node-name').text()).toBe('components')
      expect(wrapper.find('.node-icon').text()).toBe('📁') // Closed folder icon
      expect(wrapper.find('.expand-button').exists()).toBe(true)
      expect(wrapper.find('.expand-spacer').exists()).toBe(false)
    })

    it('should show expanded folder icon when folder is expanded', () => {
      const folderNode = createFolderNode({ isExpanded: true })
      const wrapper = createWrapper(folderNode)
      
      expect(wrapper.find('.node-icon').text()).toBe('📂') // Open folder icon
      expect(wrapper.find('.expand-icon').classes()).toContain('expanded')
    })

    it('should show modified indicator when file is modified', () => {
      const fileNode = createFileNode({ isModified: true })
      const wrapper = createWrapper(fileNode)
      
      expect(wrapper.find('.modified-indicator').exists()).toBe(true)
      expect(wrapper.find('.node-content').classes()).toContain('is-modified')
    })

    it('should show selected state when file is selected', () => {
      const fileNode = createFileNode()
      const wrapper = createWrapper(fileNode, { selectedFile: '/test.vue' })
      
      expect(wrapper.find('.node-content').classes()).toContain('is-selected')
    })

    it('should display file size for files', () => {
      const fileNode = createFileNode({ size: 1024 })
      const wrapper = createWrapper(fileNode)
      
      expect(wrapper.find('.file-size').text()).toBe('1 KB')
    })

    it('should render children when folder is expanded', () => {
      const childNode = createFileNode({ id: 'child', name: 'child.vue', path: '/components/child.vue' })
      const folderNode = createFolderNode({ 
        isExpanded: true, 
        children: [childNode] 
      })
      const wrapper = createWrapper(folderNode)
      
      expect(wrapper.find('.children').exists()).toBe(true)
    })
  })

  describe('File Icons', () => {
    const testCases = [
      { name: 'test.vue', expected: '🟢' },
      { name: 'test.js', expected: '🟨' },
      { name: 'test.ts', expected: '🔷' },
      { name: 'test.css', expected: '🎨' },
      { name: 'test.html', expected: '🌐' },
      { name: 'test.json', expected: '📋' },
      { name: 'test.md', expected: '📝' },
      { name: 'test.png', expected: '🖼️' },
      { name: 'test.txt', expected: '📄' }
    ]

    testCases.forEach(({ name, expected }) => {
      it(`should show correct icon for ${name}`, () => {
        const fileNode = createFileNode({ name })
        const wrapper = createWrapper(fileNode)
        
        expect(wrapper.find('.node-icon').text()).toBe(expected)
      })
    })
  })

  describe('File Size Formatting', () => {
    const testCases = [
      { size: 0, expected: '0 B' },
      { size: 512, expected: '512 B' },
      { size: 1024, expected: '1 KB' },
      { size: 1536, expected: '1.5 KB' },
      { size: 1048576, expected: '1 MB' },
      { size: 1073741824, expected: '1 GB' }
    ]

    testCases.forEach(({ size, expected }) => {
      it(`should format ${size} bytes as ${expected}`, () => {
        const fileNode = createFileNode({ size })
        const wrapper = createWrapper(fileNode)
        
        expect(wrapper.find('.file-size').text()).toBe(expected)
      })
    })
  })

  describe('Click Interactions', () => {
    it('should emit file-selected when file node is clicked', async () => {
      const fileNode = createFileNode()
      const wrapper = createWrapper(fileNode)
      
      await wrapper.find('.node-content').trigger('click')
      
      expect(wrapper.emitted('file-selected')).toEqual([['/test.vue']])
    })

    it('should toggle expanded state when folder node is clicked', async () => {
      const folderNode = createFolderNode({ isExpanded: false })
      const wrapper = createWrapper(folderNode)
      
      await wrapper.find('.node-content').trigger('click')
      
      expect(folderNode.isExpanded).toBe(true)
    })

    it('should toggle expanded state when expand button is clicked', async () => {
      const folderNode = createFolderNode({ isExpanded: false })
      const wrapper = createWrapper(folderNode)
      
      await wrapper.find('.expand-button').trigger('click')
      
      expect(folderNode.isExpanded).toBe(true)
    })
  })

  describe('Context Menu', () => {
    it('should show context menu on right click', async () => {
      const fileNode = createFileNode()
      const wrapper = createWrapper(fileNode)
      
      await wrapper.find('.node-content').trigger('contextmenu')
      
      expect(wrapper.find('.mock-context-menu').exists()).toBe(true)
    })

    it('should show correct menu items for file', () => {
      const fileNode = createFileNode()
      const wrapper = createWrapper(fileNode)
      
      const menuItems = wrapper.vm.contextMenuItems
      const itemIds = menuItems.map(item => item.id)
      
      expect(itemIds).toEqual(['rename', 'separator', 'delete'])
    })

    it('should show correct menu items for folder', () => {
      const folderNode = createFolderNode()
      const wrapper = createWrapper(folderNode)
      
      const menuItems = wrapper.vm.contextMenuItems
      const itemIds = menuItems.map(item => item.id)
      
      expect(itemIds).toEqual(['rename', 'create-file', 'create-folder', 'separator', 'delete'])
    })

    it('should hide context menu when hideContextMenu is called', async () => {
      const fileNode = createFileNode()
      const wrapper = createWrapper(fileNode)
      
      // Show menu first
      await wrapper.find('.node-content').trigger('contextmenu')
      expect(wrapper.find('.mock-context-menu').exists()).toBe(true)
      
      // Hide menu
      wrapper.vm.hideContextMenu()
      await wrapper.vm.$nextTick()
      
      expect(wrapper.find('.mock-context-menu').exists()).toBe(false)
    })
  })

  describe('File Operations', () => {
    describe('Rename Operation', () => {
      it('should emit file-renamed when rename is confirmed with valid name', () => {
        const fileNode = createFileNode({ name: 'old.vue', path: '/old.vue' })
        const wrapper = createWrapper(fileNode)
        
        mockPrompt.mockReturnValue('new.vue')
        
        wrapper.vm.handleRename()
        
        expect(wrapper.emitted('file-renamed')).toEqual([['/old.vue', '/new.vue']])
      })

      it('should not emit file-renamed when rename is cancelled', () => {
        const fileNode = createFileNode()
        const wrapper = createWrapper(fileNode)
        
        mockPrompt.mockReturnValue(null)
        
        wrapper.vm.handleRename()
        
        expect(wrapper.emitted('file-renamed')).toBeUndefined()
      })

      it('should not emit file-renamed when new name is same as old name', () => {
        const fileNode = createFileNode({ name: 'test.vue' })
        const wrapper = createWrapper(fileNode)
        
        mockPrompt.mockReturnValue('test.vue')
        
        wrapper.vm.handleRename()
        
        expect(wrapper.emitted('file-renamed')).toBeUndefined()
      })

      it('should show alert for invalid characters in filename', () => {
        const fileNode = createFileNode()
        const wrapper = createWrapper(fileNode)
        
        mockPrompt.mockReturnValue('test<>.vue')
        
        wrapper.vm.handleRename()
        
        expect(mockAlert).toHaveBeenCalledWith('檔案名稱包含無效字元')
        expect(wrapper.emitted('file-renamed')).toBeUndefined()
      })

      it('should handle nested folder paths correctly', () => {
        const fileNode = createFileNode({ 
          name: 'old.vue', 
          path: '/components/nested/old.vue' 
        })
        const wrapper = createWrapper(fileNode)
        
        mockPrompt.mockReturnValue('new.vue')
        
        wrapper.vm.handleRename()
        
        expect(wrapper.emitted('file-renamed')).toEqual([[
          '/components/nested/old.vue', 
          '/components/nested/new.vue'
        ]])
      })
    })

    describe('Delete Operation', () => {
      it('should emit file-deleted when delete is confirmed for file', () => {
        const fileNode = createFileNode()
        const wrapper = createWrapper(fileNode)
        
        mockConfirm.mockReturnValue(true)
        
        wrapper.vm.handleDelete()
        
        expect(mockConfirm).toHaveBeenCalledWith('確定要刪除檔案 "test.vue" 嗎？')
        expect(wrapper.emitted('file-deleted')).toEqual([['/test.vue']])
      })

      it('should emit file-deleted when delete is confirmed for folder', () => {
        const folderNode = createFolderNode()
        const wrapper = createWrapper(folderNode)
        
        mockConfirm.mockReturnValue(true)
        
        wrapper.vm.handleDelete()
        
        expect(mockConfirm).toHaveBeenCalledWith('確定要刪除資料夾 "components" 嗎？')
        expect(wrapper.emitted('file-deleted')).toEqual([['/components']])
      })

      it('should not emit file-deleted when delete is cancelled', () => {
        const fileNode = createFileNode()
        const wrapper = createWrapper(fileNode)
        
        mockConfirm.mockReturnValue(false)
        
        wrapper.vm.handleDelete()
        
        expect(wrapper.emitted('file-deleted')).toBeUndefined()
      })
    })

    describe('Create File Operation', () => {
      it('should emit file-created when create file is called on folder', () => {
        const folderNode = createFolderNode()
        const wrapper = createWrapper(folderNode)
        
        wrapper.vm.handleCreateFile()
        
        expect(wrapper.emitted('file-created')).toEqual([['/components', '']])
      })

      it('should not emit file-created when called on file node', () => {
        const fileNode = createFileNode()
        const wrapper = createWrapper(fileNode)
        
        wrapper.vm.handleCreateFile()
        
        expect(wrapper.emitted('file-created')).toBeUndefined()
      })
    })

    describe('Create Folder Operation', () => {
      it('should emit folder-created when create folder is confirmed', () => {
        const folderNode = createFolderNode()
        const wrapper = createWrapper(folderNode)
        
        mockPrompt.mockReturnValue('new-folder')
        
        wrapper.vm.handleCreateFolder()
        
        expect(wrapper.emitted('folder-created')).toEqual([['/components', 'new-folder']])
      })

      it('should not emit folder-created when create folder is cancelled', () => {
        const folderNode = createFolderNode()
        const wrapper = createWrapper(folderNode)
        
        mockPrompt.mockReturnValue(null)
        
        wrapper.vm.handleCreateFolder()
        
        expect(wrapper.emitted('folder-created')).toBeUndefined()
      })

      it('should not emit folder-created when called on file node', () => {
        const fileNode = createFileNode()
        const wrapper = createWrapper(fileNode)
        
        mockPrompt.mockReturnValue('new-folder')
        
        wrapper.vm.handleCreateFolder()
        
        expect(wrapper.emitted('folder-created')).toBeUndefined()
      })
    })
  })

  describe('Menu Selection Handling', () => {
    it('should emit file-renamed when rename menu item is selected', () => {
      const fileNode = createFileNode({ name: 'old.vue', path: '/old.vue' })
      const wrapper = createWrapper(fileNode)
      
      mockPrompt.mockReturnValue('new.vue')
      
      wrapper.vm.handleMenuSelect('rename')
      
      expect(wrapper.emitted('file-renamed')).toEqual([['/old.vue', '/new.vue']])
    })

    it('should emit file-created when create-file menu item is selected', () => {
      const folderNode = createFolderNode()
      const wrapper = createWrapper(folderNode)
      
      wrapper.vm.handleMenuSelect('create-file')
      
      expect(wrapper.emitted('file-created')).toEqual([['/components', '']])
    })

    it('should emit folder-created when create-folder menu item is selected', () => {
      const folderNode = createFolderNode()
      const wrapper = createWrapper(folderNode)
      
      mockPrompt.mockReturnValue('new-folder')
      
      wrapper.vm.handleMenuSelect('create-folder')
      
      expect(wrapper.emitted('folder-created')).toEqual([['/components', 'new-folder']])
    })

    it('should emit file-deleted when delete menu item is selected', () => {
      const fileNode = createFileNode()
      const wrapper = createWrapper(fileNode)
      
      mockConfirm.mockReturnValue(true)
      
      wrapper.vm.handleMenuSelect('delete')
      
      expect(wrapper.emitted('file-deleted')).toEqual([['/test.vue']])
    })

    it('should hide context menu after menu selection', async () => {
      const fileNode = createFileNode()
      const wrapper = createWrapper(fileNode)
      
      // Show menu first
      await wrapper.find('.node-content').trigger('contextmenu')
      expect(wrapper.find('.mock-context-menu').exists()).toBe(true)
      
      mockPrompt.mockReturnValue('new.vue')
      wrapper.vm.handleMenuSelect('rename')
      
      await wrapper.vm.$nextTick()
      expect(wrapper.find('.mock-context-menu').exists()).toBe(false)
    })
  })

  describe('Action Buttons', () => {
    it('should show action buttons on hover for folders', () => {
      const folderNode = createFolderNode()
      const wrapper = createWrapper(folderNode)
      
      expect(wrapper.find('.node-actions').exists()).toBe(true)
      expect(wrapper.findAll('.action-btn')).toHaveLength(4) // create file, create folder, rename, delete
    })

    it('should show action buttons on hover for files', () => {
      const fileNode = createFileNode()
      const wrapper = createWrapper(fileNode)
      
      expect(wrapper.find('.node-actions').exists()).toBe(true)
      expect(wrapper.findAll('.action-btn')).toHaveLength(2) // rename, delete
    })

    it('should emit file-created when create file action button is clicked', async () => {
      const folderNode = createFolderNode()
      const wrapper = createWrapper(folderNode)
      
      const createFileBtn = wrapper.find('.action-btn[title="新增檔案"]')
      await createFileBtn.trigger('click')
      
      expect(wrapper.emitted('file-created')).toEqual([['/components', '']])
    })

    it('should emit folder-created when create folder action button is clicked', async () => {
      const folderNode = createFolderNode()
      const wrapper = createWrapper(folderNode)
      
      mockPrompt.mockReturnValue('new-folder')
      
      const createFolderBtn = wrapper.find('.action-btn[title="新增資料夾"]')
      await createFolderBtn.trigger('click')
      
      expect(wrapper.emitted('folder-created')).toEqual([['/components', 'new-folder']])
    })
  })

  describe('Drag and Drop', () => {
    it('should have draggable attribute', () => {
      const fileNode = createFileNode()
      const wrapper = createWrapper(fileNode)
      
      expect(wrapper.find('.node-content').attributes('draggable')).toBe('true')
    })

    it('should set drag data on dragstart', () => {
      const fileNode = createFileNode()
      const wrapper = createWrapper(fileNode)
      
      const mockDataTransfer = {
        setData: vi.fn(),
        effectAllowed: ''
      }
      
      const dragEvent = {
        dataTransfer: mockDataTransfer
      } as any
      
      wrapper.vm.handleDragStart(dragEvent)
      
      expect(mockDataTransfer.setData).toHaveBeenCalledWith(
        'text/plain',
        JSON.stringify({
          type: 'file-tree-node',
          path: '/test.vue',
          name: 'test.vue',
          nodeType: 'file'
        })
      )
      expect(mockDataTransfer.effectAllowed).toBe('move')
    })

    it('should show drag over state when dragging over folder', () => {
      const folderNode = createFolderNode()
      const wrapper = createWrapper(folderNode)
      
      const mockDataTransfer = {
        getData: vi.fn().mockReturnValue(JSON.stringify({
          type: 'file-tree-node',
          path: '/other.vue',
          name: 'other.vue',
          nodeType: 'file'
        })),
        dropEffect: ''
      }
      
      const dragEvent = {
        dataTransfer: mockDataTransfer
      } as any
      
      wrapper.vm.handleDragOver(dragEvent)
      
      expect(wrapper.vm.isDragOver).toBe(true)
      expect(mockDataTransfer.dropEffect).toBe('move')
    })

    it('should clear drag over state on drag leave', () => {
      const folderNode = createFolderNode()
      const wrapper = createWrapper(folderNode)
      
      wrapper.vm.isDragOver = true
      wrapper.vm.handleDragLeave()
      
      expect(wrapper.vm.isDragOver).toBe(false)
    })

    it('should emit file-renamed on drop', () => {
      const folderNode = createFolderNode()
      const wrapper = createWrapper(folderNode)
      
      const mockDataTransfer = {
        getData: vi.fn().mockReturnValue(JSON.stringify({
          type: 'file-tree-node',
          path: '/old.vue',
          name: 'old.vue',
          nodeType: 'file'
        }))
      }
      
      const dropEvent = {
        dataTransfer: mockDataTransfer
      } as any
      
      wrapper.vm.handleDrop(dropEvent)
      
      expect(wrapper.emitted('file-renamed')).toEqual([['/old.vue', '/components/old.vue']])
      expect(wrapper.vm.isDragOver).toBe(false)
    })
  })

  describe('Responsive Design', () => {
    it('should have correct CSS classes for responsive design', () => {
      const fileNode = createFileNode()
      const wrapper = createWrapper(fileNode)
      
      expect(wrapper.find('.file-tree-node').exists()).toBe(true)
      expect(wrapper.find('.node-content').exists()).toBe(true)
    })

    it('should apply correct indentation based on level', () => {
      const fileNode = createFileNode()
      const wrapper = createWrapper(fileNode, { level: 2 })
      
      const nodeContent = wrapper.find('.node-content')
      const style = nodeContent.attributes('style')
      
      expect(style).toContain('padding-left: 40px') // 2 * 16 + 8 = 40px
    })
  })
})