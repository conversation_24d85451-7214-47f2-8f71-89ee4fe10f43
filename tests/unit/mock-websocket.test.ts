import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { MockWebSocket, mockWebSocket, restoreWebSocket } from '../utils/mock-websocket'

describe('MockWebSocket', () => {
  let ws: MockWebSocket

  beforeEach(() => {
    mockWebSocket()
  })

  afterEach(() => {
    if (ws) {
      ws.close()
    }
    restoreWebSocket()
  })

  describe('Constructor and Initial State', () => {
    it('should create WebSocket with correct initial state', () => {
      ws = new MockWebSocket('ws://localhost:8080/test')
      
      expect(ws.url).toBe('ws://localhost:8080/test')
      expect(ws.readyState).toBe(MockWebSocket.CONNECTING)
      expect(ws.protocol).toBe('')
      expect(ws.extensions).toBe('')
      expect(ws.bufferedAmount).toBe(0)
      expect(ws.binaryType).toBe('blob')
    })

    it('should handle protocol parameter', () => {
      ws = new MockWebSocket('ws://localhost:8080/test', 'chat')
      expect(ws.protocol).toBe('chat')
    })

    it('should handle protocol array parameter', () => {
      ws = new MockWebSocket('ws://localhost:8080/test', ['chat', 'echo'])
      expect(ws.protocol).toBe('chat')
    })

    it('should have correct WebSocket constants', () => {
      expect(MockWebSocket.CONNECTING).toBe(0)
      expect(MockWebSocket.OPEN).toBe(1)
      expect(MockWebSocket.CLOSING).toBe(2)
      expect(MockWebSocket.CLOSED).toBe(3)
    })
  })

  describe('Connection Lifecycle', () => {
    it('should transition to OPEN state after connection', async () => {
      ws = new MockWebSocket('ws://localhost:8080/test')
      
      await new Promise(resolve => {
        ws.onopen = () => {
          expect(ws.readyState).toBe(MockWebSocket.OPEN)
          resolve(undefined)
        }
      })
    })

    it('should call onopen handler when connection opens', async () => {
      const onOpenSpy = vi.fn()
      ws = new MockWebSocket('ws://localhost:8080/test')
      ws.onopen = onOpenSpy

      await new Promise(resolve => {
        ws.addEventListener('open', () => {
          expect(onOpenSpy).toHaveBeenCalledOnce()
          resolve(undefined)
        })
      })
    })

    it('should handle connection with delay', async () => {
      ws = new MockWebSocket('ws://localhost:8080/test')
      ws.setConnectionDelay(100)

      const startTime = Date.now()
      
      await new Promise(resolve => {
        ws.onopen = () => {
          const elapsed = Date.now() - startTime
          expect(elapsed).toBeGreaterThanOrEqual(90) // Allow some tolerance
          resolve(undefined)
        }
      })
    })

    it('should handle connection failure', async () => {
      ws = new MockWebSocket('ws://localhost:8080/test')
      ws.setConnectionFailure(true)

      await new Promise(resolve => {
        ws.onerror = () => {
          expect(ws.readyState).toBe(MockWebSocket.CLOSED)
          resolve(undefined)
        }
      })
    })
  })

  describe('Message Handling', () => {
    beforeEach(async () => {
      ws = new MockWebSocket('ws://localhost:8080/test')
      await new Promise(resolve => {
        ws.onopen = () => resolve(undefined)
      })
    })

    it('should send string messages', () => {
      const message = 'Hello, WebSocket!'
      
      expect(() => {
        ws.send(message)
      }).not.toThrow()

      const sentMessages = ws.getSentMessages()
      expect(sentMessages).toContain(message)
    })

    it('should throw error when sending on closed connection', () => {
      ws.close()
      
      expect(() => {
        ws.send('test message')
      }).toThrow('WebSocket is not open')
    })

    it('should receive simulated messages', async () => {
      const testMessage = 'Test message from server'
      
      const messagePromise = new Promise(resolve => {
        ws.onmessage = (event) => {
          expect(event.data).toBe(testMessage)
          resolve(undefined)
        }
      })

      ws.simulateMessage(testMessage)
      await messagePromise
    })

    it('should handle binary messages', () => {
      const buffer = new ArrayBuffer(8)
      const view = new Uint8Array(buffer)
      view[0] = 72 // 'H'
      view[1] = 101 // 'e'

      expect(() => {
        ws.send(buffer)
      }).not.toThrow()

      const sentMessages = ws.getSentMessages()
      expect(sentMessages).toContain(buffer)
    })

    it('should handle blob messages', () => {
      const blob = new Blob(['Hello'], { type: 'text/plain' })

      expect(() => {
        ws.send(blob)
      }).not.toThrow()

      const sentMessages = ws.getSentMessages()
      expect(sentMessages).toContain(blob)
    })
  })

  describe('Event Handling', () => {
    beforeEach(() => {
      ws = new MockWebSocket('ws://localhost:8080/test')
    })

    it('should add and remove event listeners', async () => {
      const openListener = vi.fn()
      
      ws.addEventListener('open', openListener)
      
      await new Promise(resolve => {
        ws.onopen = () => {
          expect(openListener).toHaveBeenCalledOnce()
          resolve(undefined)
        }
      })

      ws.removeEventListener('open', openListener)
      
      // Reset and test that listener was removed
      ws.reset()
      ws = new MockWebSocket('ws://localhost:8080/test')
      ws.addEventListener('open', openListener)
      
      await new Promise(resolve => {
        setTimeout(() => {
          // openListener should only have been called once from the first test
          expect(openListener).toHaveBeenCalledOnce()
          resolve(undefined)
        }, 50)
      })
    })

    it('should handle multiple event listeners', async () => {
      const listener1 = vi.fn()
      const listener2 = vi.fn()
      
      ws.addEventListener('open', listener1)
      ws.addEventListener('open', listener2)

      await new Promise(resolve => {
        ws.onopen = () => {
          expect(listener1).toHaveBeenCalledOnce()
          expect(listener2).toHaveBeenCalledOnce()
          resolve(undefined)
        }
      })
    })

    it('should handle message events', async () => {
      await new Promise(resolve => {
        ws.onopen = () => resolve(undefined)
      })

      const messageListener = vi.fn()
      ws.addEventListener('message', messageListener)

      ws.simulateMessage('test message')

      await new Promise(resolve => {
        setTimeout(() => {
          expect(messageListener).toHaveBeenCalledOnce()
          resolve(undefined)
        }, 50)
      })
    })

    it('should handle error events', () => {
      const errorListener = vi.fn()
      ws.addEventListener('error', errorListener)
      ws.onerror = vi.fn()

      ws.simulateError('Test error')

      expect(errorListener).toHaveBeenCalledOnce()
      expect(ws.onerror).toHaveBeenCalledOnce()
    })
  })

  describe('Connection Closing', () => {
    beforeEach(async () => {
      ws = new MockWebSocket('ws://localhost:8080/test')
      await new Promise(resolve => {
        ws.onopen = () => resolve(undefined)
      })
    })

    it('should close connection gracefully', async () => {
      const closePromise = new Promise(resolve => {
        ws.onclose = (event) => {
          expect(event.code).toBe(1000)
          expect(event.reason).toBe('')
          expect(event.wasClean).toBe(true)
          expect(ws.readyState).toBe(MockWebSocket.CLOSED)
          resolve(undefined)
        }
      })

      ws.close()
      await closePromise
    })

    it('should close with custom code and reason', async () => {
      const closePromise = new Promise(resolve => {
        ws.onclose = (event) => {
          expect(event.code).toBe(1001)
          expect(event.reason).toBe('Going away')
          expect(event.wasClean).toBe(false)
          resolve(undefined)
        }
      })

      ws.close(1001, 'Going away')
      await closePromise
    })

    it('should handle server-initiated close', async () => {
      const closePromise = new Promise(resolve => {
        ws.onclose = (event) => {
          expect(event.code).toBe(1006)
          expect(event.reason).toBe('Server shutdown')
          expect(event.wasClean).toBe(false)
          resolve(undefined)
        }
      })

      ws.simulateServerClose(1006, 'Server shutdown')
      await closePromise
    })

    it('should not close already closed connection', async () => {
      ws.close()
      
      await new Promise(resolve => {
        ws.onclose = () => resolve(undefined)
      })

      const closeListener = vi.fn()
      ws.addEventListener('close', closeListener)

      // Try to close again
      ws.close()

      // Should not trigger another close event
      await new Promise(resolve => {
        setTimeout(() => {
          expect(closeListener).not.toHaveBeenCalled()
          resolve(undefined)
        }, 50)
      })
    })
  })

  describe('Network Simulation', () => {
    beforeEach(async () => {
      ws = new MockWebSocket('ws://localhost:8080/test')
      await new Promise(resolve => {
        ws.onopen = () => resolve(undefined)
      })
    })

    it('should simulate network delay for messages', async () => {
      ws.setNetworkDelay(100)
      
      const startTime = Date.now()
      
      const messagePromise = new Promise(resolve => {
        ws.onmessage = () => {
          const elapsed = Date.now() - startTime
          expect(elapsed).toBeGreaterThanOrEqual(90) // Allow some tolerance
          resolve(undefined)
        }
      })

      ws.simulateMessage('delayed message')
      await messagePromise
    })

    it('should simulate connection errors', () => {
      const errorListener = vi.fn()
      ws.addEventListener('error', errorListener)

      ws.simulateError('Network error')

      expect(errorListener).toHaveBeenCalledOnce()
      const errorEvent = errorListener.mock.calls[0][0]
      expect(errorEvent.message).toBe('Network error')
    })
  })

  describe('Test Utilities', () => {
    beforeEach(async () => {
      ws = new MockWebSocket('ws://localhost:8080/test')
      await new Promise(resolve => {
        ws.onopen = () => resolve(undefined)
      })
    })

    it('should track sent messages', () => {
      ws.send('message 1')
      ws.send('message 2')
      ws.send('message 3')

      const sentMessages = ws.getSentMessages()
      expect(sentMessages).toHaveLength(3)
      expect(sentMessages).toEqual(['message 1', 'message 2', 'message 3'])
    })

    it('should clear message queue', () => {
      ws.send('message 1')
      ws.send('message 2')

      expect(ws.getSentMessages()).toHaveLength(2)

      ws.clearMessageQueue()
      expect(ws.getSentMessages()).toHaveLength(0)
    })

    it('should reset WebSocket state', () => {
      ws.send('test message')
      ws.addEventListener('message', vi.fn())

      expect(ws.getSentMessages()).toHaveLength(1)
      expect(ws.readyState).toBe(MockWebSocket.OPEN)

      ws.reset()

      expect(ws.getSentMessages()).toHaveLength(0)
      expect(ws.readyState).toBe(MockWebSocket.CONNECTING)
      expect(ws.onopen).toBeNull()
      expect(ws.onclose).toBeNull()
      expect(ws.onmessage).toBeNull()
      expect(ws.onerror).toBeNull()
    })
  })

  describe('Edge Cases', () => {
    it('should handle rapid open/close cycles', async () => {
      ws = new MockWebSocket('ws://localhost:8080/test')
      
      // Close immediately after creation
      ws.close()

      await new Promise(resolve => {
        setTimeout(() => {
          expect(ws.readyState).toBe(MockWebSocket.CLOSED)
          resolve(undefined)
        }, 50)
      })
    })

    it('should handle sending messages during closing state', async () => {
      ws = new MockWebSocket('ws://localhost:8080/test')
      
      await new Promise(resolve => {
        ws.onopen = () => resolve(undefined)
      })

      ws.close()
      
      // Try to send message while closing
      expect(() => {
        ws.send('message during close')
      }).toThrow('WebSocket is not open')
    })

    it('should handle event listener errors gracefully', async () => {
      ws = new MockWebSocket('ws://localhost:8080/test')
      
      const faultyListener = () => {
        throw new Error('Listener error')
      }

      ws.addEventListener('open', faultyListener)

      // Should not throw despite faulty listener
      await new Promise(resolve => {
        ws.onopen = () => {
          // Connection should still work despite listener error
          expect(ws.readyState).toBe(MockWebSocket.OPEN)
          resolve(undefined)
        }
      })
    })

    it('should handle multiple simultaneous messages', async () => {
      ws = new MockWebSocket('ws://localhost:8080/test')
      
      await new Promise(resolve => {
        ws.onopen = () => resolve(undefined)
      })

      const messages = ['msg1', 'msg2', 'msg3', 'msg4', 'msg5']
      const receivedMessages: string[] = []

      ws.onmessage = (event) => {
        receivedMessages.push(event.data)
      }

      // Send multiple messages rapidly
      messages.forEach(msg => ws.simulateMessage(msg))

      await new Promise(resolve => {
        setTimeout(() => {
          expect(receivedMessages).toHaveLength(5)
          expect(receivedMessages).toEqual(messages)
          resolve(undefined)
        }, 100)
      })
    })
  })

  describe('Global Mock Functions', () => {
    it('should mock global WebSocket', () => {
      mockWebSocket()
      
      expect(global.WebSocket).toBe(MockWebSocket)
      expect(window.WebSocket).toBe(MockWebSocket)
    })

    it('should restore WebSocket', () => {
      mockWebSocket()
      restoreWebSocket()
      
      expect(global.WebSocket).toBeUndefined()
      expect(window.WebSocket).toBeUndefined()
    })
  })
})