// /tests/unit/LoadingOverlay.test.ts
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import { setActivePinia, createPinia } from 'pinia';
import LoadingOverlay from '@/components/ui/LoadingOverlay.vue';
import { useUIStore } from '@/stores/ui';

// Mock Heroicons
vi.mock('@heroicons/vue/24/outline', () => ({
  XMarkIcon: { name: 'XMarkIcon', template: '<div>XMarkIcon</div>' },
}));

describe('LoadingOverlay', () => {
  let uiStore: ReturnType<typeof useUIStore>;

  beforeEach(() => {
    setActivePinia(createPinia());
    uiStore = useUIStore();
  });

  it('應該在沒有載入狀態時不顯示任何內容', () => {
    const wrapper = mount(LoadingOverlay);
    
    expect(wrapper.find('[role="dialog"]').exists()).toBe(false);
  });

  it('應該顯示載入覆蓋層', async () => {
    const wrapper = mount(LoadingOverlay, {
      attachTo: document.body
    });
    
    uiStore.startLoading('正在載入...');
    await wrapper.vm.$nextTick();
    
    // 由於使用 Teleport，需要在 document.body 中查找
    const overlay = document.querySelector('[role="dialog"]');
    expect(overlay).toBeTruthy();
    expect(overlay?.getAttribute('aria-modal')).toBe('true');
    expect(document.body.textContent).toContain('正在載入...');
  });

  it('應該顯示載入訊息', async () => {
    const wrapper = mount(LoadingOverlay, {
      attachTo: document.body
    });
    
    uiStore.startLoading('正在儲存檔案...');
    await wrapper.vm.$nextTick();
    
    const title = document.querySelector('#loading-title');
    expect(title).toBeTruthy();
    expect(title?.textContent).toBe('正在儲存檔案...');
  });

  it('應該顯示進度條', async () => {
    const wrapper = mount(LoadingOverlay, {
      attachTo: document.body
    });
    
    uiStore.startLoading('正在上傳...', { progress: 75 });
    await wrapper.vm.$nextTick();
    
    expect(document.body.textContent).toContain('進度');
    expect(document.body.textContent).toContain('75%');
    
    const progressBar = document.querySelector('.bg-indigo-600');
    expect(progressBar).toBeTruthy();
    expect(progressBar?.getAttribute('style')).toContain('width: 75%');
  });

  it('應該顯示取消按鈕', async () => {
    const wrapper = mount(LoadingOverlay, {
      attachTo: document.body
    });
    const onCancel = vi.fn();
    
    uiStore.startLoading('正在處理...', {
      canCancel: true,
      onCancel,
    });
    await wrapper.vm.$nextTick();
    
    const cancelButton = document.querySelector('button');
    expect(cancelButton).toBeTruthy();
    expect(cancelButton?.textContent).toContain('取消');
    
    cancelButton?.click();
    expect(onCancel).toHaveBeenCalled();
  });

  it('應該不顯示取消按鈕當不可取消時', async () => {
    const wrapper = mount(LoadingOverlay, {
      attachTo: document.body
    });
    
    uiStore.startLoading('正在處理...', { canCancel: false });
    await wrapper.vm.$nextTick();
    
    const cancelButton = document.querySelector('button');
    expect(cancelButton).toBeFalsy();
  });

  it('應該顯示多個載入狀態指示器', async () => {
    const wrapper = mount(LoadingOverlay, {
      attachTo: document.body
    });
    
    uiStore.startLoading('載入 1');
    uiStore.startLoading('載入 2');
    uiStore.startLoading('載入 3');
    await wrapper.vm.$nextTick();
    
    expect(document.body.textContent).toContain('3 個任務進行中');
    
    const indicators = document.querySelectorAll('.w-2.h-2.rounded-full');
    expect(indicators).toHaveLength(3);
    
    // 最後一個應該是活躍狀態（藍色）
    expect(indicators[2].classList.contains('bg-indigo-600')).toBe(true);
    // 其他應該是非活躍狀態（灰色）
    expect(indicators[0].classList.contains('bg-gray-300')).toBe(true);
    expect(indicators[1].classList.contains('bg-gray-300')).toBe(true);
  });

  it('應該不顯示多個載入狀態指示器當只有一個載入狀態時', async () => {
    const wrapper = mount(LoadingOverlay, {
      attachTo: document.body
    });
    
    uiStore.startLoading('載入中...');
    await wrapper.vm.$nextTick();
    
    expect(document.body.textContent).not.toContain('個任務進行中');
    expect(document.querySelectorAll('.w-2.h-2.rounded-full')).toHaveLength(0);
  });

  it('應該顯示主要載入狀態的訊息', async () => {
    const wrapper = mount(LoadingOverlay, {
      attachTo: document.body
    });
    
    uiStore.startLoading('載入 1');
    uiStore.startLoading('載入 2');
    uiStore.startLoading('載入 3');
    await wrapper.vm.$nextTick();
    
    // 應該顯示最新（主要）載入狀態的訊息
    const title = document.querySelector('#loading-title');
    expect(title?.textContent).toBe('載入 3');
  });

  it('應該在載入狀態結束時隱藏覆蓋層', async () => {
    const wrapper = mount(LoadingOverlay, {
      attachTo: document.body
    });
    
    const loadingId = uiStore.startLoading('載入中...');
    await wrapper.vm.$nextTick();
    
    expect(document.querySelector('[role="dialog"]')).toBeTruthy();
    
    uiStore.stopLoading(loadingId);
    await wrapper.vm.$nextTick();
    
    expect(document.querySelector('[role="dialog"]')).toBeFalsy();
  });

  it('應該正確設置 aria 屬性', async () => {
    const wrapper = mount(LoadingOverlay, {
      attachTo: document.body
    });
    
    uiStore.startLoading('正在載入...');
    await wrapper.vm.$nextTick();
    
    const overlay = document.querySelector('[role="dialog"]');
    expect(overlay?.getAttribute('aria-modal')).toBe('true');
    expect(overlay?.getAttribute('aria-labelledby')).toBe('loading-title');
    expect(overlay?.getAttribute('aria-describedby')).toBe('loading-description');
    
    const title = document.querySelector('#loading-title');
    expect(title).toBeTruthy();
    
    const description = document.querySelector('#loading-description');
    expect(description).toBeTruthy();
  });

  it('應該顯示載入動畫', async () => {
    const wrapper = mount(LoadingOverlay, {
      attachTo: document.body
    });
    
    uiStore.startLoading('載入中...');
    await wrapper.vm.$nextTick();
    
    const spinner = document.querySelector('.animate-spin');
    expect(spinner).toBeTruthy();
    expect(spinner?.classList.contains('border-indigo-600')).toBe(true);
    expect(spinner?.classList.contains('border-t-transparent')).toBe(true);
  });

  it('應該更新載入狀態', async () => {
    const wrapper = mount(LoadingOverlay, {
      attachTo: document.body
    });
    
    const loadingId = uiStore.startLoading('開始載入...');
    await wrapper.vm.$nextTick();
    
    const title = document.querySelector('#loading-title');
    expect(title?.textContent).toBe('開始載入...');
    
    uiStore.updateLoading(loadingId, {
      message: '正在處理...',
      progress: 50,
    });
    await wrapper.vm.$nextTick();
    
    expect(title?.textContent).toBe('正在處理...');
    expect(document.body.textContent).toContain('50%');
  });
});