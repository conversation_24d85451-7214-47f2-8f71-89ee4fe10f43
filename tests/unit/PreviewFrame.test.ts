import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { mount, VueWrapper } from '@vue/test-utils'
import { nextTick } from 'vue'
import PreviewFrame from '../../src/components/preview/PreviewFrame.vue'
import * as vueCompiler from '../../src/lib/vue-compiler'

// Mock the vue-compiler module
vi.mock('../../src/lib/vue-compiler', () => ({
  compileVueComponent: vi.fn(),
  validateVueComponent: vi.fn(),
  extractComponentDependencies: vi.fn(),
  generatePreviewCode: vi.fn(),
  isValidVueSFC: vi.fn(),
  formatVueSFC: vi.fn()
}))

describe('PreviewFrame', () => {
  let wrapper: VueWrapper<any>
  let mockCompileVueComponent: any

  beforeEach(() => {
    mockCompileVueComponent = vi.mocked(vueCompiler.compileVueComponent)
    
    // Reset all mocks
    vi.clearAllMocks()
    
    // Setup default mock implementations
    mockCompileVueComponent.mockResolvedValue({
      script: 'const PreviewComponent = { name: "TestComponent" };',
      template: 'function render() { return h("div", "Hello World"); }',
      styles: ['.test { color: red; }'],
      errors: [],
      warnings: []
    })
  })

  afterEach(() => {
    if (wrapper) {
      wrapper.unmount()
    }
  })

  describe('Component Rendering', () => {
    it('should render preview frame container', () => {
      wrapper = mount(PreviewFrame, {
        props: {
          componentCode: '<template><div>Test</div></template>'
        }
      })

      expect(wrapper.find('.preview-frame-container').exists()).toBe(true)
      expect(wrapper.find('.preview-header').exists()).toBe(true)
      expect(wrapper.find('.preview-title').text()).toBe('組件預覽')
    })

    it('should show loading state during compilation', async () => {
      // Mock a delayed compilation
      let resolveCompilation: any
      mockCompileVueComponent.mockImplementation(() => 
        new Promise(resolve => {
          resolveCompilation = resolve
        })
      )

      wrapper = mount(PreviewFrame, {
        props: {
          componentCode: '<template><div>Test</div></template>'
        }
      })

      await nextTick()
      // Wait a bit for the compilation to start
      await new Promise(resolve => setTimeout(resolve, 10))

      expect(wrapper.find('.loading-overlay').exists()).toBe(true)
      expect(wrapper.find('.loading-text').text()).toBe('正在編譯組件...')
      expect(wrapper.find('.loading-spinner').exists()).toBe(true)

      // Resolve the compilation
      if (resolveCompilation) {
        resolveCompilation({
          script: 'const PreviewComponent = {};',
          template: 'function render() { return h("div"); }',
          styles: [],
          errors: [],
          warnings: []
        })
      }
    })

    it('should show empty state when no component code provided', async () => {
      wrapper = mount(PreviewFrame, {
        props: {
          componentCode: ''
        }
      })

      await nextTick()
      await new Promise(resolve => setTimeout(resolve, 50))

      expect(wrapper.find('.preview-iframe').exists()).toBe(true)
      // The iframe should contain empty state HTML
      const iframe = wrapper.find('.preview-iframe').element as HTMLIFrameElement
      expect(iframe.srcdoc).toContain('等待組件程式碼...')
    })
  })

  describe('Compilation Process', () => {
    it('should compile component code on mount', async () => {
      const componentCode = '<template><div>Hello World</div></template>'
      
      wrapper = mount(PreviewFrame, {
        props: {
          componentCode
        }
      })

      await nextTick()
      await new Promise(resolve => setTimeout(resolve, 50))

      expect(mockCompileVueComponent).toHaveBeenCalledWith(
        componentCode,
        'PreviewComponent.vue'
      )
    })

    it('should recompile when component code changes', async () => {
      wrapper = mount(PreviewFrame, {
        props: {
          componentCode: '<template><div>Initial</div></template>'
        }
      })

      await nextTick()
      await new Promise(resolve => setTimeout(resolve, 50))

      expect(mockCompileVueComponent).toHaveBeenCalledTimes(1)

      // Change the component code
      await wrapper.setProps({
        componentCode: '<template><div>Updated</div></template>'
      })

      await new Promise(resolve => setTimeout(resolve, 1100)) // Wait for refresh delay

      expect(mockCompileVueComponent).toHaveBeenCalledTimes(2)
    })

    it('should emit compile-success event on successful compilation', async () => {
      wrapper = mount(PreviewFrame, {
        props: {
          componentCode: '<template><div>Test</div></template>'
        }
      })

      await nextTick()
      await new Promise(resolve => setTimeout(resolve, 50))

      expect(wrapper.emitted('compile-success')).toBeTruthy()
      expect(wrapper.emitted('compile-success')).toHaveLength(1)
    })

    it('should handle compilation errors gracefully', async () => {
      const compileError = {
        message: 'Syntax error in template',
        line: 1,
        column: 10,
        file: 'PreviewComponent.vue'
      }

      mockCompileVueComponent.mockResolvedValue({
        script: '',
        template: '',
        styles: [],
        errors: [compileError],
        warnings: []
      })

      wrapper = mount(PreviewFrame, {
        props: {
          componentCode: '<template><div>Invalid syntax</div></template>'
        }
      })

      await nextTick()
      await new Promise(resolve => setTimeout(resolve, 50))

      expect(wrapper.find('.error-display').exists()).toBe(true)
      expect(wrapper.find('.error-message').text()).toContain('Syntax error in template')
      expect(wrapper.find('.error-location').text()).toContain('第 1 行')
      expect(wrapper.emitted('compile-error')).toBeTruthy()
    })

    it('should handle compilation exceptions', async () => {
      mockCompileVueComponent.mockRejectedValue(new Error('Compilation failed'))

      wrapper = mount(PreviewFrame, {
        props: {
          componentCode: '<template><div>Test</div></template>'
        }
      })

      await nextTick()
      await new Promise(resolve => setTimeout(resolve, 50))

      expect(wrapper.find('.error-display').exists()).toBe(true)
      expect(wrapper.find('.error-message').text()).toContain('Compilation failed')
      expect(wrapper.emitted('compile-error')).toBeTruthy()
    })
  })

  describe('Preview Generation', () => {
    it('should generate correct preview HTML', async () => {
      const mockResult = {
        script: 'const PreviewComponent = { name: "TestComponent" };',
        template: 'function render() { return h("div", "Hello"); }',
        styles: ['.test { color: blue; }'],
        errors: [],
        warnings: []
      }

      mockCompileVueComponent.mockResolvedValue(mockResult)

      wrapper = mount(PreviewFrame, {
        props: {
          componentCode: '<template><div>Hello</div></template>'
        }
      })

      await nextTick()
      await new Promise(resolve => setTimeout(resolve, 50))

      const iframe = wrapper.find('.preview-iframe').element as HTMLIFrameElement
      expect(iframe.srcdoc).toContain('<!DOCTYPE html>')
      expect(iframe.srcdoc).toContain('Vue Component Preview')
      expect(iframe.srcdoc).toContain('const PreviewComponent = { name: "TestComponent" };')
      expect(iframe.srcdoc).toContain('.test { color: blue; }')
      expect(iframe.srcdoc).toContain('https://unpkg.com/vue@3/dist/vue.global.js')
      expect(iframe.srcdoc).toContain('https://cdn.tailwindcss.com')
    })

    it('should include Tailwind CSS in preview', async () => {
      wrapper = mount(PreviewFrame, {
        props: {
          componentCode: '<template><div class="bg-blue-500">Styled</div></template>'
        }
      })

      await nextTick()
      await new Promise(resolve => setTimeout(resolve, 50))

      const iframe = wrapper.find('.preview-iframe').element as HTMLIFrameElement
      expect(iframe.srcdoc).toContain('https://cdn.tailwindcss.com')
    })

    it('should handle multiple styles correctly', async () => {
      const mockResult = {
        script: 'const PreviewComponent = {};',
        template: 'function render() { return h("div"); }',
        styles: [
          '.component { background: red; }',
          '.scoped { color: blue; }'
        ],
        errors: [],
        warnings: []
      }

      mockCompileVueComponent.mockResolvedValue(mockResult)

      wrapper = mount(PreviewFrame, {
        props: {
          componentCode: '<template><div>Test</div></template>'
        }
      })

      await nextTick()
      await new Promise(resolve => setTimeout(resolve, 50))

      const iframe = wrapper.find('.preview-iframe').element as HTMLIFrameElement
      expect(iframe.srcdoc).toContain('.component { background: red; }')
      expect(iframe.srcdoc).toContain('.scoped { color: blue; }')
    })
  })

  describe('Error Handling', () => {
    it('should display compile errors with details', async () => {
      const error = {
        message: 'Template syntax error',
        line: 5,
        column: 12,
        file: 'Component.vue'
      }

      mockCompileVueComponent.mockResolvedValue({
        script: '',
        template: '',
        styles: [],
        errors: [error],
        warnings: []
      })

      wrapper = mount(PreviewFrame, {
        props: {
          componentCode: '<template><div>Invalid</div></template>'
        }
      })

      await nextTick()
      await new Promise(resolve => setTimeout(resolve, 50))

      expect(wrapper.find('.error-display').exists()).toBe(true)
      expect(wrapper.find('.error-message').text()).toBe('Template syntax error')
      expect(wrapper.find('.error-location').text()).toContain('第 5 行，第 12 列')
      expect(wrapper.find('.error-file').text()).toContain('Component.vue')
    })

    it('should allow clearing compile errors', async () => {
      mockCompileVueComponent.mockResolvedValue({
        script: '',
        template: '',
        styles: [],
        errors: [{ message: 'Test error' }],
        warnings: []
      })

      wrapper = mount(PreviewFrame, {
        props: {
          componentCode: '<template><div>Test</div></template>'
        }
      })

      await nextTick()
      await new Promise(resolve => setTimeout(resolve, 50))

      expect(wrapper.find('.error-display').exists()).toBe(true)

      await wrapper.find('.clear-error-btn').trigger('click')

      expect(wrapper.find('.error-display').exists()).toBe(false)
    })

    it('should handle runtime errors from iframe', async () => {
      wrapper = mount(PreviewFrame, {
        props: {
          componentCode: '<template><div>Test</div></template>'
        }
      })

      await nextTick()
      await new Promise(resolve => setTimeout(resolve, 50))

      // Get the component instance to call the message handler directly
      const vm = wrapper.vm as any
      
      // Simulate runtime error by calling the handler directly
      vm.handleIframeMessage({
        data: {
          type: 'runtime-error',
          error: {
            message: 'Runtime error occurred',
            stack: 'Error stack trace'
          }
        }
      })

      await nextTick()

      expect(wrapper.find('.runtime-error-display').exists()).toBe(true)
      expect(wrapper.find('.error-message').text()).toContain('Runtime error occurred')
      expect(wrapper.emitted('runtime-error')).toBeTruthy()
    })

    it('should allow clearing runtime errors', async () => {
      wrapper = mount(PreviewFrame, {
        props: {
          componentCode: '<template><div>Test</div></template>'
        }
      })

      await nextTick()
      await new Promise(resolve => setTimeout(resolve, 50))

      // Get the component instance to call the message handler directly
      const vm = wrapper.vm as any
      
      // Simulate runtime error
      vm.handleIframeMessage({
        data: {
          type: 'runtime-error',
          error: { message: 'Runtime error' }
        }
      })

      await nextTick()

      expect(wrapper.find('.runtime-error-display').exists()).toBe(true)

      await wrapper.find('.clear-error-btn').trigger('click')

      expect(wrapper.find('.runtime-error-display').exists()).toBe(false)
    })
  })

  describe('User Interactions', () => {
    it('should refresh preview when refresh button clicked', async () => {
      wrapper = mount(PreviewFrame, {
        props: {
          componentCode: '<template><div>Test</div></template>'
        }
      })

      await nextTick()
      await new Promise(resolve => setTimeout(resolve, 50))

      // Clear previous calls
      mockCompileVueComponent.mockClear()

      await wrapper.find('.refresh-btn').trigger('click')
      await nextTick()

      expect(mockCompileVueComponent).toHaveBeenCalledTimes(1)
    })

    it('should toggle fullscreen mode', async () => {
      wrapper = mount(PreviewFrame, {
        props: {
          componentCode: '<template><div>Test</div></template>'
        }
      })

      expect(wrapper.find('.fullscreen-overlay').exists()).toBe(false)

      await wrapper.find('.fullscreen-btn').trigger('click')

      expect(wrapper.find('.fullscreen-overlay').exists()).toBe(true)
      expect(wrapper.find('.fullscreen-content').exists()).toBe(true)

      await wrapper.find('.close-fullscreen-btn').trigger('click')

      expect(wrapper.find('.fullscreen-overlay').exists()).toBe(false)
    })

    it('should close fullscreen when clicking overlay', async () => {
      wrapper = mount(PreviewFrame, {
        props: {
          componentCode: '<template><div>Test</div></template>'
        }
      })

      await wrapper.find('.fullscreen-btn').trigger('click')
      expect(wrapper.find('.fullscreen-overlay').exists()).toBe(true)

      await wrapper.find('.fullscreen-overlay').trigger('click')
      expect(wrapper.find('.fullscreen-overlay').exists()).toBe(false)
    })

    it('should not close fullscreen when clicking content', async () => {
      wrapper = mount(PreviewFrame, {
        props: {
          componentCode: '<template><div>Test</div></template>'
        }
      })

      await wrapper.find('.fullscreen-btn').trigger('click')
      expect(wrapper.find('.fullscreen-overlay').exists()).toBe(true)

      await wrapper.find('.fullscreen-content').trigger('click')
      expect(wrapper.find('.fullscreen-overlay').exists()).toBe(true)
    })
  })

  describe('Status Display', () => {
    it('should show correct status text for different states', async () => {
      wrapper = mount(PreviewFrame, {
        props: {
          componentCode: ''
        }
      })

      // Initial idle state
      expect(wrapper.find('.status-text').text()).toBe('待機中')

      // Update with component code to trigger compilation
      await wrapper.setProps({
        componentCode: '<template><div>Test</div></template>'
      })

      await nextTick()

      // Should show success after compilation
      await new Promise(resolve => setTimeout(resolve, 50))
      expect(wrapper.find('.status-text').text()).toBe('編譯成功')
    })

    it('should show error status when compilation fails', async () => {
      mockCompileVueComponent.mockResolvedValue({
        script: '',
        template: '',
        styles: [],
        errors: [{ message: 'Compilation error' }],
        warnings: []
      })

      wrapper = mount(PreviewFrame, {
        props: {
          componentCode: '<template><div>Invalid</div></template>'
        }
      })

      await nextTick()
      await new Promise(resolve => setTimeout(resolve, 50))

      expect(wrapper.find('.status-text').text()).toBe('編譯錯誤')
    })
  })

  describe('Props and Configuration', () => {
    it('should respect autoRefresh prop', async () => {
      wrapper = mount(PreviewFrame, {
        props: {
          componentCode: '<template><div>Initial</div></template>',
          autoRefresh: false
        }
      })

      await nextTick()
      await new Promise(resolve => setTimeout(resolve, 50))

      mockCompileVueComponent.mockClear()

      // Change component code
      await wrapper.setProps({
        componentCode: '<template><div>Updated</div></template>'
      })

      // Wait longer than default refresh delay
      await new Promise(resolve => setTimeout(resolve, 1500))

      // Should not have been called again due to autoRefresh: false
      expect(mockCompileVueComponent).not.toHaveBeenCalled()
    })

    it('should use custom refresh delay', async () => {
      wrapper = mount(PreviewFrame, {
        props: {
          componentCode: '<template><div>Initial</div></template>',
          autoRefresh: true,
          refreshDelay: 500
        }
      })

      await nextTick()
      await new Promise(resolve => setTimeout(resolve, 50))

      mockCompileVueComponent.mockClear()

      // Change component code
      await wrapper.setProps({
        componentCode: '<template><div>Updated</div></template>'
      })

      // Wait for custom delay
      await new Promise(resolve => setTimeout(resolve, 600))

      expect(mockCompileVueComponent).toHaveBeenCalledTimes(1)
    })

    it('should handle dependencies prop', async () => {
      const dependencies = ['vue-router', 'pinia']

      wrapper = mount(PreviewFrame, {
        props: {
          componentCode: '<template><div>Test</div></template>',
          dependencies
        }
      })

      await nextTick()

      // Change dependencies
      await wrapper.setProps({
        dependencies: ['vue-router', 'pinia', 'axios']
      })

      await new Promise(resolve => setTimeout(resolve, 1100))

      // Should trigger recompilation
      expect(mockCompileVueComponent).toHaveBeenCalledTimes(2)
    })
  })

  describe('Component Lifecycle', () => {
    it('should clean up on unmount', async () => {
      const addEventListenerSpy = vi.spyOn(window, 'addEventListener')
      const removeEventListenerSpy = vi.spyOn(window, 'removeEventListener')

      wrapper = mount(PreviewFrame, {
        props: {
          componentCode: '<template><div>Test</div></template>'
        }
      })

      expect(addEventListenerSpy).toHaveBeenCalledWith('message', expect.any(Function))

      wrapper.unmount()

      expect(removeEventListenerSpy).toHaveBeenCalledWith('message', expect.any(Function))

      addEventListenerSpy.mockRestore()
      removeEventListenerSpy.mockRestore()
    })

    it('should expose component methods', () => {
      wrapper = mount(PreviewFrame, {
        props: {
          componentCode: '<template><div>Test</div></template>'
        }
      })

      const vm = wrapper.vm as any

      expect(typeof vm.refreshPreview).toBe('function')
      expect(typeof vm.clearError).toBe('function')
      expect(typeof vm.clearRuntimeError).toBe('function')
      expect(typeof vm.toggleFullscreen).toBe('function')
      expect(typeof vm.getStatus).toBe('function')
      expect(typeof vm.hasError).toBe('function')
    })
  })

  describe('Message Handling', () => {
    it('should handle preview-ready message', async () => {
      wrapper = mount(PreviewFrame, {
        props: {
          componentCode: '<template><div>Test</div></template>'
        }
      })

      await nextTick()

      // Get the component instance to call the message handler directly
      const vm = wrapper.vm as any
      
      vm.handleIframeMessage({
        data: { type: 'preview-ready' }
      })

      await nextTick()

      expect(wrapper.emitted('preview-ready')).toBeTruthy()
    })

    it('should ignore unrelated messages', async () => {
      wrapper = mount(PreviewFrame, {
        props: {
          componentCode: '<template><div>Test</div></template>'
        }
      })

      await nextTick()

      // Get the component instance to call the message handler directly
      const vm = wrapper.vm as any
      
      vm.handleIframeMessage({
        data: { type: 'unrelated', data: 'test' }
      })

      await nextTick()

      // Should not emit any events for unrelated messages
      expect(wrapper.emitted('runtime-error')).toBeFalsy()
      expect(wrapper.emitted('preview-ready')).toBeFalsy()
    })
  })
})