import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { DatabaseTestManager } from '../utils/database-test-manager'
import { TestDataFactory } from '../utils/test-data-factory'

describe('DatabaseTestManager', () => {
  let databaseManager: DatabaseTestManager
  let testDataFactory: TestDataFactory

  beforeEach(async () => {
    databaseManager = new DatabaseTestManager()
    await databaseManager.initializeTestDatabase()
    testDataFactory = new TestDataFactory(databaseManager.getPrismaClient())
  })

  afterEach(async () => {
    await databaseManager.cleanup()
  })

  describe('Database Initialization', () => {
    it('should initialize test database successfully', async () => {
      const isConnected = await databaseManager.checkConnection()
      expect(isConnected).toBe(true)
    })

    it('should reset database to clean state', async () => {
      // Create some test data first
      await testDataFactory.createProject()
      
      // Verify data exists
      let counts = await databaseManager.getTableCounts()
      expect(counts.projects).toBeGreaterThan(0)

      // Reset database
      await databaseManager.resetDatabase()
      
      // Verify data is cleared
      counts = await databaseManager.getTableCounts()
      expect(counts.projects).toBe(0)
      expect(counts.files).toBe(0)
    })

    it('should handle database connection errors gracefully', async () => {
      // Create a manager with invalid database URL
      const invalidManager = new DatabaseTestManager()
      
      // Mock the prisma client to simulate connection failure
      const mockPrisma = {
        $connect: vi.fn().mockRejectedValue(new Error('Connection failed')),
        $disconnect: vi.fn()
      }
      
      // Replace the prisma client
      ;(invalidManager as any).prisma = mockPrisma

      await expect(invalidManager.initializeTestDatabase()).rejects.toThrow('Connection failed')
    })
  })

  describe('Test Data Management', () => {
    it('should create test project successfully', async () => {
      const projectData = {
        name: 'Test Project',
        description: 'A test project for testing'
      }

      const project = await databaseManager.createTestData('project', projectData)

      expect(project).toBeDefined()
      expect(project.id).toBeDefined()
      expect(project.name).toBe(projectData.name)
      expect(project.description).toBe(projectData.description)
    })

    it('should create test file with project relationship', async () => {
      // First create a project
      const project = await databaseManager.createTestData('project', {
        name: 'Parent Project'
      })

      // Then create a file for that project
      const fileData = {
        name: 'test.js',
        content: 'console.log("Hello, World!");',
        projectId: project.id
      }

      const file = await databaseManager.createTestData('file', fileData)

      expect(file).toBeDefined()
      expect(file.id).toBeDefined()
      expect(file.name).toBe(fileData.name)
      expect(file.content).toBe(fileData.content)
      expect(file.projectId).toBe(project.id)
    })

    it('should handle foreign key constraint violations', async () => {
      const fileData = {
        name: 'orphan.js',
        content: 'console.log("I have no parent");',
        projectId: 'non-existent-project-id'
      }

      await expect(
        databaseManager.createTestData('file', fileData)
      ).rejects.toThrow()
    })

    it('should track created records for cleanup', async () => {
      const project = await databaseManager.createTestData('project', {
        name: 'Tracked Project'
      })

      const file = await databaseManager.createTestData('file', {
        name: 'tracked.js',
        content: 'tracked content',
        projectId: project.id
      })

      // Verify records exist
      let counts = await databaseManager.getTableCounts()
      expect(counts.projects).toBeGreaterThan(0)
      expect(counts.files).toBeGreaterThan(0)

      // Cleanup should remove tracked records
      await databaseManager.cleanupTestData()

      counts = await databaseManager.getTableCounts()
      expect(counts.projects).toBe(0)
      expect(counts.files).toBe(0)
    })
  })

  describe('Transaction Management', () => {
    it('should execute operations within transaction', async () => {
      const result = await databaseManager.withTransaction(async (prisma) => {
        const project = await prisma.project.create({
          data: {
            name: 'Transaction Project',
            description: 'Created in transaction'
          }
        })

        const file = await prisma.file.create({
          data: {
            name: 'transaction.js',
            content: 'transaction content',
            projectId: project.id
          }
        })

        return { project, file }
      })

      expect(result.project).toBeDefined()
      expect(result.file).toBeDefined()
      expect(result.file.projectId).toBe(result.project.id)

      // Verify data was actually created
      const counts = await databaseManager.getTableCounts()
      expect(counts.projects).toBeGreaterThan(0)
      expect(counts.files).toBeGreaterThan(0)
    })

    it('should rollback transaction on error', async () => {
      const initialCounts = await databaseManager.getTableCounts()

      await expect(
        databaseManager.withTransaction(async (prisma) => {
          // Create a project
          await prisma.project.create({
            data: {
              name: 'Rollback Project',
              description: 'This should be rolled back'
            }
          })

          // Throw an error to trigger rollback
          throw new Error('Transaction failed')
        })
      ).rejects.toThrow('Transaction failed')

      // Verify no data was created due to rollback
      const finalCounts = await databaseManager.getTableCounts()
      expect(finalCounts.projects).toBe(initialCounts.projects)
    })
  })

  describe('Seed Data', () => {
    it('should seed test data successfully', async () => {
      await databaseManager.seedTestData()

      const counts = await databaseManager.getTableCounts()
      expect(counts.projects).toBeGreaterThan(0)
      expect(counts.files).toBeGreaterThan(0)
    })

    it('should create seed data with proper relationships', async () => {
      await databaseManager.seedTestData()

      const prisma = databaseManager.getPrismaClient()
      const projects = await prisma.project.findMany({
        include: { files: true }
      })

      expect(projects.length).toBeGreaterThan(0)
      
      const projectWithFiles = projects.find(p => p.files.length > 0)
      expect(projectWithFiles).toBeDefined()
      expect(projectWithFiles!.files[0].projectId).toBe(projectWithFiles!.id)
    })
  })

  describe('Database Statistics', () => {
    it('should return accurate table counts', async () => {
      // Create known amount of test data
      const project1 = await testDataFactory.createProject()
      const project2 = await testDataFactory.createProject()
      
      await testDataFactory.createFile(project1.id)
      await testDataFactory.createFile(project1.id)
      await testDataFactory.createFile(project2.id)

      const counts = await databaseManager.getTableCounts()
      expect(counts.projects).toBe(2)
      expect(counts.files).toBe(3)
    })

    it('should handle empty database counts', async () => {
      await databaseManager.resetDatabase()
      
      const counts = await databaseManager.getTableCounts()
      expect(counts.projects).toBe(0)
      expect(counts.files).toBe(0)
    })
  })

  describe('Error Handling', () => {
    it('should provide clear error messages for constraint violations', async () => {
      try {
        await databaseManager.createTestData('file', {
          name: 'invalid.js',
          content: 'content',
          projectId: 'invalid-id'
        })
      } catch (error) {
        expect(error).toBeDefined()
        expect(error.message).toContain('Foreign key constraint')
      }
    })

    it('should handle cleanup errors gracefully', async () => {
      // Mock prisma to simulate cleanup error
      const mockPrisma = {
        file: {
          deleteMany: vi.fn().mockRejectedValue(new Error('Cleanup failed'))
        },
        project: {
          deleteMany: vi.fn()
        },
        $disconnect: vi.fn()
      }

      ;(databaseManager as any).prisma = mockPrisma
      ;(databaseManager as any).createdRecords.set('file', ['test-id'])

      await expect(databaseManager.cleanupTestData()).rejects.toThrow('Cleanup failed')
    })
  })

  describe('Concurrent Access', () => {
    it('should handle concurrent database operations', async () => {
      const operations = []
      
      // Create multiple concurrent operations
      for (let i = 0; i < 5; i++) {
        operations.push(
          testDataFactory.createProject({
            overrides: { name: `Concurrent Project ${i}` }
          })
        )
      }

      const results = await Promise.all(operations)
      
      expect(results).toHaveLength(5)
      results.forEach((project, index) => {
        expect(project.name).toBe(`Concurrent Project ${index}`)
      })

      // Verify all projects were created
      const counts = await databaseManager.getTableCounts()
      expect(counts.projects).toBe(5)
    })

    it('should maintain data integrity under concurrent access', async () => {
      const project = await testDataFactory.createProject()
      
      // Create multiple files concurrently for the same project
      const fileOperations = []
      for (let i = 0; i < 10; i++) {
        fileOperations.push(
          testDataFactory.createFile(project.id, {
            overrides: { name: `concurrent-${i}.js` }
          })
        )
      }

      const files = await Promise.all(fileOperations)
      
      expect(files).toHaveLength(10)
      files.forEach((file, index) => {
        expect(file.projectId).toBe(project.id)
        expect(file.name).toBe(`concurrent-${index}.js`)
      })
    })
  })
})