import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { mount, VueWrapper } from '@vue/test-utils'
import { nextTick } from 'vue'
import PreviewFrame from '../../src/components/preview/PreviewFrame.vue'
import * as vueCompiler from '../../src/lib/vue-compiler'

// Mock the vue-compiler module
vi.mock('../../src/lib/vue-compiler', () => ({
  compileVueComponent: vi.fn()
}))

describe('PreviewFrame Hot Reload', () => {
  let wrapper: VueWrapper<any>
  let mockCompileVueComponent: any

  beforeEach(() => {
    mockCompileVueComponent = vi.mocked(vueCompiler.compileVueComponent)
    vi.clearAllMocks()
    
    mockCompileVueComponent.mockResolvedValue({
      script: 'const PreviewComponent = { name: "TestComponent" };',
      template: 'function render() { return h("div", "Hello World"); }',
      styles: ['.test { color: red; }'],
      errors: [],
      warnings: []
    })
  })

  afterEach(() => {
    if (wrapper) {
      wrapper.unmount()
    }
  })

  it('should implement hot reload on component code changes', async () => {
    wrapper = mount(PreviewFrame, {
      props: {
        componentCode: '<template><div>Initial</div></template>',
        autoRefresh: true,
        refreshDelay: 100
      }
    })

    await nextTick()
    await new Promise(resolve => setTimeout(resolve, 50))

    expect(mockCompileVueComponent).toHaveBeenCalledTimes(1)

    // Change the component code
    await wrapper.setProps({
      componentCode: '<template><div>Updated</div></template>'
    })

    // Wait for the refresh delay
    await new Promise(resolve => setTimeout(resolve, 150))

    expect(mockCompileVueComponent).toHaveBeenCalledTimes(2)
    expect(mockCompileVueComponent).toHaveBeenLastCalledWith(
      '<template><div>Updated</div></template>',
      'PreviewComponent.vue'
    )
  })

  it('should show loading indicator during hot reload', async () => {
    let resolveCompilation: any
    mockCompileVueComponent.mockImplementation(() => 
      new Promise(resolve => {
        resolveCompilation = resolve
      })
    )

    wrapper = mount(PreviewFrame, {
      props: {
        componentCode: '<template><div>Test</div></template>'
      }
    })

    await nextTick()
    await new Promise(resolve => setTimeout(resolve, 10))

    // Should show loading state
    expect(wrapper.find('.loading-overlay').exists()).toBe(true)
    expect(wrapper.find('.loading-spinner').exists()).toBe(true)

    // Resolve compilation
    if (resolveCompilation) {
      resolveCompilation({
        script: 'const PreviewComponent = {};',
        template: 'function render() { return h("div"); }',
        styles: [],
        errors: [],
        warnings: []
      })
    }

    await nextTick()
    await new Promise(resolve => setTimeout(resolve, 10))

    // Loading should be gone
    expect(wrapper.find('.loading-overlay').exists()).toBe(false)
  })

  it('should display compilation errors with proper error handling', async () => {
    const compileError = {
      message: 'Template syntax error',
      line: 1,
      column: 5,
      file: 'Component.vue'
    }

    mockCompileVueComponent.mockResolvedValue({
      script: '',
      template: '',
      styles: [],
      errors: [compileError],
      warnings: []
    })

    wrapper = mount(PreviewFrame, {
      props: {
        componentCode: '<template><div>Invalid syntax</div></template>'
      }
    })

    await nextTick()
    await new Promise(resolve => setTimeout(resolve, 50))

    // Should show error display
    expect(wrapper.find('.error-display').exists()).toBe(true)
    expect(wrapper.find('.error-message').text()).toContain('Template syntax error')
    expect(wrapper.find('.error-location').text()).toContain('第 1 行')
    expect(wrapper.find('.error-file').text()).toContain('Component.vue')

    // Should have error actions
    expect(wrapper.find('.clear-error-btn').exists()).toBe(true)
    expect(wrapper.find('.retry-btn').exists()).toBe(true)
  })

  it('should handle runtime errors from preview iframe', async () => {
    wrapper = mount(PreviewFrame, {
      props: {
        componentCode: '<template><div>Test</div></template>'
      }
    })

    await nextTick()
    await new Promise(resolve => setTimeout(resolve, 50))

    // Simulate runtime error
    const vm = wrapper.vm as any
    vm.handleIframeMessage({
      data: {
        type: 'runtime-error',
        error: {
          message: 'Component runtime error',
          stack: 'Error stack trace'
        }
      }
    })

    await nextTick()

    // Should show runtime error display
    expect(wrapper.find('.runtime-error-display').exists()).toBe(true)
    expect(wrapper.find('.error-message').text()).toContain('Component runtime error')
    expect(wrapper.find('.error-stack').exists()).toBe(true)
  })

  it('should allow clearing errors and retrying compilation', async () => {
    mockCompileVueComponent.mockResolvedValue({
      script: '',
      template: '',
      styles: [],
      errors: [{ message: 'Test error' }],
      warnings: []
    })

    wrapper = mount(PreviewFrame, {
      props: {
        componentCode: '<template><div>Test</div></template>'
      }
    })

    await nextTick()
    await new Promise(resolve => setTimeout(resolve, 50))

    expect(wrapper.find('.error-display').exists()).toBe(true)

    // Clear the error
    await wrapper.find('.clear-error-btn').trigger('click')
    expect(wrapper.find('.error-display').exists()).toBe(false)

    // Mock successful compilation for retry
    mockCompileVueComponent.mockResolvedValue({
      script: 'const PreviewComponent = {};',
      template: 'function render() { return h("div"); }',
      styles: [],
      errors: [],
      warnings: []
    })

    // Retry compilation
    const retryBtn = wrapper.find('.retry-btn')
    if (retryBtn.exists()) {
      await retryBtn.trigger('click')
    } else {
      // If retry button doesn't exist, manually trigger refresh
      const vm = wrapper.vm as any
      vm.refreshPreview()
    }
    await nextTick()
    await new Promise(resolve => setTimeout(resolve, 50))

    // Should show preview content now
    expect(wrapper.find('.preview-content').exists()).toBe(true)
    expect(wrapper.find('.error-display').exists()).toBe(false)
  })
})