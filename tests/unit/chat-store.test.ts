// /tests/unit/chat-store.test.ts
import { setActivePinia, createPinia } from 'pinia';
import { useChatStore } from '../../src/stores/chat';
import { describe, it, expect, beforeEach, vi } from 'vitest';

describe('Chat Store', () => {
  beforeEach(() => {
    // 為每個測試建立一個新的 Pinia 實例
    setActivePinia(createPinia());
  });

  it('初始狀態應為空', () => {
    const chatStore = useChatStore();
    // 初始訊息列表應為空
    expect(chatStore.messages.length).toBe(0);
    // 初始不應處於載入狀態
    expect(chatStore.isLoading).toBe(false);
    // 初始 WebSocket 應為 null
    expect(chatStore.socket).toBeNull();
  });

  it('sendMessage 應能新增使用者訊息並設定 isLoading', async () => {
    const chatStore = useChatStore();
    const messageContent = '你好';

    // Mock WebSocket
    const mockWebSocket = {
      readyState: WebSocket.OPEN,
      send: vi.fn(),
      close: vi.fn(),
    };
    chatStore.socket = mockWebSocket as any;
    chatStore.connectionStatus = 'connected';

    // 執行 sendMessage
    await chatStore.sendMessage(messageContent);

    // 驗證訊息列表
    expect(chatStore.messages.length).toBe(1);
    const userMessage = chatStore.messages[0];
    expect(userMessage.type).toBe('user');
    expect(userMessage.content).toBe(messageContent);

    // 驗證 isLoading 狀態
    expect(chatStore.isLoading).toBe(true);
  });
});
