import { describe, it, expect, vi, beforeEach } from 'vitest';
import { mount } from '@vue/test-utils';
import MessageList from '../../src/components/chat/MessageList.vue';
import type { ChatMessage } from '../../src/stores/chat';

describe('MessageList 組件', () => {
  const mockMessages: ChatMessage[] = [
    {
      id: '1',
      type: 'user',
      content: '測試使用者訊息',
      timestamp: new Date('2024-01-01T10:00:00Z')
    },
    {
      id: '2',
      type: 'ai',
      content: '測試 AI 回應',
      timestamp: new Date('2024-01-01T10:01:00Z')
    },
    {
      id: '3',
      type: 'system',
      content: '系統訊息',
      timestamp: new Date('2024-01-01T10:02:00Z')
    }
  ];

  beforeEach(() => {
    // Mock navigator.clipboard
    Object.assign(navigator, {
      clipboard: {
        writeText: vi.fn().mockResolvedValue(undefined)
      }
    });

    // Mock window.confirm
    global.confirm = vi.fn().mockReturnValue(true);
  });

  describe('空狀態顯示', () => {
    it('當沒有訊息時應該顯示空狀態', () => {
      const wrapper = mount(MessageList, {
        props: {
          messages: [],
          isLoading: false,
          currentStreamingMessageId: ''
        }
      });

      expect(wrapper.find('.empty-state').exists()).toBe(true);
      expect(wrapper.text()).toContain('開始與 AI 對話');
      expect(wrapper.findAll('.suggestion-chip')).toHaveLength(4);
    });

    it('點擊建議問題應該觸發事件', async () => {
      const wrapper = mount(MessageList, {
        props: {
          messages: [],
          isLoading: false,
          currentStreamingMessageId: ''
        }
      });

      const suggestionChip = wrapper.find('.suggestion-chip');
      await suggestionChip.trigger('click');

      expect(wrapper.emitted('suggestion-selected')).toBeTruthy();
      expect(wrapper.emitted('suggestion-selected')?.[0]).toEqual(['生成一個響應式的導航欄組件']);
    });
  });

  describe('訊息顯示', () => {
    it('應該正確顯示所有類型的訊息', () => {
      const wrapper = mount(MessageList, {
        props: {
          messages: mockMessages,
          isLoading: false,
          currentStreamingMessageId: ''
        }
      });

      expect(wrapper.findAll('.message-wrapper')).toHaveLength(3);
      
      // 檢查使用者訊息
      const userMessage = wrapper.find('.message-user');
      expect(userMessage.exists()).toBe(true);
      expect(userMessage.text()).toContain('測試使用者訊息');
      expect(userMessage.text()).toContain('您');

      // 檢查 AI 訊息
      const aiMessage = wrapper.find('.message-ai');
      expect(aiMessage.exists()).toBe(true);
      expect(aiMessage.text()).toContain('AI 助手');

      // 檢查系統訊息
      const systemMessage = wrapper.find('.message-system');
      expect(systemMessage.exists()).toBe(true);
      expect(systemMessage.text()).toContain('系統');
    });

    it('應該正確格式化時間', () => {
      const wrapper = mount(MessageList, {
        props: {
          messages: [mockMessages[0]],
          isLoading: false,
          currentStreamingMessageId: ''
        }
      });

      const timeElement = wrapper.find('.message-time');
      expect(timeElement.exists()).toBe(true);
      // 時間格式應該包含時間資訊（可能包含上午/下午）
      expect(timeElement.text()).toMatch(/\d{1,2}:\d{2}/);
    });

    it('應該顯示串流指示器', () => {
      const wrapper = mount(MessageList, {
        props: {
          messages: mockMessages,
          isLoading: false,
          currentStreamingMessageId: '2'
        }
      });

      const streamingIndicator = wrapper.find('.streaming-indicator');
      expect(streamingIndicator.exists()).toBe(true);
      expect(streamingIndicator.text()).toBe('⚡');
    });
  });

  describe('AI 訊息格式化', () => {
    it('應該正確格式化 Markdown 內容', () => {
      const messageWithMarkdown: ChatMessage = {
        id: '1',
        type: 'ai',
        content: '這是 **粗體** 和 *斜體* 文字，還有 `程式碼`',
        timestamp: new Date()
      };

      const wrapper = mount(MessageList, {
        props: {
          messages: [messageWithMarkdown],
          isLoading: false,
          currentStreamingMessageId: ''
        }
      });

      const aiMessage = wrapper.find('.ai-message');
      const html = aiMessage.html();
      
      expect(html).toContain('<strong>粗體</strong>');
      expect(html).toContain('<em>斜體</em>');
      expect(html).toContain('<code>程式碼</code>');
    });

    it('應該正確格式化程式碼塊', () => {
      const messageWithCode: ChatMessage = {
        id: '1',
        type: 'ai',
        content: '```javascript\nconsole.log("Hello World");\n```',
        timestamp: new Date()
      };

      const wrapper = mount(MessageList, {
        props: {
          messages: [messageWithCode],
          isLoading: false,
          currentStreamingMessageId: ''
        }
      });

      const aiMessage = wrapper.find('.ai-message');
      const html = aiMessage.html();
      
      expect(html).toContain('<pre><code class="language-javascript">');
      expect(html).toContain('console.log("Hello World");');
    });

    it('包含程式碼的訊息應該顯示操作按鈕', () => {
      const messageWithCode: ChatMessage = {
        id: '1',
        type: 'ai',
        content: '```vue\n<template>\n  <div>Hello</div>\n</template>\n```',
        timestamp: new Date()
      };

      const wrapper = mount(MessageList, {
        props: {
          messages: [messageWithCode],
          isLoading: false,
          currentStreamingMessageId: ''
        }
      });

      expect(wrapper.find('.copy-code-btn').exists()).toBe(true);
      expect(wrapper.find('.apply-code-btn').exists()).toBe(true);
    });
  });

  describe('使用者互動', () => {
    it('點擊刪除按鈕應該觸發事件', async () => {
      const wrapper = mount(MessageList, {
        props: {
          messages: [mockMessages[0]],
          isLoading: false,
          currentStreamingMessageId: ''
        }
      });

      const deleteBtn = wrapper.find('.delete-message-btn');
      await deleteBtn.trigger('click');

      expect(wrapper.emitted('message-delete')).toBeTruthy();
      expect(wrapper.emitted('message-delete')?.[0]).toEqual(['1']);
    });

    it('點擊複製代碼按鈕應該觸發事件', async () => {
      const messageWithCode: ChatMessage = {
        id: '1',
        type: 'ai',
        content: '```javascript\nconsole.log("test");\n```',
        timestamp: new Date()
      };

      const wrapper = mount(MessageList, {
        props: {
          messages: [messageWithCode],
          isLoading: false,
          currentStreamingMessageId: ''
        }
      });

      const copyBtn = wrapper.find('.copy-code-btn');
      await copyBtn.trigger('click');

      expect(wrapper.emitted('code-copy')).toBeTruthy();
      expect(wrapper.emitted('code-copy')?.[0]).toEqual([messageWithCode.content]);
    });

    it('點擊應用代碼按鈕應該觸發事件', async () => {
      const messageWithCode: ChatMessage = {
        id: '1',
        type: 'ai',
        content: '```vue\n<template><div>Test</div></template>\n```',
        timestamp: new Date()
      };

      const wrapper = mount(MessageList, {
        props: {
          messages: [messageWithCode],
          isLoading: false,
          currentStreamingMessageId: ''
        }
      });

      const applyBtn = wrapper.find('.apply-code-btn');
      await applyBtn.trigger('click');

      expect(wrapper.emitted('code-apply')).toBeTruthy();
      expect(wrapper.emitted('code-apply')?.[0]).toEqual([messageWithCode.content]);
    });
  });

  describe('載入狀態', () => {
    it('載入中時應該顯示載入指示器', () => {
      const wrapper = mount(MessageList, {
        props: {
          messages: mockMessages,
          isLoading: true,
          currentStreamingMessageId: '',
          loadingText: '正在處理中...'
        }
      });

      expect(wrapper.find('.loading-message').exists()).toBe(true);
      expect(wrapper.find('.loading-dots').exists()).toBe(true);
      expect(wrapper.text()).toContain('正在處理中...');
    });

    it('不載入時不應該顯示載入指示器', () => {
      const wrapper = mount(MessageList, {
        props: {
          messages: mockMessages,
          isLoading: false,
          currentStreamingMessageId: ''
        }
      });

      expect(wrapper.find('.loading-message').exists()).toBe(false);
    });
  });

  describe('錯誤處理', () => {
    it('有錯誤訊息時應該顯示錯誤提示', () => {
      const wrapper = mount(MessageList, {
        props: {
          messages: mockMessages,
          isLoading: false,
          currentStreamingMessageId: '',
          errorMessage: '連接失敗'
        }
      });

      expect(wrapper.find('.error-message').exists()).toBe(true);
      expect(wrapper.text()).toContain('發生錯誤');
      expect(wrapper.text()).toContain('連接失敗');
      expect(wrapper.find('.error-retry-btn').exists()).toBe(true);
    });

    it('點擊重試按鈕應該觸發事件', async () => {
      const wrapper = mount(MessageList, {
        props: {
          messages: mockMessages,
          isLoading: false,
          currentStreamingMessageId: '',
          errorMessage: '連接失敗'
        }
      });

      const retryBtn = wrapper.find('.error-retry-btn');
      await retryBtn.trigger('click');

      expect(wrapper.emitted('error-retry')).toBeTruthy();
    });

    it('沒有錯誤訊息時不應該顯示錯誤提示', () => {
      const wrapper = mount(MessageList, {
        props: {
          messages: mockMessages,
          isLoading: false,
          currentStreamingMessageId: ''
        }
      });

      expect(wrapper.find('.error-message').exists()).toBe(false);
    });
  });

  describe('響應式設計', () => {
    it('應該包含響應式 CSS 類別', () => {
      const wrapper = mount(MessageList, {
        props: {
          messages: mockMessages,
          isLoading: false,
          currentStreamingMessageId: ''
        }
      });

      // 檢查是否有響應式相關的 CSS 類別
      expect(wrapper.find('.message-bubble').exists()).toBe(true);
      expect(wrapper.find('.suggestion-chips').exists()).toBe(false); // 因為有訊息所以不顯示
    });
  });

  describe('輔助功能', () => {
    it('刪除按鈕應該有適當的 title 屬性', () => {
      const wrapper = mount(MessageList, {
        props: {
          messages: [mockMessages[0]],
          isLoading: false,
          currentStreamingMessageId: ''
        }
      });

      const deleteBtn = wrapper.find('.delete-message-btn');
      expect(deleteBtn.attributes('title')).toBe('刪除訊息');
    });

    it('串流指示器應該有適當的 title 屬性', () => {
      const wrapper = mount(MessageList, {
        props: {
          messages: [mockMessages[0]],
          isLoading: false,
          currentStreamingMessageId: '1'
        }
      });

      const streamingIndicator = wrapper.find('.streaming-indicator');
      expect(streamingIndicator.attributes('title')).toBe('正在接收中...');
    });
  });
});