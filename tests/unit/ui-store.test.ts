// /tests/unit/ui-store.test.ts
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { setActivePinia, createPinia } from 'pinia';
import { useUIStore } from '@/stores/ui';
import type { NotificationAction } from '@/stores/ui';

describe('UI Store', () => {
  beforeEach(() => {
    setActivePinia(createPinia());
  });

  describe('通知系統', () => {
    it('應該能夠顯示成功通知', () => {
      const uiStore = useUIStore();
      
      const notificationId = uiStore.showSuccess('操作成功', '檔案已儲存');
      
      expect(uiStore.notifications).toHaveLength(1);
      expect(uiStore.notifications[0]).toMatchObject({
        type: 'success',
        title: '操作成功',
        message: '檔案已儲存',
        duration: 3000,
      });
      expect(notificationId).toBe(uiStore.notifications[0].id);
    });

    it('應該能夠顯示錯誤通知', () => {
      const uiStore = useUIStore();
      
      const actions: NotificationAction[] = [
        { label: '重試', action: vi.fn(), style: 'primary' }
      ];
      
      uiStore.showError('操作失敗', '網路連線錯誤', actions);
      
      expect(uiStore.notifications).toHaveLength(1);
      expect(uiStore.notifications[0]).toMatchObject({
        type: 'error',
        title: '操作失敗',
        message: '網路連線錯誤',
        duration: 0, // 錯誤訊息不自動關閉
        actions,
      });
    });

    it('應該能夠顯示警告通知', () => {
      const uiStore = useUIStore();
      
      uiStore.showWarning('注意', '檔案尚未儲存');
      
      expect(uiStore.notifications).toHaveLength(1);
      expect(uiStore.notifications[0]).toMatchObject({
        type: 'warning',
        title: '注意',
        message: '檔案尚未儲存',
        duration: 4000,
      });
    });

    it('應該能夠顯示資訊通知', () => {
      const uiStore = useUIStore();
      
      uiStore.showInfo('提示', '新功能已上線');
      
      expect(uiStore.notifications).toHaveLength(1);
      expect(uiStore.notifications[0]).toMatchObject({
        type: 'info',
        title: '提示',
        message: '新功能已上線',
        duration: 4000,
      });
    });

    it('應該能夠關閉通知', () => {
      const uiStore = useUIStore();
      
      const notificationId = uiStore.showSuccess('測試');
      expect(uiStore.notifications).toHaveLength(1);
      
      uiStore.dismissNotification(notificationId);
      expect(uiStore.notifications).toHaveLength(0);
    });

    it('應該能夠清空所有通知', () => {
      const uiStore = useUIStore();
      
      uiStore.showSuccess('通知 1');
      uiStore.showError('通知 2');
      uiStore.showWarning('通知 3');
      expect(uiStore.notifications).toHaveLength(3);
      
      uiStore.clearNotifications();
      expect(uiStore.notifications).toHaveLength(0);
    });

    it('應該能夠根據類型顯示使用者訊息', () => {
      const uiStore = useUIStore();
      
      uiStore.showUserMessage('成功訊息', 'success');
      uiStore.showUserMessage('錯誤訊息', 'error');
      uiStore.showUserMessage('警告訊息', 'warning');
      uiStore.showUserMessage('資訊訊息', 'info');
      
      expect(uiStore.notifications).toHaveLength(4);
      expect(uiStore.notifications[0].type).toBe('success');
      expect(uiStore.notifications[1].type).toBe('error');
      expect(uiStore.notifications[2].type).toBe('warning');
      expect(uiStore.notifications[3].type).toBe('info');
    });
  });

  describe('確認對話框', () => {
    it('應該能夠顯示確認對話框', () => {
      const uiStore = useUIStore();
      const onConfirm = vi.fn();
      const onCancel = vi.fn();
      
      const dialogId = uiStore.showConfirmDialog({
        title: '確認刪除',
        message: '您確定要刪除這個檔案嗎？',
        confirmText: '刪除',
        cancelText: '取消',
        type: 'danger',
        onConfirm,
        onCancel,
      });
      
      expect(uiStore.confirmDialogs).toHaveLength(1);
      expect(uiStore.confirmDialogs[0]).toMatchObject({
        title: '確認刪除',
        message: '您確定要刪除這個檔案嗎？',
        confirmText: '刪除',
        cancelText: '取消',
        type: 'danger',
        onConfirm,
        onCancel,
      });
      expect(dialogId).toBe(uiStore.confirmDialogs[0].id);
    });

    it('應該能夠關閉確認對話框', () => {
      const uiStore = useUIStore();
      
      const dialogId = uiStore.showConfirmDialog({
        title: '測試',
        message: '測試訊息',
        onConfirm: vi.fn(),
      });
      
      expect(uiStore.confirmDialogs).toHaveLength(1);
      
      uiStore.dismissConfirmDialog(dialogId);
      expect(uiStore.confirmDialogs).toHaveLength(0);
    });

    it('應該使用預設值', () => {
      const uiStore = useUIStore();
      
      uiStore.showConfirmDialog({
        title: '測試',
        message: '測試訊息',
        onConfirm: vi.fn(),
      });
      
      expect(uiStore.confirmDialogs[0]).toMatchObject({
        confirmText: '確認',
        cancelText: '取消',
        type: 'info',
      });
    });
  });

  describe('載入狀態', () => {
    it('應該能夠開始載入狀態', () => {
      const uiStore = useUIStore();
      
      const loadingId = uiStore.startLoading('正在載入...');
      
      expect(uiStore.isLoading).toBe(true);
      expect(uiStore.loadingStates).toHaveLength(1);
      expect(uiStore.loadingStates[0]).toMatchObject({
        message: '正在載入...',
      });
      expect(uiStore.primaryLoadingState).toBe(uiStore.loadingStates[0]);
      expect(loadingId).toBe(uiStore.loadingStates[0].id);
    });

    it('應該能夠開始帶選項的載入狀態', () => {
      const uiStore = useUIStore();
      const onCancel = vi.fn();
      
      uiStore.startLoading('正在上傳...', {
        progress: 50,
        canCancel: true,
        onCancel,
      });
      
      expect(uiStore.loadingStates[0]).toMatchObject({
        message: '正在上傳...',
        progress: 50,
        canCancel: true,
        onCancel,
      });
    });

    it('應該能夠更新載入狀態', () => {
      const uiStore = useUIStore();
      
      const loadingId = uiStore.startLoading('正在載入...');
      uiStore.updateLoading(loadingId, {
        message: '正在處理...',
        progress: 75,
      });
      
      expect(uiStore.loadingStates[0]).toMatchObject({
        message: '正在處理...',
        progress: 75,
      });
    });

    it('應該能夠結束載入狀態', () => {
      const uiStore = useUIStore();
      
      const loadingId = uiStore.startLoading('正在載入...');
      expect(uiStore.isLoading).toBe(true);
      
      uiStore.stopLoading(loadingId);
      expect(uiStore.isLoading).toBe(false);
      expect(uiStore.loadingStates).toHaveLength(0);
    });

    it('應該能夠清空所有載入狀態', () => {
      const uiStore = useUIStore();
      
      uiStore.startLoading('載入 1');
      uiStore.startLoading('載入 2');
      uiStore.startLoading('載入 3');
      expect(uiStore.loadingStates).toHaveLength(3);
      
      uiStore.clearLoading();
      expect(uiStore.isLoading).toBe(false);
      expect(uiStore.loadingStates).toHaveLength(0);
    });

    it('應該正確計算主要載入狀態', () => {
      const uiStore = useUIStore();
      
      const id1 = uiStore.startLoading('載入 1');
      const id2 = uiStore.startLoading('載入 2');
      const id3 = uiStore.startLoading('載入 3');
      
      // 主要載入狀態應該是最新的一個
      expect(uiStore.primaryLoadingState?.id).toBe(id3);
      
      // 移除最新的，主要載入狀態應該變成第二個
      uiStore.stopLoading(id3);
      expect(uiStore.primaryLoadingState?.id).toBe(id2);
      
      // 移除第二個，主要載入狀態應該變成第一個
      uiStore.stopLoading(id2);
      expect(uiStore.primaryLoadingState?.id).toBe(id1);
    });
  });

  describe('錯誤處理', () => {
    it('應該能夠處理 API 錯誤', () => {
      const uiStore = useUIStore();
      
      const error = {
        response: {
          data: {
            message: 'API 錯誤訊息'
          }
        }
      };
      
      uiStore.handleApiError(error, '儲存檔案');
      
      expect(uiStore.notifications).toHaveLength(1);
      expect(uiStore.notifications[0]).toMatchObject({
        type: 'error',
        title: '儲存檔案失敗',
        message: 'API 錯誤訊息',
      });
    });

    it('應該能夠處理網路錯誤', () => {
      const uiStore = useUIStore();
      
      uiStore.handleNetworkError(new Error('網路連線失敗'));
      
      expect(uiStore.notifications).toHaveLength(1);
      expect(uiStore.notifications[0]).toMatchObject({
        type: 'error',
        title: '網路連線錯誤',
        message: '請檢查您的網路連線並重試',
      });
    });

    it('應該處理不同類型的錯誤', () => {
      const uiStore = useUIStore();
      
      // 字串錯誤
      uiStore.handleApiError('字串錯誤');
      expect(uiStore.notifications[0].message).toBe('字串錯誤');
      
      // Error 物件
      uiStore.handleApiError(new Error('Error 物件錯誤'));
      expect(uiStore.notifications[1].message).toBe('Error 物件錯誤');
      
      // 未知錯誤
      uiStore.handleApiError({});
      expect(uiStore.notifications[2].message).toBe('請稍後再試');
    });
  });
});