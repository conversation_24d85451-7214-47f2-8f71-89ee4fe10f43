import { Page, Locator } from '@playwright/test'
import { TestUtils } from '../utils/test-utils'

/**
 * 預覽頁面物件模型
 */
export class PreviewPage {
  private testUtils: TestUtils

  // 選擇器
  private readonly previewContainer = '[data-testid="preview-container"]'
  private readonly previewFrame = '[data-testid="preview-frame"]'
  private readonly previewError = '[data-testid="preview-error"]'
  private readonly previewLoading = '[data-testid="preview-loading"]'
  private readonly refreshButton = '[data-testid="refresh-preview-button"]'
  private readonly fullscreenButton = '[data-testid="fullscreen-button"]'
  private readonly deviceSelector = '[data-testid="device-selector"]'
  private readonly zoomControls = '[data-testid="zoom-controls"]'
  private readonly errorDetails = '[data-testid="error-details"]'
  private readonly compileStatus = '[data-testid="compile-status"]'

  constructor(private page: Page) {
    this.testUtils = new TestUtils(page)
  }

  /**
   * 導航到預覽頁面
   */
  async goto(projectId?: string) {
    const url = projectId ? `/project/${projectId}/preview` : '/preview'
    await this.page.goto(url)
    await this.waitForPageLoad()
  }

  /**
   * 等待頁面載入完成
   */
  async waitForPageLoad() {
    await this.page.waitForSelector(this.previewContainer, { state: 'visible' })
  }

  /**
   * 等待預覽載入完成
   */
  async waitForPreviewLoad(timeout = 10000) {
    // 等待載入指示器消失
    await this.page.waitForSelector(this.previewLoading, { 
      state: 'hidden', 
      timeout 
    }).catch(() => {
      // 忽略如果沒有載入指示器
    })

    // 等待預覽框架載入
    await this.page.waitForSelector(this.previewFrame, { 
      state: 'visible', 
      timeout 
    })
  }

  /**
   * 檢查預覽是否有錯誤
   */
  async hasPreviewError(): Promise<boolean> {
    return await this.testUtils.isVisible(this.previewError)
  }

  /**
   * 獲取預覽錯誤訊息
   */
  async getPreviewError(): Promise<string> {
    const errorElement = this.page.locator(this.previewError)
    return await errorElement.textContent() || ''
  }

  /**
   * 獲取詳細錯誤資訊
   */
  async getErrorDetails(): Promise<string> {
    const detailsElement = this.page.locator(this.errorDetails)
    return await detailsElement.textContent() || ''
  }

  /**
   * 重新整理預覽
   */
  async refreshPreview() {
    await this.testUtils.clickWhenVisible(this.refreshButton)
    await this.waitForPreviewLoad()
  }

  /**
   * 切換全螢幕模式
   */
  async toggleFullscreen() {
    await this.testUtils.clickWhenVisible(this.fullscreenButton)
  }

  /**
   * 選擇裝置預覽模式
   */
  async selectDevice(device: 'desktop' | 'tablet' | 'mobile') {
    await this.testUtils.clickWhenVisible(this.deviceSelector)
    
    const deviceOption = `[data-testid="device-option"][data-device="${device}"]`
    await this.testUtils.clickWhenVisible(deviceOption)
    
    await this.waitForPreviewLoad()
  }

  /**
   * 調整縮放比例
   */
  async setZoom(zoom: number) {
    const zoomInput = `${this.zoomControls} input[type="range"]`
    await this.page.fill(zoomInput, zoom.toString())
  }

  /**
   * 獲取預覽框架內容
   */
  async getPreviewContent(): Promise<string> {
    const frame = this.page.frameLocator(this.previewFrame)
    const body = frame.locator('body')
    return await body.innerHTML()
  }

  /**
   * 在預覽中點擊元素
   */
  async clickInPreview(selector: string) {
    const frame = this.page.frameLocator(this.previewFrame)
    const element = frame.locator(selector)
    await element.click()
  }

  /**
   * 在預覽中輸入文字
   */
  async typeInPreview(selector: string, text: string) {
    const frame = this.page.frameLocator(this.previewFrame)
    const element = frame.locator(selector)
    await element.fill(text)
  }

  /**
   * 檢查預覽中是否存在元素
   */
  async hasElementInPreview(selector: string): Promise<boolean> {
    try {
      const frame = this.page.frameLocator(this.previewFrame)
      const element = frame.locator(selector)
      await element.waitFor({ state: 'visible', timeout: 1000 })
      return true
    } catch {
      return false
    }
  }

  /**
   * 獲取預覽中元素的文字內容
   */
  async getTextFromPreview(selector: string): Promise<string> {
    const frame = this.page.frameLocator(this.previewFrame)
    const element = frame.locator(selector)
    return await element.textContent() || ''
  }

  /**
   * 獲取預覽中元素的屬性
   */
  async getAttributeFromPreview(selector: string, attribute: string): Promise<string> {
    const frame = this.page.frameLocator(this.previewFrame)
    const element = frame.locator(selector)
    return await element.getAttribute(attribute) || ''
  }

  /**
   * 檢查 CSS 樣式是否正確載入
   */
  async checkStylesLoaded(): Promise<boolean> {
    const frame = this.page.frameLocator(this.previewFrame)
    
    // 檢查是否有 Tailwind CSS 類別生效
    const hasStyles = await frame.evaluate(() => {
      const element = document.querySelector('[class*="bg-"]') || 
                     document.querySelector('[class*="text-"]') ||
                     document.querySelector('[class*="p-"]')
      
      if (!element) return false
      
      const styles = window.getComputedStyle(element)
      return styles.backgroundColor !== 'rgba(0, 0, 0, 0)' || 
             styles.color !== 'rgb(0, 0, 0)' ||
             styles.padding !== '0px'
    })
    
    return hasStyles
  }

  /**
   * 獲取編譯狀態
   */
  async getCompileStatus(): Promise<string> {
    const statusElement = this.page.locator(this.compileStatus)
    return await statusElement.getAttribute('data-status') || 'unknown'
  }

  /**
   * 等待編譯完成
   */
  async waitForCompileComplete(timeout = 15000) {
    await this.page.waitForFunction(
      () => {
        const status = document.querySelector('[data-testid="compile-status"]')
        return status?.getAttribute('data-status') === 'success'
      },
      {},
      { timeout }
    )
  }

  /**
   * 檢查熱重載是否工作
   */
  async testHotReload(originalContent: string, newContent: string): Promise<boolean> {
    // 獲取初始內容
    const initialContent = await this.getPreviewContent()
    
    // 這個方法需要與編輯器頁面配合使用
    // 在實際測試中，會先修改程式碼，然後檢查預覽是否更新
    
    // 等待內容更新
    await this.page.waitForFunction(
      (original) => {
        const frame = document.querySelector('[data-testid="preview-frame"]') as HTMLIFrameElement
        if (!frame?.contentDocument) return false
        
        const currentContent = frame.contentDocument.body.innerHTML
        return currentContent !== original
      },
      initialContent,
      { timeout: 10000 }
    )
    
    return true
  }

  /**
   * 截圖預覽內容
   */
  async screenshotPreview(): Promise<Buffer> {
    const frame = this.page.frameLocator(this.previewFrame)
    return await frame.locator('body').screenshot()
  }

  /**
   * 檢查響應式設計
   */
  async testResponsiveDesign() {
    const results = {
      desktop: false,
      tablet: false,
      mobile: false
    }

    // 測試桌面版
    await this.selectDevice('desktop')
    await this.waitForPreviewLoad()
    results.desktop = await this.hasElementInPreview('body')

    // 測試平板版
    await this.selectDevice('tablet')
    await this.waitForPreviewLoad()
    results.tablet = await this.hasElementInPreview('body')

    // 測試手機版
    await this.selectDevice('mobile')
    await this.waitForPreviewLoad()
    results.mobile = await this.hasElementInPreview('body')

    return results
  }

  /**
   * 測試互動功能
   */
  async testInteractivity(buttonSelector: string, expectedResult: string): Promise<boolean> {
    // 點擊按鈕
    await this.clickInPreview(buttonSelector)
    
    // 等待結果出現
    await this.page.waitForTimeout(1000)
    
    // 檢查結果
    const result = await this.getTextFromPreview('[data-testid="result"]')
    return result.includes(expectedResult)
  }

  /**
   * 檢查動畫效果
   */
  async checkAnimations(): Promise<boolean> {
    const frame = this.page.frameLocator(this.previewFrame)
    
    // 檢查是否有 CSS 動畫或過渡效果
    const hasAnimations = await frame.evaluate(() => {
      const elements = document.querySelectorAll('*')
      
      for (const element of elements) {
        const styles = window.getComputedStyle(element)
        if (styles.animationName !== 'none' || 
            styles.transitionProperty !== 'none') {
          return true
        }
      }
      
      return false
    })
    
    return hasAnimations
  }
}