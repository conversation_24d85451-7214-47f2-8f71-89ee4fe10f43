import { Page, Locator } from '@playwright/test'
import { TestUtils } from '../utils/test-utils'

/**
 * 聊天頁面物件模型
 */
export class ChatPage {
  private testUtils: TestUtils

  // 選擇器
  private readonly chatContainer = '[data-testid="chat-container"]'
  private readonly messageInput = '[data-testid="message-input"]'
  private readonly sendButton = '[data-testid="send-button"]'
  private readonly messageList = '[data-testid="message-list"]'
  private readonly userMessage = '[data-testid="user-message"]'
  private readonly aiMessage = '[data-testid="ai-message"]'
  private readonly loadingIndicator = '[data-testid="loading-indicator"]'
  private readonly connectionStatus = '[data-testid="connection-status"]'
  private readonly errorMessage = '[data-testid="error-message"]'
  private readonly retryButton = '[data-testid="retry-button"]'

  constructor(private page: Page) {
    this.testUtils = new TestUtils(page)
  }

  /**
   * 導航到聊天頁面
   */
  async goto() {
    await this.page.goto('/chat')
    await this.waitForPageLoad()
  }

  /**
   * 等待頁面載入完成
   */
  async waitForPageLoad() {
    await this.page.waitForSelector(this.chatContainer, { state: 'visible' })
    await this.testUtils.waitForWebSocketConnection()
  }

  /**
   * 發送訊息
   */
  async sendMessage(message: string) {
    await this.testUtils.typeWhenVisible(this.messageInput, message)
    await this.testUtils.clickWhenVisible(this.sendButton)
  }

  /**
   * 等待 AI 回應
   */
  async waitForAiResponse(timeout = 30000) {
    await this.page.waitForSelector(this.aiMessage, { 
      state: 'visible', 
      timeout 
    })
    
    // 等待載入指示器消失
    await this.page.waitForSelector(this.loadingIndicator, { 
      state: 'hidden', 
      timeout: 5000 
    }).catch(() => {
      // 忽略如果沒有載入指示器
    })
  }

  /**
   * 獲取最後一條使用者訊息
   */
  async getLastUserMessage(): Promise<string> {
    const messages = this.page.locator(this.userMessage)
    const lastMessage = messages.last()
    return await lastMessage.textContent() || ''
  }

  /**
   * 獲取最後一條 AI 訊息
   */
  async getLastAiMessage(): Promise<string> {
    const messages = this.page.locator(this.aiMessage)
    const lastMessage = messages.last()
    return await lastMessage.textContent() || ''
  }

  /**
   * 獲取所有訊息
   */
  async getAllMessages(): Promise<Array<{ type: 'user' | 'ai', content: string }>> {
    const messages = []
    
    const userMessages = await this.page.locator(this.userMessage).all()
    for (const msg of userMessages) {
      const content = await msg.textContent()
      if (content) {
        messages.push({ type: 'user' as const, content })
      }
    }

    const aiMessages = await this.page.locator(this.aiMessage).all()
    for (const msg of aiMessages) {
      const content = await msg.textContent()
      if (content) {
        messages.push({ type: 'ai' as const, content })
      }
    }

    return messages.sort((a, b) => {
      // 這裡需要根據實際的時間戳或順序來排序
      return 0
    })
  }

  /**
   * 檢查連線狀態
   */
  async getConnectionStatus(): Promise<string> {
    const statusElement = this.page.locator(this.connectionStatus)
    return await statusElement.getAttribute('data-status') || 'unknown'
  }

  /**
   * 檢查是否顯示載入指示器
   */
  async isLoading(): Promise<boolean> {
    return await this.testUtils.isVisible(this.loadingIndicator)
  }

  /**
   * 檢查是否有錯誤訊息
   */
  async hasError(): Promise<boolean> {
    return await this.testUtils.isVisible(this.errorMessage)
  }

  /**
   * 獲取錯誤訊息
   */
  async getErrorMessage(): Promise<string> {
    const errorElement = this.page.locator(this.errorMessage)
    return await errorElement.textContent() || ''
  }

  /**
   * 點擊重試按鈕
   */
  async clickRetry() {
    await this.testUtils.clickWhenVisible(this.retryButton)
  }

  /**
   * 清空聊天記錄
   */
  async clearChat() {
    // 假設有清空按鈕
    const clearButton = '[data-testid="clear-chat-button"]'
    if (await this.testUtils.isVisible(clearButton)) {
      await this.testUtils.clickWhenVisible(clearButton)
    }
  }

  /**
   * 檢查訊息是否包含程式碼
   */
  async messageContainsCode(messageIndex: number): Promise<boolean> {
    const codeBlock = `${this.aiMessage}:nth-child(${messageIndex + 1}) code`
    return await this.testUtils.isVisible(codeBlock)
  }

  /**
   * 獲取訊息中的程式碼
   */
  async getCodeFromMessage(messageIndex: number): Promise<string> {
    const codeBlock = `${this.aiMessage}:nth-child(${messageIndex + 1}) code`
    const codeElement = this.page.locator(codeBlock)
    return await codeElement.textContent() || ''
  }

  /**
   * 模擬網路斷線
   */
  async simulateNetworkDisconnection() {
    await this.page.setOfflineMode(true)
  }

  /**
   * 恢復網路連線
   */
  async restoreNetworkConnection() {
    await this.page.setOfflineMode(false)
  }

  /**
   * 等待重新連線
   */
  async waitForReconnection() {
    await this.testUtils.waitForWebSocketConnection()
  }
}