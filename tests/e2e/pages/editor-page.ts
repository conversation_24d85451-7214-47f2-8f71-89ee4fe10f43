import { Page, Locator } from '@playwright/test'
import { TestUtils } from '../utils/test-utils'

/**
 * 程式碼編輯器頁面物件模型
 */
export class EditorPage {
  private testUtils: TestUtils

  // 選擇器
  private readonly editorContainer = '[data-testid="editor-container"]'
  private readonly monacoEditor = '.monaco-editor'
  private readonly fileTree = '[data-testid="file-tree"]'
  private readonly fileNode = '[data-testid="file-node"]'
  private readonly createFileButton = '[data-testid="create-file-button"]'
  private readonly createFolderButton = '[data-testid="create-folder-button"]'
  private readonly saveButton = '[data-testid="save-button"]'
  private readonly formatButton = '[data-testid="format-button"]'
  private readonly fileContextMenu = '[data-testid="file-context-menu"]'
  private readonly renameInput = '[data-testid="rename-input"]'
  private readonly deleteConfirmButton = '[data-testid="delete-confirm-button"]'
  private readonly modifiedIndicator = '[data-testid="modified-indicator"]'
  private readonly errorMarker = '.monaco-editor .squiggly-error'
  private readonly suggestionWidget = '.monaco-editor .suggest-widget'

  constructor(private page: Page) {
    this.testUtils = new TestUtils(page)
  }

  /**
   * 導航到編輯器頁面
   */
  async goto(projectId?: string) {
    const url = projectId ? `/project/${projectId}` : '/editor'
    await this.page.goto(url)
    await this.waitForPageLoad()
  }

  /**
   * 等待頁面載入完成
   */
  async waitForPageLoad() {
    await this.page.waitForSelector(this.editorContainer, { state: 'visible' })
    await this.page.waitForSelector(this.monacoEditor, { state: 'visible' })
  }

  /**
   * 在編輯器中輸入程式碼
   */
  async typeInEditor(code: string) {
    // 點擊編輯器以獲得焦點
    await this.page.click(this.monacoEditor)
    
    // 清空編輯器
    await this.page.keyboard.press('Control+A')
    
    // 輸入程式碼
    await this.page.keyboard.type(code)
  }

  /**
   * 獲取編輯器內容
   */
  async getEditorContent(): Promise<string> {
    return await this.page.evaluate(() => {
      const editor = (window as any).monaco?.editor?.getModels()?.[0]
      return editor?.getValue() || ''
    })
  }

  /**
   * 儲存檔案
   */
  async saveFile() {
    await this.testUtils.clickWhenVisible(this.saveButton)
    // 或使用快捷鍵
    // await this.page.keyboard.press('Control+S')
  }

  /**
   * 格式化程式碼
   */
  async formatCode() {
    await this.testUtils.clickWhenVisible(this.formatButton)
    // 或使用快捷鍵
    // await this.page.keyboard.press('Shift+Alt+F')
  }

  /**
   * 點擊檔案樹中的檔案
   */
  async clickFile(fileName: string) {
    const fileSelector = `${this.fileNode}[data-file-name="${fileName}"]`
    await this.testUtils.clickWhenVisible(fileSelector)
  }

  /**
   * 右鍵點擊檔案
   */
  async rightClickFile(fileName: string) {
    const fileSelector = `${this.fileNode}[data-file-name="${fileName}"]`
    await this.page.click(fileSelector, { button: 'right' })
    await this.page.waitForSelector(this.fileContextMenu, { state: 'visible' })
  }

  /**
   * 建立新檔案
   */
  async createFile(fileName: string, content?: string) {
    await this.testUtils.clickWhenVisible(this.createFileButton)
    
    // 輸入檔案名稱
    const nameInput = '[data-testid="file-name-input"]'
    await this.testUtils.typeWhenVisible(nameInput, fileName)
    
    // 確認建立
    const confirmButton = '[data-testid="confirm-create-button"]'
    await this.testUtils.clickWhenVisible(confirmButton)
    
    // 如果有內容，輸入內容
    if (content) {
      await this.typeInEditor(content)
      await this.saveFile()
    }
  }

  /**
   * 建立新資料夾
   */
  async createFolder(folderName: string) {
    await this.testUtils.clickWhenVisible(this.createFolderButton)
    
    // 輸入資料夾名稱
    const nameInput = '[data-testid="folder-name-input"]'
    await this.testUtils.typeWhenVisible(nameInput, folderName)
    
    // 確認建立
    const confirmButton = '[data-testid="confirm-create-button"]'
    await this.testUtils.clickWhenVisible(confirmButton)
  }

  /**
   * 重新命名檔案
   */
  async renameFile(oldName: string, newName: string) {
    await this.rightClickFile(oldName)
    
    // 點擊重新命名選項
    const renameOption = '[data-testid="rename-option"]'
    await this.testUtils.clickWhenVisible(renameOption)
    
    // 輸入新名稱
    await this.testUtils.typeWhenVisible(this.renameInput, newName)
    
    // 按 Enter 確認
    await this.page.keyboard.press('Enter')
  }

  /**
   * 刪除檔案
   */
  async deleteFile(fileName: string) {
    await this.rightClickFile(fileName)
    
    // 點擊刪除選項
    const deleteOption = '[data-testid="delete-option"]'
    await this.testUtils.clickWhenVisible(deleteOption)
    
    // 確認刪除
    await this.testUtils.clickWhenVisible(this.deleteConfirmButton)
  }

  /**
   * 檢查檔案是否有未儲存的變更
   */
  async hasUnsavedChanges(fileName: string): Promise<boolean> {
    const fileSelector = `${this.fileNode}[data-file-name="${fileName}"]`
    const indicator = `${fileSelector} ${this.modifiedIndicator}`
    return await this.testUtils.isVisible(indicator)
  }

  /**
   * 檢查是否有語法錯誤
   */
  async hasSyntaxErrors(): Promise<boolean> {
    return await this.testUtils.isVisible(this.errorMarker)
  }

  /**
   * 獲取語法錯誤數量
   */
  async getSyntaxErrorCount(): Promise<number> {
    const errors = await this.page.locator(this.errorMarker).count()
    return errors
  }

  /**
   * 觸發自動完成
   */
  async triggerAutoComplete() {
    await this.page.keyboard.press('Control+Space')
    await this.page.waitForSelector(this.suggestionWidget, { state: 'visible' })
  }

  /**
   * 選擇自動完成建議
   */
  async selectAutoCompleteSuggestion(index: number = 0) {
    const suggestions = `${this.suggestionWidget} .monaco-list-row`
    const suggestion = this.page.locator(suggestions).nth(index)
    await suggestion.click()
  }

  /**
   * 獲取檔案樹中的所有檔案
   */
  async getFileList(): Promise<string[]> {
    const files = await this.page.locator(this.fileNode).all()
    const fileNames = []
    
    for (const file of files) {
      const name = await file.getAttribute('data-file-name')
      if (name) {
        fileNames.push(name)
      }
    }
    
    return fileNames
  }

  /**
   * 展開資料夾
   */
  async expandFolder(folderName: string) {
    const folderSelector = `${this.fileNode}[data-file-name="${folderName}"][data-file-type="folder"]`
    const expandButton = `${folderSelector} [data-testid="expand-button"]`
    
    if (await this.testUtils.isVisible(expandButton)) {
      await this.testUtils.clickWhenVisible(expandButton)
    }
  }

  /**
   * 摺疊資料夾
   */
  async collapseFolder(folderName: string) {
    const folderSelector = `${this.fileNode}[data-file-name="${folderName}"][data-file-type="folder"]`
    const collapseButton = `${folderSelector} [data-testid="collapse-button"]`
    
    if (await this.testUtils.isVisible(collapseButton)) {
      await this.testUtils.clickWhenVisible(collapseButton)
    }
  }

  /**
   * 拖拽檔案到資料夾
   */
  async dragFileToFolder(fileName: string, folderName: string) {
    const fileSelector = `${this.fileNode}[data-file-name="${fileName}"]`
    const folderSelector = `${this.fileNode}[data-file-name="${folderName}"][data-file-type="folder"]`
    
    await this.testUtils.dragAndDrop(fileSelector, folderSelector)
  }

  /**
   * 搜尋檔案
   */
  async searchFiles(searchTerm: string) {
    const searchInput = '[data-testid="file-search-input"]'
    await this.testUtils.typeWhenVisible(searchInput, searchTerm)
  }

  /**
   * 切換到特定分頁
   */
  async switchToTab(fileName: string) {
    const tabSelector = `[data-testid="editor-tab"][data-file-name="${fileName}"]`
    await this.testUtils.clickWhenVisible(tabSelector)
  }

  /**
   * 關閉分頁
   */
  async closeTab(fileName: string) {
    const closeButton = `[data-testid="editor-tab"][data-file-name="${fileName}"] [data-testid="close-tab-button"]`
    await this.testUtils.clickWhenVisible(closeButton)
  }

  /**
   * 獲取開啟的分頁列表
   */
  async getOpenTabs(): Promise<string[]> {
    const tabs = await this.page.locator('[data-testid="editor-tab"]').all()
    const tabNames = []
    
    for (const tab of tabs) {
      const name = await tab.getAttribute('data-file-name')
      if (name) {
        tabNames.push(name)
      }
    }
    
    return tabNames
  }
}