import { FullConfig } from '@playwright/test'
import { DatabaseTestUtils } from './utils/test-utils'

async function globalTeardown(config: FullConfig) {
  console.log('🧹 開始 E2E 測試全域清理...')

  try {
    // 清理測試資料
    console.log('🗄️ 清理測試資料...')
    await DatabaseTestUtils.cleanupTestData()
    console.log('✅ 測試資料已清理')

    // 清理暫存檔案
    console.log('📁 清理暫存檔案...')
    // 這裡可以添加清理暫存檔案的邏輯
    console.log('✅ 暫存檔案已清理')

    console.log('🎉 E2E 測試環境清理完成')

  } catch (error) {
    console.error('❌ E2E 測試環境清理失敗:', error)
    // 不拋出錯誤，避免影響測試結果報告
  }
}

export default globalTeardown