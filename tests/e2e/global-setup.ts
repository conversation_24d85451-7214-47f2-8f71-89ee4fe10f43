import { chromium, FullConfig } from '@playwright/test'
import { DatabaseTestUtils } from './utils/test-utils'

async function globalSetup(config: FullConfig) {
  console.log('🚀 開始 E2E 測試全域設定...')

  // 啟動瀏覽器進行初始化檢查
  const browser = await chromium.launch()
  const page = await browser.newPage()

  try {
    // 等待前端服務啟動
    console.log('⏳ 等待前端服務啟動...')
    let frontendReady = false
    let attempts = 0
    const maxAttempts = 30

    while (!frontendReady && attempts < maxAttempts) {
      try {
        const response = await page.goto('http://localhost:5173', { 
          waitUntil: 'networkidle',
          timeout: 5000 
        })
        if (response?.ok()) {
          frontendReady = true
          console.log('✅ 前端服務已就緒')
        }
      } catch (error) {
        attempts++
        console.log(`⏳ 前端服務尚未就緒，重試 ${attempts}/${maxAttempts}...`)
        await page.waitForTimeout(2000)
      }
    }

    if (!frontendReady) {
      throw new Error('前端服務啟動失敗')
    }

    // 等待後端服務啟動
    console.log('⏳ 等待後端服務啟動...')
    let backendReady = false
    attempts = 0

    while (!backendReady && attempts < maxAttempts) {
      try {
        const response = await page.goto('http://localhost:3001/health', { 
          timeout: 5000 
        })
        if (response?.ok()) {
          backendReady = true
          console.log('✅ 後端服務已就緒')
        }
      } catch (error) {
        attempts++
        console.log(`⏳ 後端服務尚未就緒，重試 ${attempts}/${maxAttempts}...`)
        await page.waitForTimeout(2000)
      }
    }

    if (!backendReady) {
      throw new Error('後端服務啟動失敗')
    }

    // 初始化測試資料庫
    console.log('🗄️ 初始化測試資料庫...')
    await DatabaseTestUtils.cleanupTestData()
    console.log('✅ 測試資料庫已初始化')

    // 檢查 AI 服務連線（如果有配置）
    console.log('🤖 檢查 AI 服務連線...')
    try {
      const aiResponse = await page.evaluate(async () => {
        const response = await fetch('http://localhost:3001/api/ai/providers')
        return response.ok
      })
      
      if (aiResponse) {
        console.log('✅ AI 服務連線正常')
      } else {
        console.log('⚠️ AI 服務連線異常，將使用模擬模式')
      }
    } catch (error) {
      console.log('⚠️ AI 服務檢查失敗，將使用模擬模式')
    }

    console.log('🎉 E2E 測試環境設定完成')

  } catch (error) {
    console.error('❌ E2E 測試環境設定失敗:', error)
    throw error
  } finally {
    await browser.close()
  }
}

export default globalSetup