import { Page, expect } from '@playwright/test'

/**
 * 測試工具函數
 */
export class TestUtils {
  constructor(private page: Page) {}

  /**
   * 等待元素可見並點擊
   */
  async clickWhenVisible(selector: string, timeout = 5000) {
    await this.page.waitForSelector(selector, { state: 'visible', timeout })
    await this.page.click(selector)
  }

  /**
   * 等待元素可見並輸入文字
   */
  async typeWhenVisible(selector: string, text: string, timeout = 5000) {
    await this.page.waitForSelector(selector, { state: 'visible', timeout })
    await this.page.fill(selector, text)
  }

  /**
   * 等待載入完成
   */
  async waitForLoadingComplete() {
    // 等待載入指示器消失
    await this.page.waitForSelector('[data-testid="loading"]', { 
      state: 'hidden', 
      timeout: 10000 
    }).catch(() => {
      // 如果沒有載入指示器，忽略錯誤
    })
  }

  /**
   * 等待 WebSocket 連線建立
   */
  async waitForWebSocketConnection() {
    // 等待連線狀態指示器顯示已連線
    await this.page.waitForSelector('[data-testid="connection-status"][data-status="connected"]', {
      timeout: 10000
    })
  }

  /**
   * 截圖並附加到測試報告
   */
  async takeScreenshot(name: string) {
    const screenshot = await this.page.screenshot({ fullPage: true })
    return screenshot
  }

  /**
   * 等待 API 回應
   */
  async waitForApiResponse(urlPattern: string | RegExp, timeout = 10000) {
    return await this.page.waitForResponse(urlPattern, { timeout })
  }

  /**
   * 模擬網路延遲
   */
  async simulateSlowNetwork() {
    await this.page.route('**/*', async route => {
      await new Promise(resolve => setTimeout(resolve, 1000))
      await route.continue()
    })
  }

  /**
   * 檢查控制台錯誤
   */
  async checkConsoleErrors() {
    const errors: string[] = []
    
    this.page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text())
      }
    })

    return errors
  }

  /**
   * 等待元素包含特定文字
   */
  async waitForTextContent(selector: string, text: string, timeout = 5000) {
    await this.page.waitForFunction(
      ({ selector, text }) => {
        const element = document.querySelector(selector)
        return element && element.textContent?.includes(text)
      },
      { selector, text },
      { timeout }
    )
  }

  /**
   * 滾動到元素
   */
  async scrollToElement(selector: string) {
    await this.page.locator(selector).scrollIntoViewIfNeeded()
  }

  /**
   * 等待檔案下載
   */
  async waitForDownload() {
    const downloadPromise = this.page.waitForEvent('download')
    return downloadPromise
  }

  /**
   * 模擬拖拽操作
   */
  async dragAndDrop(sourceSelector: string, targetSelector: string) {
    await this.page.dragAndDrop(sourceSelector, targetSelector)
  }

  /**
   * 檢查元素是否可見
   */
  async isVisible(selector: string): Promise<boolean> {
    try {
      await this.page.waitForSelector(selector, { state: 'visible', timeout: 1000 })
      return true
    } catch {
      return false
    }
  }

  /**
   * 等待動畫完成
   */
  async waitForAnimations() {
    await this.page.waitForTimeout(500) // 等待 CSS 動畫
  }
}

/**
 * 效能測試工具
 */
export class PerformanceUtils {
  constructor(private page: Page) {}

  /**
   * 測量頁面載入時間
   */
  async measurePageLoadTime(): Promise<number> {
    const startTime = Date.now()
    await this.page.waitForLoadState('networkidle')
    return Date.now() - startTime
  }

  /**
   * 測量首次內容繪製時間 (FCP)
   */
  async measureFCP(): Promise<number> {
    const fcp = await this.page.evaluate(() => {
      return new Promise<number>((resolve) => {
        new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (entry.name === 'first-contentful-paint') {
              resolve(entry.startTime)
            }
          }
        }).observe({ entryTypes: ['paint'] })
      })
    })
    return fcp
  }

  /**
   * 測量最大內容繪製時間 (LCP)
   */
  async measureLCP(): Promise<number> {
    const lcp = await this.page.evaluate(() => {
      return new Promise<number>((resolve) => {
        new PerformanceObserver((list) => {
          const entries = list.getEntries()
          const lastEntry = entries[entries.length - 1]
          resolve(lastEntry.startTime)
        }).observe({ entryTypes: ['largest-contentful-paint'] })
      })
    })
    return lcp
  }

  /**
   * 獲取記憶體使用情況
   */
  async getMemoryUsage() {
    const metrics = await this.page.evaluate(() => {
      return (performance as any).memory
    })
    return metrics
  }
}

/**
 * 資料庫測試工具
 */
export class DatabaseTestUtils {
  /**
   * 清理測試資料
   */
  static async cleanupTestData() {
    // 這裡可以呼叫後端 API 來清理測試資料
    // 或者直接操作測試資料庫
  }

  /**
   * 建立測試專案
   */
  static async createTestProject(name: string) {
    // 透過 API 建立測試專案
    const response = await fetch('http://localhost:3001/api/projects', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ name, description: `Test project: ${name}` })
    })
    return response.json()
  }

  /**
   * 刪除測試專案
   */
  static async deleteTestProject(projectId: string) {
    await fetch(`http://localhost:3001/api/projects/${projectId}`, {
      method: 'DELETE'
    })
  }
}