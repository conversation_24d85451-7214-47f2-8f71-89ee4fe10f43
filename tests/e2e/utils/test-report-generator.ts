import { TestResult, TestCase } from '@playwright/test/reporter'
import fs from 'fs'
import path from 'path'

/**
 * E2E 測試報告生成器
 */
export class E2ETestReportGenerator {
  private results: TestResult[] = []
  private startTime: Date = new Date()
  private endTime?: Date

  /**
   * 添加測試結果
   */
  addResult(result: TestResult) {
    this.results.push(result)
  }

  /**
   * 設定測試結束時間
   */
  setEndTime() {
    this.endTime = new Date()
  }

  /**
   * 生成 HTML 報告
   */
  generateHTMLReport(): string {
    const totalTests = this.results.length
    const passedTests = this.results.filter(r => r.status === 'passed').length
    const failedTests = this.results.filter(r => r.status === 'failed').length
    const skippedTests = this.results.filter(r => r.status === 'skipped').length
    const duration = this.endTime ? this.endTime.getTime() - this.startTime.getTime() : 0

    const html = `
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UIGen Vue E2E 測試報告</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        .summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            padding: 30px;
            background: #f8f9fa;
        }
        .summary-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .summary-card h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .summary-card .number {
            font-size: 2em;
            font-weight: bold;
            margin: 10px 0;
        }
        .passed { color: #28a745; }
        .failed { color: #dc3545; }
        .skipped { color: #ffc107; }
        .total { color: #007bff; }
        .results {
            padding: 30px;
        }
        .test-group {
            margin-bottom: 30px;
        }
        .test-group h2 {
            color: #333;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
        }
        .test-case {
            display: flex;
            align-items: center;
            padding: 15px;
            margin: 10px 0;
            border-radius: 6px;
            border-left: 4px solid;
        }
        .test-case.passed {
            background: #f8fff9;
            border-left-color: #28a745;
        }
        .test-case.failed {
            background: #fff8f8;
            border-left-color: #dc3545;
        }
        .test-case.skipped {
            background: #fffdf5;
            border-left-color: #ffc107;
        }
        .test-status {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-right: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 12px;
        }
        .test-status.passed { background: #28a745; }
        .test-status.failed { background: #dc3545; }
        .test-status.skipped { background: #ffc107; }
        .test-info {
            flex: 1;
        }
        .test-title {
            font-weight: 600;
            margin-bottom: 5px;
        }
        .test-duration {
            color: #666;
            font-size: 0.9em;
        }
        .error-details {
            background: #f8f8f8;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
            font-family: monospace;
            font-size: 0.9em;
            white-space: pre-wrap;
        }
        .footer {
            background: #f8f9fa;
            padding: 20px;
            text-align: center;
            color: #666;
            border-top: 1px solid #eee;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>UIGen Vue E2E 測試報告</h1>
            <p>生成時間: ${new Date().toLocaleString('zh-TW')}</p>
        </div>
        
        <div class="summary">
            <div class="summary-card">
                <h3>總測試數</h3>
                <div class="number total">${totalTests}</div>
            </div>
            <div class="summary-card">
                <h3>通過</h3>
                <div class="number passed">${passedTests}</div>
            </div>
            <div class="summary-card">
                <h3>失敗</h3>
                <div class="number failed">${failedTests}</div>
            </div>
            <div class="summary-card">
                <h3>跳過</h3>
                <div class="number skipped">${skippedTests}</div>
            </div>
            <div class="summary-card">
                <h3>執行時間</h3>
                <div class="number">${Math.round(duration / 1000)}s</div>
            </div>
            <div class="summary-card">
                <h3>成功率</h3>
                <div class="number passed">${totalTests > 0 ? Math.round((passedTests / totalTests) * 100) : 0}%</div>
            </div>
        </div>
        
        <div class="results">
            ${this.generateTestGroupsHTML()}
        </div>
        
        <div class="footer">
            <p>UIGen Vue 專案 E2E 測試報告 - 由 Playwright 生成</p>
        </div>
    </div>
</body>
</html>`

    return html
  }

  /**
   * 生成測試群組 HTML
   */
  private generateTestGroupsHTML(): string {
    const groups = this.groupTestsByFile()
    
    return Object.entries(groups).map(([file, tests]) => {
      const testCasesHTML = tests.map(result => {
        const status = result.status
        const statusIcon = status === 'passed' ? '✓' : status === 'failed' ? '✗' : '⊘'
        const duration = result.duration ? `${Math.round(result.duration)}ms` : 'N/A'
        
        let errorHTML = ''
        if (status === 'failed' && result.error) {
          errorHTML = `<div class="error-details">${this.escapeHtml(result.error.message || '未知錯誤')}</div>`
        }
        
        return `
          <div class="test-case ${status}">
            <div class="test-status ${status}">${statusIcon}</div>
            <div class="test-info">
              <div class="test-title">${this.escapeHtml(result.title || '未命名測試')}</div>
              <div class="test-duration">執行時間: ${duration}</div>
              ${errorHTML}
            </div>
          </div>
        `
      }).join('')
      
      return `
        <div class="test-group">
          <h2>${this.getFileDisplayName(file)}</h2>
          ${testCasesHTML}
        </div>
      `
    }).join('')
  }

  /**
   * 按檔案分組測試結果
   */
  private groupTestsByFile(): Record<string, TestResult[]> {
    const groups: Record<string, TestResult[]> = {}
    
    this.results.forEach(result => {
      const file = result.title || 'unknown'
      if (!groups[file]) {
        groups[file] = []
      }
      groups[file].push(result)
    })
    
    return groups
  }

  /**
   * 獲取檔案顯示名稱
   */
  private getFileDisplayName(file: string): string {
    const displayNames: Record<string, string> = {
      'create-project.spec.ts': '專案建立工作流程',
      'complete-user-journey.spec.ts': '完整使用者場景',
      'chat-with-ai.spec.ts': 'AI 聊天功能',
      'file-management.spec.ts': '檔案管理功能',
      'responsive-design.spec.ts': '響應式設計',
      'performance.spec.ts': '效能測試'
    }
    
    return displayNames[file] || file
  }

  /**
   * HTML 轉義
   */
  private escapeHtml(text: string): string {
    const div = document.createElement('div')
    div.textContent = text
    return div.innerHTML
  }

  /**
   * 生成 JSON 報告
   */
  generateJSONReport(): string {
    const report = {
      summary: {
        total: this.results.length,
        passed: this.results.filter(r => r.status === 'passed').length,
        failed: this.results.filter(r => r.status === 'failed').length,
        skipped: this.results.filter(r => r.status === 'skipped').length,
        duration: this.endTime ? this.endTime.getTime() - this.startTime.getTime() : 0,
        startTime: this.startTime.toISOString(),
        endTime: this.endTime?.toISOString()
      },
      results: this.results.map(result => ({
        title: result.title,
        status: result.status,
        duration: result.duration,
        error: result.error ? {
          message: result.error.message,
          stack: result.error.stack
        } : null
      }))
    }
    
    return JSON.stringify(report, null, 2)
  }

  /**
   * 儲存報告到檔案
   */
  async saveReports(outputDir: string = 'test-results') {
    // 確保輸出目錄存在
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true })
    }
    
    // 儲存 HTML 報告
    const htmlReport = this.generateHTMLReport()
    fs.writeFileSync(path.join(outputDir, 'e2e-report.html'), htmlReport)
    
    // 儲存 JSON 報告
    const jsonReport = this.generateJSONReport()
    fs.writeFileSync(path.join(outputDir, 'e2e-report.json'), jsonReport)
    
    console.log(`📊 E2E 測試報告已儲存到 ${outputDir}/`)
  }
}

/**
 * 效能測試報告生成器
 */
export class PerformanceReportGenerator {
  private metrics: Array<{
    testName: string
    loadTime: number
    fcp: number
    lcp: number
    memoryUsage: any
    timestamp: Date
  }> = []

  /**
   * 添加效能指標
   */
  addMetrics(testName: string, metrics: {
    loadTime: number
    fcp: number
    lcp: number
    memoryUsage: any
  }) {
    this.metrics.push({
      testName,
      ...metrics,
      timestamp: new Date()
    })
  }

  /**
   * 生成效能報告
   */
  generateReport(): string {
    const avgLoadTime = this.metrics.reduce((sum, m) => sum + m.loadTime, 0) / this.metrics.length
    const avgFCP = this.metrics.reduce((sum, m) => sum + m.fcp, 0) / this.metrics.length
    const avgLCP = this.metrics.reduce((sum, m) => sum + m.lcp, 0) / this.metrics.length

    return `
# UIGen Vue E2E 效能測試報告

## 總覽
- 測試數量: ${this.metrics.length}
- 平均載入時間: ${Math.round(avgLoadTime)}ms
- 平均 FCP: ${Math.round(avgFCP)}ms
- 平均 LCP: ${Math.round(avgLCP)}ms

## 詳細指標

${this.metrics.map(metric => `
### ${metric.testName}
- 載入時間: ${metric.loadTime}ms
- FCP: ${metric.fcp}ms
- LCP: ${metric.lcp}ms
- 記憶體使用: ${Math.round(metric.memoryUsage?.usedJSHeapSize / 1024 / 1024 || 0)}MB
- 測試時間: ${metric.timestamp.toLocaleString('zh-TW')}
`).join('')}

## 效能基準
- ✅ 載入時間 < 3000ms
- ✅ FCP < 1500ms  
- ✅ LCP < 2500ms
- ✅ 記憶體使用 < 100MB
`
  }

  /**
   * 儲存效能報告
   */
  async saveReport(outputDir: string = 'test-results') {
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true })
    }
    
    const report = this.generateReport()
    fs.writeFileSync(path.join(outputDir, 'performance-report.md'), report)
    
    // 也儲存 JSON 格式
    const jsonReport = JSON.stringify(this.metrics, null, 2)
    fs.writeFileSync(path.join(outputDir, 'performance-metrics.json'), jsonReport)
    
    console.log(`⚡ 效能測試報告已儲存到 ${outputDir}/`)
  }
}