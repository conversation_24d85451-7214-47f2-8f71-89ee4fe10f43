import { test, expect } from '@playwright/test'
import { TestUtils, DatabaseTestUtils } from '../utils/test-utils'

test.describe('專案建立工作流程', () => {
  let testUtils: TestUtils

  test.beforeEach(async ({ page }) => {
    testUtils = new TestUtils(page)
    await DatabaseTestUtils.cleanupTestData()
  })

  test.afterEach(async () => {
    await DatabaseTestUtils.cleanupTestData()
  })

  test('應該能夠建立新專案', async ({ page }) => {
    // 導航到首頁
    await page.goto('/')
    
    // 點擊建立新專案按鈕
    await testUtils.clickWhenVisible('[data-testid="create-project-button"]')
    
    // 填寫專案資訊
    const projectName = `測試專案_${Date.now()}`
    await testUtils.typeWhenVisible('[data-testid="project-name-input"]', projectName)
    await testUtils.typeWhenVisible('[data-testid="project-description-input"]', '這是一個測試專案')
    
    // 選擇專案範本
    await testUtils.clickWhenVisible('[data-testid="template-basic"]')
    
    // 確認建立
    await testUtils.clickWhenVisible('[data-testid="confirm-create-project"]')
    
    // 等待專案建立完成
    await testUtils.waitForLoadingComplete()
    
    // 驗證專案已建立並導航到專案頁面
    await expect(page).toHaveURL(/\/project\/[a-zA-Z0-9-]+/)
    
    // 驗證專案名稱顯示正確
    await expect(page.locator('[data-testid="project-title"]')).toContainText(projectName)
    
    // 驗證基本檔案結構已建立
    await expect(page.locator('[data-testid="file-node"][data-file-name="App.vue"]')).toBeVisible()
    await expect(page.locator('[data-testid="file-node"][data-file-name="main.ts"]')).toBeVisible()
  })

  test('應該能夠開啟現有專案', async ({ page }) => {
    // 先建立一個測試專案
    const project = await DatabaseTestUtils.createTestProject('現有專案測試')
    
    // 導航到首頁
    await page.goto('/')
    
    // 在專案列表中找到並點擊專案
    const projectCard = `[data-testid="project-card"][data-project-id="${project.id}"]`
    await testUtils.clickWhenVisible(projectCard)
    
    // 等待專案載入
    await testUtils.waitForLoadingComplete()
    
    // 驗證導航到正確的專案頁面
    await expect(page).toHaveURL(`/project/${project.id}`)
    
    // 驗證專案名稱顯示正確
    await expect(page.locator('[data-testid="project-title"]')).toContainText(project.name)
  })

  test('應該能夠刪除專案', async ({ page }) => {
    // 先建立一個測試專案
    const project = await DatabaseTestUtils.createTestProject('待刪除專案')
    
    // 導航到首頁
    await page.goto('/')
    
    // 右鍵點擊專案卡片
    const projectCard = `[data-testid="project-card"][data-project-id="${project.id}"]`
    await page.click(projectCard, { button: 'right' })
    
    // 點擊刪除選項
    await testUtils.clickWhenVisible('[data-testid="delete-project-option"]')
    
    // 確認刪除
    await testUtils.typeWhenVisible('[data-testid="delete-confirmation-input"]', project.name)
    await testUtils.clickWhenVisible('[data-testid="confirm-delete-project"]')
    
    // 等待刪除完成
    await testUtils.waitForLoadingComplete()
    
    // 驗證專案已從列表中移除
    await expect(page.locator(projectCard)).not.toBeVisible()
  })

  test('應該能夠複製專案', async ({ page }) => {
    // 先建立一個測試專案
    const originalProject = await DatabaseTestUtils.createTestProject('原始專案')
    
    // 導航到首頁
    await page.goto('/')
    
    // 右鍵點擊專案卡片
    const projectCard = `[data-testid="project-card"][data-project-id="${originalProject.id}"]`
    await page.click(projectCard, { button: 'right' })
    
    // 點擊複製選項
    await testUtils.clickWhenVisible('[data-testid="duplicate-project-option"]')
    
    // 輸入新專案名稱
    const newProjectName = `${originalProject.name}_副本`
    await testUtils.typeWhenVisible('[data-testid="duplicate-project-name"]', newProjectName)
    
    // 確認複製
    await testUtils.clickWhenVisible('[data-testid="confirm-duplicate-project"]')
    
    // 等待複製完成
    await testUtils.waitForLoadingComplete()
    
    // 驗證新專案出現在列表中
    await expect(page.locator(`[data-testid="project-card"] [data-testid="project-name"]`))
      .toContainText(newProjectName)
  })

  test('應該能夠搜尋專案', async ({ page }) => {
    // 建立多個測試專案
    await DatabaseTestUtils.createTestProject('Vue 組件專案')
    await DatabaseTestUtils.createTestProject('React 組件專案')
    await DatabaseTestUtils.createTestProject('Angular 組件專案')
    
    // 導航到首頁
    await page.goto('/')
    
    // 等待專案列表載入
    await testUtils.waitForLoadingComplete()
    
    // 搜尋 Vue 專案
    await testUtils.typeWhenVisible('[data-testid="project-search-input"]', 'Vue')
    
    // 等待搜尋結果
    await page.waitForTimeout(500)
    
    // 驗證只顯示 Vue 專案
    await expect(page.locator('[data-testid="project-card"]')).toHaveCount(1)
    await expect(page.locator('[data-testid="project-name"]')).toContainText('Vue 組件專案')
    
    // 清空搜尋
    await page.fill('[data-testid="project-search-input"]', '')
    
    // 驗證所有專案都顯示
    await expect(page.locator('[data-testid="project-card"]')).toHaveCount(3)
  })

  test('應該能夠排序專案', async ({ page }) => {
    // 建立多個測試專案（有不同的建立時間）
    await DatabaseTestUtils.createTestProject('專案 A')
    await page.waitForTimeout(100)
    await DatabaseTestUtils.createTestProject('專案 B')
    await page.waitForTimeout(100)
    await DatabaseTestUtils.createTestProject('專案 C')
    
    // 導航到首頁
    await page.goto('/')
    
    // 等待專案列表載入
    await testUtils.waitForLoadingComplete()
    
    // 點擊排序選項
    await testUtils.clickWhenVisible('[data-testid="sort-dropdown"]')
    
    // 選擇按名稱排序
    await testUtils.clickWhenVisible('[data-testid="sort-by-name"]')
    
    // 驗證專案按名稱排序
    const projectNames = await page.locator('[data-testid="project-name"]').allTextContents()
    expect(projectNames).toEqual(projectNames.sort())
    
    // 選擇按建立時間排序
    await testUtils.clickWhenVisible('[data-testid="sort-dropdown"]')
    await testUtils.clickWhenVisible('[data-testid="sort-by-created"]')
    
    // 驗證專案按建立時間排序（最新的在前）
    const firstProject = await page.locator('[data-testid="project-name"]').first().textContent()
    expect(firstProject).toBe('專案 C')
  })

  test('應該處理專案建立錯誤', async ({ page }) => {
    // 導航到首頁
    await page.goto('/')
    
    // 點擊建立新專案按鈕
    await testUtils.clickWhenVisible('[data-testid="create-project-button"]')
    
    // 嘗試建立沒有名稱的專案
    await testUtils.clickWhenVisible('[data-testid="confirm-create-project"]')
    
    // 驗證顯示錯誤訊息
    await expect(page.locator('[data-testid="error-message"]')).toContainText('專案名稱不能為空')
    
    // 嘗試建立重複名稱的專案
    await DatabaseTestUtils.createTestProject('重複名稱專案')
    
    await testUtils.typeWhenVisible('[data-testid="project-name-input"]', '重複名稱專案')
    await testUtils.clickWhenVisible('[data-testid="confirm-create-project"]')
    
    // 驗證顯示重複名稱錯誤
    await expect(page.locator('[data-testid="error-message"]')).toContainText('專案名稱已存在')
  })

  test('應該支援專案匯入和匯出', async ({ page }) => {
    // 先建立一個測試專案
    const project = await DatabaseTestUtils.createTestProject('匯出測試專案')
    
    // 導航到專案頁面
    await page.goto(`/project/${project.id}`)
    
    // 點擊匯出按鈕
    await testUtils.clickWhenVisible('[data-testid="export-project-button"]')
    
    // 等待下載開始
    const downloadPromise = testUtils.waitForDownload()
    await testUtils.clickWhenVisible('[data-testid="confirm-export"]')
    
    const download = await downloadPromise
    expect(download.suggestedFilename()).toMatch(/.*\.zip$/)
    
    // 測試匯入功能
    await page.goto('/')
    await testUtils.clickWhenVisible('[data-testid="import-project-button"]')
    
    // 模擬檔案上傳（在實際測試中需要準備測試檔案）
    const fileInput = '[data-testid="import-file-input"]'
    // await page.setInputFiles(fileInput, 'path/to/test-project.zip')
    
    // 由於無法在此環境中實際上傳檔案，我們只驗證 UI 元素存在
    await expect(page.locator(fileInput)).toBeVisible()
    await expect(page.locator('[data-testid="confirm-import"]')).toBeVisible()
  })
})