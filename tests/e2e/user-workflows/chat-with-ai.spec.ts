import { test, expect } from '@playwright/test'
import { TestUtils, DatabaseTestUtils } from '../utils/test-utils'
import { ChatPage } from '../pages/chat-page'
import { EditorPage } from '../pages/editor-page'
import { PreviewPage } from '../pages/preview-page'

test.describe('AI 聊天功能測試', () => {
  let testUtils: TestUtils
  let chatPage: ChatPage
  let editorPage: EditorPage
  let previewPage: PreviewPage
  let testProjectId: string

  test.beforeEach(async ({ page }) => {
    testUtils = new TestUtils(page)
    chatPage = new ChatPage(page)
    editorPage = new EditorPage(page)
    previewPage = new PreviewPage(page)

    // 建立測試專案
    const project = await DatabaseTestUtils.createTestProject(`聊天測試專案_${Date.now()}`)
    testProjectId = project.id
  })

  test.afterEach(async () => {
    if (testProjectId) {
      await DatabaseTestUtils.deleteTestProject(testProjectId)
    }
  })

  test('基本聊天功能', async ({ page }) => {
    await chatPage.goto()
    
    // 測試簡單問候
    await chatPage.sendMessage('你好')
    await chatPage.waitForAiResponse()
    
    const response = await chatPage.getLastAiMessage()
    expect(response.length).toBeGreaterThan(0)
    
    // 測試組件生成請求
    await chatPage.sendMessage('請建立一個簡單的按鈕組件')
    await chatPage.waitForAiResponse()
    
    const componentResponse = await chatPage.getLastAiMessage()
    expect(componentResponse).toContain('button')
    expect(componentResponse).toContain('template')
  })

  test('複雜組件生成', async ({ page }) => {
    await chatPage.goto()
    
    // 請求複雜的表單組件
    const complexRequest = `
      請建立一個使用者註冊表單組件，包含以下功能：
      1. 使用者名稱輸入框（必填，最少3個字元）
      2. 電子郵件輸入框（必填，需要驗證格式）
      3. 密碼輸入框（必填，最少8個字元）
      4. 確認密碼輸入框（必須與密碼相符）
      5. 提交按鈕
      6. 表單驗證和錯誤提示
      7. 使用 Tailwind CSS 樣式
    `
    
    await chatPage.sendMessage(complexRequest)
    await chatPage.waitForAiResponse(45000) // 複雜請求可能需要更長時間
    
    const response = await chatPage.getLastAiMessage()
    
    // 驗證回應包含所需元素
    expect(response).toContain('form')
    expect(response).toContain('input')
    expect(response).toContain('validation')
    expect(response).toContain('tailwind')
    
    // 驗證生成的程式碼在編輯器中可見
    await editorPage.goto(testProjectId)
    await testUtils.waitForLoadingComplete()
    
    const fileList = await editorPage.getFileList()
    const formFile = fileList.find(file => 
      file.toLowerCase().includes('form') || 
      file.toLowerCase().includes('register')
    )
    expect(formFile).toBeDefined()
  })

  test('程式碼修改和優化', async ({ page }) => {
    await chatPage.goto()
    
    // 先生成基本組件
    await chatPage.sendMessage('請建立一個計數器組件')
    await chatPage.waitForAiResponse()
    
    // 請求修改
    await chatPage.sendMessage('請為計數器添加動畫效果')
    await chatPage.waitForAiResponse()
    
    // 請求樣式改進
    await chatPage.sendMessage('請改進計數器的視覺設計，使用現代化的 UI 風格')
    await chatPage.waitForAiResponse()
    
    // 請求功能擴展
    await chatPage.sendMessage('請添加步進值設定功能，讓使用者可以設定每次增減的數值')
    await chatPage.waitForAiResponse()
    
    // 驗證對話歷史
    const messages = await chatPage.getAllMessages()
    expect(messages.length).toBeGreaterThanOrEqual(8) // 4 個使用者訊息 + 4 個 AI 回應
    
    // 驗證最終組件功能
    await previewPage.goto(testProjectId)
    await previewPage.waitForPreviewLoad()
    
    expect(await previewPage.hasPreviewError()).toBe(false)
    expect(await previewPage.hasElementInPreview('button')).toBe(true)
  })

  test('錯誤處理和修正', async ({ page }) => {
    await chatPage.goto()
    
    // 生成有潛在問題的組件
    await chatPage.sendMessage('請建立一個複雜的資料表格組件，包含排序、篩選和分頁功能')
    await chatPage.waitForAiResponse(30000)
    
    // 檢查預覽是否有錯誤
    await previewPage.goto(testProjectId)
    await previewPage.waitForPreviewLoad()
    
    if (await previewPage.hasPreviewError()) {
      const errorMessage = await previewPage.getPreviewError()
      
      // 回到聊天，請求修正錯誤
      await chatPage.goto()
      await chatPage.sendMessage(`程式碼出現錯誤：${errorMessage}，請幫我修正`)
      await chatPage.waitForAiResponse(30000)
      
      // 再次檢查預覽
      await previewPage.goto(testProjectId)
      await previewPage.waitForPreviewLoad()
      
      // 錯誤應該已修正
      expect(await previewPage.hasPreviewError()).toBe(false)
    }
  })

  test('多語言和國際化支援', async ({ page }) => {
    await chatPage.goto()
    
    // 請求多語言組件
    const i18nRequest = `
      請建立一個多語言支援的導航選單組件，包含：
      1. 語言切換下拉選單
      2. 支援中文、英文、日文
      3. 選單項目會根據選擇的語言顯示對應文字
      4. 使用 Vue 3 的 i18n 功能
    `
    
    await chatPage.sendMessage(i18nRequest)
    await chatPage.waitForAiResponse(30000)
    
    const response = await chatPage.getLastAiMessage()
    expect(response).toContain('i18n')
    expect(response).toContain('locale')
    
    // 驗證組件在預覽中正常運作
    await previewPage.goto(testProjectId)
    await previewPage.waitForPreviewLoad()
    
    expect(await previewPage.hasElementInPreview('select')).toBe(true)
    expect(await previewPage.hasElementInPreview('nav')).toBe(true)
  })

  test('響應式設計請求', async ({ page }) => {
    await chatPage.goto()
    
    // 請求響應式組件
    const responsiveRequest = `
      請建立一個響應式的卡片網格組件，要求：
      1. 桌面版顯示 4 欄
      2. 平板版顯示 2 欄  
      3. 手機版顯示 1 欄
      4. 使用 Tailwind CSS 的響應式工具類
      5. 包含圖片、標題、描述和按鈕
    `
    
    await chatPage.sendMessage(responsiveRequest)
    await chatPage.waitForAiResponse(30000)
    
    // 測試響應式設計
    await previewPage.goto(testProjectId)
    await previewPage.waitForPreviewLoad()
    
    const responsiveResults = await previewPage.testResponsiveDesign()
    expect(responsiveResults.desktop).toBe(true)
    expect(responsiveResults.tablet).toBe(true)
    expect(responsiveResults.mobile).toBe(true)
  })

  test('API 整合組件', async ({ page }) => {
    await chatPage.goto()
    
    // 請求 API 整合組件
    const apiRequest = `
      請建立一個使用者列表組件，包含：
      1. 從 API 獲取使用者資料
      2. 載入狀態指示器
      3. 錯誤處理
      4. 搜尋功能
      5. 使用 Vue 3 的 Composition API
      6. 使用 fetch 或 axios 進行 API 呼叫
    `
    
    await chatPage.sendMessage(apiRequest)
    await chatPage.waitForAiResponse(30000)
    
    const response = await chatPage.getLastAiMessage()
    expect(response).toContain('fetch')
    expect(response).toContain('loading')
    expect(response).toContain('error')
    expect(response).toContain('composable')
  })

  test('動畫和過渡效果', async ({ page }) => {
    await chatPage.goto()
    
    // 請求動畫組件
    const animationRequest = `
      請建立一個圖片輪播組件，包含：
      1. 自動播放功能
      2. 手動切換按鈕
      3. 指示器點點
      4. 平滑的過渡動畫
      5. 暫停和繼續功能
      6. 使用 Vue 的 Transition 組件
    `
    
    await chatPage.sendMessage(animationRequest)
    await chatPage.waitForAiResponse(30000)
    
    // 檢查動畫效果
    await previewPage.goto(testProjectId)
    await previewPage.waitForPreviewLoad()
    
    expect(await previewPage.checkAnimations()).toBe(true)
    expect(await previewPage.hasElementInPreview('[data-testid="carousel"]')).toBe(true)
  })

  test('聊天歷史和上下文', async ({ page }) => {
    await chatPage.goto()
    
    // 建立對話上下文
    await chatPage.sendMessage('請建立一個部落格文章組件')
    await chatPage.waitForAiResponse()
    
    await chatPage.sendMessage('請為這個組件添加評論功能')
    await chatPage.waitForAiResponse()
    
    await chatPage.sendMessage('請為評論添加回覆功能')
    await chatPage.waitForAiResponse()
    
    await chatPage.sendMessage('請添加點讚功能')
    await chatPage.waitForAiResponse()
    
    // 驗證 AI 能夠理解上下文
    const lastResponse = await chatPage.getLastAiMessage()
    expect(lastResponse).toContain('like')
    
    // 測試引用之前的對話
    await chatPage.sendMessage('請修改之前提到的評論功能，添加編輯和刪除選項')
    await chatPage.waitForAiResponse()
    
    const contextResponse = await chatPage.getLastAiMessage()
    expect(contextResponse).toContain('edit')
    expect(contextResponse).toContain('delete')
  })

  test('效能優化建議', async ({ page }) => {
    await chatPage.goto()
    
    // 請求效能優化
    const performanceRequest = `
      我有一個大型的資料表格組件，包含 1000+ 行資料，
      請幫我優化效能，包含：
      1. 虛擬滾動
      2. 懶載入
      3. 記憶化
      4. 防抖搜尋
    `
    
    await chatPage.sendMessage(performanceRequest)
    await chatPage.waitForAiResponse(30000)
    
    const response = await chatPage.getLastAiMessage()
    expect(response).toContain('virtual')
    expect(response).toContain('lazy')
    expect(response).toContain('memo')
    expect(response).toContain('debounce')
  })

  test('無障礙功能支援', async ({ page }) => {
    await chatPage.goto()
    
    // 請求無障礙組件
    const a11yRequest = `
      請建立一個無障礙的模態對話框組件，包含：
      1. 鍵盤導航支援
      2. 螢幕閱讀器支援
      3. 焦點管理
      4. ARIA 屬性
      5. ESC 鍵關閉功能
    `
    
    await chatPage.sendMessage(a11yRequest)
    await chatPage.waitForAiResponse(30000)
    
    const response = await chatPage.getLastAiMessage()
    expect(response).toContain('aria')
    expect(response).toContain('tabindex')
    expect(response).toContain('focus')
    expect(response).toContain('keyboard')
  })
})