import { test, expect } from '@playwright/test'
import { TestUtils, DatabaseTestUtils, PerformanceUtils } from '../utils/test-utils'
import { ChatPage } from '../pages/chat-page'
import { EditorPage } from '../pages/editor-page'
import { PreviewPage } from '../pages/preview-page'

test.describe('完整使用者場景測試', () => {
  let testUtils: TestUtils
  let performanceUtils: PerformanceUtils
  let chatPage: ChatPage
  let editorPage: EditorPage
  let previewPage: PreviewPage
  let testProjectId: string

  test.beforeEach(async ({ page }) => {
    testUtils = new TestUtils(page)
    performanceUtils = new PerformanceUtils(page)
    chatPage = new ChatPage(page)
    editorPage = new EditorPage(page)
    previewPage = new PreviewPage(page)

    // 清理測試資料
    await DatabaseTestUtils.cleanupTestData()
    
    // 建立測試專案
    const project = await DatabaseTestUtils.createTestProject(`完整測試專案_${Date.now()}`)
    testProjectId = project.id
  })

  test.afterEach(async () => {
    // 清理測試資料
    if (testProjectId) {
      await DatabaseTestUtils.deleteTestProject(testProjectId)
    }
    await DatabaseTestUtils.cleanupTestData()
  })

  test('完整工作流程：專案建立 → 聊天生成組件 → 編輯程式碼 → 預覽結果', async ({ page }) => {
    // 1. 導航到專案
    await page.goto(`/project/${testProjectId}`)
    await testUtils.waitForLoadingComplete()

    // 驗證專案頁面載入
    await expect(page.locator('[data-testid="project-container"]')).toBeVisible()

    // 2. 開始聊天生成組件
    await chatPage.goto()
    
    // 發送組件生成請求
    const componentRequest = '請幫我建立一個簡單的計數器組件，包含增加和減少按鈕，以及顯示當前數值'
    await chatPage.sendMessage(componentRequest)
    
    // 等待 AI 回應
    await chatPage.waitForAiResponse(30000)
    
    // 驗證 AI 回應包含程式碼
    const aiResponse = await chatPage.getLastAiMessage()
    expect(aiResponse).toContain('Counter')
    expect(aiResponse.length).toBeGreaterThan(100)

    // 3. 檢查生成的程式碼是否出現在編輯器中
    await editorPage.goto(testProjectId)
    
    // 等待檔案樹載入
    await testUtils.waitForLoadingComplete()
    
    // 檢查是否有新的組件檔案
    const fileList = await editorPage.getFileList()
    const counterFile = fileList.find(file => file.includes('Counter'))
    expect(counterFile).toBeDefined()

    // 4. 編輯生成的程式碼
    if (counterFile) {
      await editorPage.clickFile(counterFile)
      
      // 獲取當前程式碼
      const originalCode = await editorPage.getEditorContent()
      expect(originalCode).toContain('Counter')
      
      // 修改程式碼（添加樣式）
      const modifiedCode = originalCode.replace(
        '<template>',
        '<template>\n  <!-- 修改過的計數器組件 -->'
      )
      
      await editorPage.typeInEditor(modifiedCode)
      await editorPage.saveFile()
      
      // 驗證檔案已修改
      expect(await editorPage.hasUnsavedChanges(counterFile)).toBe(false)
    }

    // 5. 預覽組件
    await previewPage.goto(testProjectId)
    
    // 等待預覽載入
    await previewPage.waitForPreviewLoad()
    
    // 驗證預覽沒有錯誤
    expect(await previewPage.hasPreviewError()).toBe(false)
    
    // 驗證組件在預覽中正確顯示
    expect(await previewPage.hasElementInPreview('button')).toBe(true)
    expect(await previewPage.hasElementInPreview('[data-testid="counter-display"]')).toBe(true)

    // 6. 測試組件互動功能
    // 點擊增加按鈕
    await previewPage.clickInPreview('[data-testid="increment-button"]')
    
    // 驗證計數器值改變
    const counterValue = await previewPage.getTextFromPreview('[data-testid="counter-display"]')
    expect(counterValue).toContain('1')
    
    // 點擊減少按鈕
    await previewPage.clickInPreview('[data-testid="decrement-button"]')
    
    // 驗證計數器值回到 0
    const newCounterValue = await previewPage.getTextFromPreview('[data-testid="counter-display"]')
    expect(newCounterValue).toContain('0')

    // 7. 測試響應式設計
    const responsiveResults = await previewPage.testResponsiveDesign()
    expect(responsiveResults.desktop).toBe(true)
    expect(responsiveResults.tablet).toBe(true)
    expect(responsiveResults.mobile).toBe(true)

    // 8. 繼續聊天改進組件
    await chatPage.goto()
    
    const improvementRequest = '請為計數器組件添加重置按鈕和最大值限制（最大值為 10）'
    await chatPage.sendMessage(improvementRequest)
    
    await chatPage.waitForAiResponse(30000)
    
    // 9. 驗證改進後的組件
    await previewPage.goto(testProjectId)
    await previewPage.waitForPreviewLoad()
    
    // 檢查是否有重置按鈕
    expect(await previewPage.hasElementInPreview('[data-testid="reset-button"]')).toBe(true)
    
    // 測試最大值限制
    for (let i = 0; i < 12; i++) {
      await previewPage.clickInPreview('[data-testid="increment-button"]')
    }
    
    const maxValue = await previewPage.getTextFromPreview('[data-testid="counter-display"]')
    expect(parseInt(maxValue)).toBeLessThanOrEqual(10)

    // 10. 測試檔案管理功能
    await editorPage.goto(testProjectId)
    
    // 建立新的樣式檔案
    await editorPage.createFile('Counter.css', `
      .counter-container {
        padding: 20px;
        border: 1px solid #ccc;
        border-radius: 8px;
        text-align: center;
      }
      
      .counter-button {
        margin: 0 10px;
        padding: 8px 16px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
      }
    `)
    
    // 驗證檔案已建立
    const updatedFileList = await editorPage.getFileList()
    expect(updatedFileList).toContain('Counter.css')

    // 11. 效能測試
    const loadTime = await performanceUtils.measurePageLoadTime()
    expect(loadTime).toBeLessThan(5000) // 頁面載入時間應少於 5 秒
    
    const fcp = await performanceUtils.measureFCP()
    expect(fcp).toBeLessThan(3000) // 首次內容繪製應少於 3 秒
  })

  test('錯誤處理和恢復流程', async ({ page }) => {
    // 1. 測試網路斷線情況
    await chatPage.goto()
    
    // 模擬網路斷線
    await chatPage.simulateNetworkDisconnection()
    
    // 嘗試發送訊息
    await chatPage.sendMessage('測試訊息')
    
    // 驗證顯示錯誤狀態
    expect(await chatPage.hasError()).toBe(true)
    expect(await chatPage.getConnectionStatus()).toBe('disconnected')
    
    // 恢復網路連線
    await chatPage.restoreNetworkConnection()
    await chatPage.waitForReconnection()
    
    // 驗證連線恢復
    expect(await chatPage.getConnectionStatus()).toBe('connected')

    // 2. 測試編譯錯誤處理
    await editorPage.goto(testProjectId)
    
    // 建立有語法錯誤的檔案
    await editorPage.createFile('ErrorComponent.vue', `
      <template>
        <div>
          <h1>{{ title </h1>
        </div>
      </template>
      
      <script setup>
      const title = 'Error Test'
      </script>
    `)
    
    // 驗證編輯器顯示語法錯誤
    expect(await editorPage.hasSyntaxErrors()).toBe(true)
    
    // 修正錯誤
    const correctedCode = `
      <template>
        <div>
          <h1>{{ title }}</h1>
        </div>
      </template>
      
      <script setup>
      const title = 'Error Test'
      </script>
    `
    
    await editorPage.typeInEditor(correctedCode)
    await editorPage.saveFile()
    
    // 驗證錯誤已修正
    expect(await editorPage.hasSyntaxErrors()).toBe(false)

    // 3. 測試預覽錯誤處理
    await previewPage.goto(testProjectId)
    
    // 如果有編譯錯誤，預覽應該顯示錯誤訊息
    if (await previewPage.hasPreviewError()) {
      const errorMessage = await previewPage.getPreviewError()
      expect(errorMessage.length).toBeGreaterThan(0)
      
      // 重新整理預覽
      await previewPage.refreshPreview()
    }
  })

  test('多檔案專案管理流程', async ({ page }) => {
    await editorPage.goto(testProjectId)
    
    // 1. 建立複雜的檔案結構
    await editorPage.createFolder('components')
    await editorPage.createFolder('utils')
    await editorPage.createFolder('styles')
    
    // 2. 在不同資料夾中建立檔案
    await editorPage.expandFolder('components')
    await editorPage.createFile('components/Button.vue', `
      <template>
        <button class="btn" @click="$emit('click')">
          <slot></slot>
        </button>
      </template>
      
      <script setup>
      defineEmits(['click'])
      </script>
      
      <style scoped>
      .btn {
        padding: 8px 16px;
        border: 1px solid #ccc;
        border-radius: 4px;
        cursor: pointer;
      }
      </style>
    `)
    
    await editorPage.createFile('utils/helpers.ts', `
      export function formatNumber(num: number): string {
        return num.toLocaleString()
      }
      
      export function debounce(func: Function, wait: number) {
        let timeout: NodeJS.Timeout
        return function executedFunction(...args: any[]) {
          const later = () => {
            clearTimeout(timeout)
            func(...args)
          }
          clearTimeout(timeout)
          timeout = setTimeout(later, wait)
        }
      }
    `)
    
    await editorPage.createFile('styles/global.css', `
      * {
        box-sizing: border-box;
      }
      
      body {
        font-family: Arial, sans-serif;
        margin: 0;
        padding: 0;
      }
    `)

    // 3. 測試檔案操作
    // 重新命名檔案
    await editorPage.renameFile('components/Button.vue', 'components/CustomButton.vue')
    
    // 驗證檔案已重新命名
    const fileList = await editorPage.getFileList()
    expect(fileList).toContain('components/CustomButton.vue')
    expect(fileList).not.toContain('components/Button.vue')
    
    // 4. 測試檔案拖拽
    await editorPage.dragFileToFolder('utils/helpers.ts', 'components')
    
    // 5. 測試多分頁編輯
    await editorPage.clickFile('components/CustomButton.vue')
    await editorPage.clickFile('styles/global.css')
    
    // 驗證多個分頁已開啟
    const openTabs = await editorPage.getOpenTabs()
    expect(openTabs.length).toBeGreaterThan(1)
    
    // 6. 測試分頁切換
    await editorPage.switchToTab('components/CustomButton.vue')
    
    // 驗證編輯器內容切換
    const editorContent = await editorPage.getEditorContent()
    expect(editorContent).toContain('CustomButton')

    // 7. 關閉分頁
    await editorPage.closeTab('styles/global.css')
    
    const updatedTabs = await editorPage.getOpenTabs()
    expect(updatedTabs).not.toContain('styles/global.css')
  })

  test('AI 聊天進階功能測試', async ({ page }) => {
    await chatPage.goto()
    
    // 1. 測試多輪對話
    await chatPage.sendMessage('請建立一個待辦事項列表組件')
    await chatPage.waitForAiResponse()
    
    await chatPage.sendMessage('請為這個組件添加拖拽排序功能')
    await chatPage.waitForAiResponse()
    
    await chatPage.sendMessage('請添加本地儲存功能，讓待辦事項可以持久化')
    await chatPage.waitForAiResponse()
    
    // 驗證對話歷史
    const messages = await chatPage.getAllMessages()
    expect(messages.length).toBeGreaterThanOrEqual(6) // 3 個使用者訊息 + 3 個 AI 回應
    
    // 2. 測試程式碼修改請求
    await chatPage.sendMessage('請修改按鈕的顏色為藍色，並添加懸停效果')
    await chatPage.waitForAiResponse()
    
    // 3. 測試錯誤修正請求
    await chatPage.sendMessage('上面的程式碼有語法錯誤，請幫我修正')
    await chatPage.waitForAiResponse()
    
    // 4. 驗證 AI 回應包含程式碼
    const lastResponse = await chatPage.getLastAiMessage()
    expect(await chatPage.messageContainsCode(messages.length)).toBe(true)
  })

  test('效能基準測試', async ({ page }) => {
    // 1. 頁面載入效能
    const startTime = Date.now()
    await page.goto(`/project/${testProjectId}`)
    await testUtils.waitForLoadingComplete()
    const loadTime = Date.now() - startTime
    
    expect(loadTime).toBeLessThan(3000) // 頁面載入應在 3 秒內完成
    
    // 2. 聊天回應時間
    await chatPage.goto()
    
    const chatStartTime = Date.now()
    await chatPage.sendMessage('請建立一個簡單的 Hello World 組件')
    await chatPage.waitForAiResponse()
    const chatResponseTime = Date.now() - chatStartTime
    
    expect(chatResponseTime).toBeLessThan(15000) // AI 回應應在 15 秒內
    
    // 3. 編輯器效能
    await editorPage.goto(testProjectId)
    
    const editorStartTime = Date.now()
    await editorPage.createFile('PerformanceTest.vue', `
      <template>
        <div>
          ${'<p>Performance test line</p>\n'.repeat(100)}
        </div>
      </template>
    `)
    const editorTime = Date.now() - editorStartTime
    
    expect(editorTime).toBeLessThan(2000) // 大檔案建立應在 2 秒內
    
    // 4. 預覽編譯效能
    await previewPage.goto(testProjectId)
    
    const previewStartTime = Date.now()
    await previewPage.waitForPreviewLoad()
    const previewTime = Date.now() - previewStartTime
    
    expect(previewTime).toBeLessThan(5000) // 預覽編譯應在 5 秒內
    
    // 5. 記憶體使用測試
    const memoryUsage = await performanceUtils.getMemoryUsage()
    expect(memoryUsage.usedJSHeapSize).toBeLessThan(100 * 1024 * 1024) // 記憶體使用應少於 100MB
  })

  test('跨瀏覽器相容性基本測試', async ({ page, browserName }) => {
    // 這個測試會在不同瀏覽器中執行
    await page.goto(`/project/${testProjectId}`)
    
    // 基本功能測試
    await expect(page.locator('[data-testid="project-container"]')).toBeVisible()
    
    // 聊天功能
    await chatPage.goto()
    await chatPage.sendMessage('測試瀏覽器相容性')
    await chatPage.waitForAiResponse()
    
    expect(await chatPage.getLastAiMessage().length).toBeGreaterThan(0)
    
    // 編輯器功能
    await editorPage.goto(testProjectId)
    await editorPage.createFile(`${browserName}-test.vue`, '<template><div>Test</div></template>')
    
    // 預覽功能
    await previewPage.goto(testProjectId)
    await previewPage.waitForPreviewLoad()
    
    expect(await previewPage.hasPreviewError()).toBe(false)
  })
})