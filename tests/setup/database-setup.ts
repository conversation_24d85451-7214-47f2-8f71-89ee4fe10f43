// 資料庫測試設置
import { execSync } from 'child_process'
import { existsSync, unlinkSync } from 'fs'
import { resolve } from 'path'

/**
 * 設置測試資料庫
 */
export async function setupTestDatabase(): Promise<void> {
  // 使用時間戳和隨機數生成唯一的測試資料庫名稱
  const timestamp = Date.now()
  const random = Math.floor(Math.random() * 10000)
  const testDbName = `test-${timestamp}-${random}.db`
  const testDbPath = resolve(process.cwd(), 'prisma', testDbName)
  
  try {
    // 刪除現有的測試資料庫
    if (existsSync(testDbPath)) {
      unlinkSync(testDbPath)
      console.log('🗑️ Removed existing test database')
    }

    // 設置測試環境變數
    const databaseUrl = `file:./prisma/${testDbName}`
    process.env.DATABASE_URL = databaseUrl
    
    // 使用 Prisma 創建測試資料庫架構
    execSync('npx prisma db push --force-reset', {
      stdio: 'pipe',
      env: { ...process.env, DATABASE_URL: databaseUrl }
    })
    
    console.log(`✅ Test database schema created: ${testDbName}`)
  } catch (error) {
    console.error('❌ Failed to setup test database:', error)
    throw error
  }
}

/**
 * 清理測試資料庫
 */
export async function cleanupTestDatabase(): Promise<void> {
  try {
    // 從環境變數中獲取當前測試資料庫路徑
    const databaseUrl = process.env.DATABASE_URL
    if (databaseUrl && databaseUrl.startsWith('file:./prisma/')) {
      const dbFileName = databaseUrl.replace('file:./prisma/', '')
      const testDbPath = resolve(process.cwd(), 'prisma', dbFileName)
      
      if (existsSync(testDbPath)) {
        unlinkSync(testDbPath)
        console.log(`🗑️ Test database cleaned up: ${dbFileName}`)
      }
    }
    
    // 清理所有測試資料庫檔案 (以 test- 開頭的)
    const prismaDir = resolve(process.cwd(), 'prisma')
    if (existsSync(prismaDir)) {
      const { readdirSync } = require('fs')
      const files = readdirSync(prismaDir)
      
      for (const file of files) {
        if (file.startsWith('test-') && file.endsWith('.db')) {
          const filePath = resolve(prismaDir, file)
          try {
            unlinkSync(filePath)
            console.log(`🗑️ Cleaned up old test database: ${file}`)
          } catch (error) {
            // 忽略清理失敗的檔案
          }
        }
      }
    }
  } catch (error) {
    console.error('❌ Failed to cleanup test database:', error)
    // 不拋出錯誤，因為清理失敗不應該影響測試
  }
}