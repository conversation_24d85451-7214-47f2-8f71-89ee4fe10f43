// 整合測試環境設置
import { vi } from 'vitest'
import dotenv from 'dotenv'

// 載入環境變數
dotenv.config({ path: '.env.test' })
dotenv.config({ path: '.env' })

// 設置測試環境變數
process.env.NODE_ENV = 'test'
process.env.DATABASE_URL = process.env.DATABASE_URL || 'file:./prisma/test.db'

// Mock console methods to reduce noise in tests
const originalConsole = { ...console }

beforeAll(() => {
  // Reduce console noise during tests but keep important ones
  console.log = vi.fn()
  console.info = vi.fn()
  // Keep warn and error for debugging integration issues
})

afterAll(() => {
  // Restore console
  Object.assign(console, originalConsole)
})

// Global test cleanup
afterEach(() => {
  vi.clearAllMocks()
})

// Handle unhandled promise rejections in tests
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason)
})

// Increase timeout for integration tests
vi.setConfig({
  testTimeout: 30000,
  hookTimeout: 10000,
})