// CI 環境測試設置
import { vi } from 'vitest'
import dotenv from 'dotenv'
import { setupTestDatabase, cleanupTestDatabase } from './database-setup'

// 載入環境變數
dotenv.config({ path: '.env.test' })
dotenv.config({ path: '.env' })

// 設置測試環境變數
process.env.NODE_ENV = 'test'
process.env.CI = 'true'
process.env.DATABASE_URL = process.env.DATABASE_URL || 'file:./prisma/test.db'

// 全域測試設定
if (typeof window !== 'undefined') {
  // 前端環境設定
  global.ResizeObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn(),
  }))

  // Mock IntersectionObserver
  global.IntersectionObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn(),
  }))

  // Mock matchMedia
  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: vi.fn().mockImplementation(query => ({
      matches: false,
      media: query,
      onchange: null,
      addListener: vi.fn(),
      removeListener: vi.fn(),
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
      dispatchEvent: vi.fn(),
    })),
  })

  // Mock scrollTo
  Object.defineProperty(window, 'scrollTo', {
    writable: true,
    value: vi.fn(),
  })
}

// 測試開始前的全域設置
beforeAll(async () => {
  // 設置測試資料庫
  if (process.env.VITEST_POOL_ID === '1' || !process.env.VITEST_POOL_ID) {
    // 只在主進程或單一進程中初始化資料庫
    await setupTestDatabase()
  }
  
  // 減少測試過程中的控制台輸出
  console.log = vi.fn()
  console.info = vi.fn()
  // 保留警告和錯誤以便調試
})

// 測試結束後的全域清理
afterAll(async () => {
  // 清理測試資料庫
  if (process.env.VITEST_POOL_ID === '1' || !process.env.VITEST_POOL_ID) {
    // 只在主進程或單一進程中清理資料庫
    await cleanupTestDatabase()
  }
})

// 每個測試後的清理
afterEach(() => {
  vi.clearAllMocks()
})

// 處理測試中未捕獲的 Promise 拒絕
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason)
})

// 設置較長的超時時間，適應 CI 環境可能較慢的執行速度
vi.setConfig({
  testTimeout: 15000,
  hookTimeout: 15000,
})