// 前端測試環境設置
import { vi } from 'vitest'
import { config } from '@vue/test-utils'

// Mock 環境變數
vi.mock('process', () => ({
  env: {
    VITE_APP_NAME: 'UIGen Vue Test',
    VITE_API_BASE_URL: 'http://localhost:3001',
    NODE_ENV: 'test',
  },
}))

// 全域測試設定
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}))

// Mock IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}))

// Mock matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(), // deprecated
    removeListener: vi.fn(), // deprecated
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
})

// Mock scrollTo
Object.defineProperty(window, 'scrollTo', {
  writable: true,
  value: vi.fn(),
})

// 設定 Vue Test Utils 全域配置
config.global.config.globalProperties = {
  ...config.global.config.globalProperties,
}

// 為 Teleport 組件設定測試環境
beforeEach(() => {
  // 確保每個測試都有乾淨的 DOM
  document.body.innerHTML = ''
  
  // 為 Teleport 創建目標容器
  const teleportTarget = document.createElement('div')
  teleportTarget.id = 'teleport-target'
  document.body.appendChild(teleportTarget)
})

// 清理測試後的狀態
afterEach(() => {
  vi.clearAllMocks()
})