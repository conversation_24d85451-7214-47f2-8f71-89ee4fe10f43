// Server 測試環境設置
import { vi } from 'vitest'
import dotenv from 'dotenv'
import { setupTestDatabase, cleanupTestDatabase } from './database-setup'

// 載入環境變數
dotenv.config({ path: '.env.test' })
dotenv.config({ path: '.env' })

// 設置測試環境變數
process.env.NODE_ENV = 'test'
process.env.DATABASE_URL = process.env.DATABASE_URL || 'file:./prisma/test.db'

// Mock console methods to reduce noise in tests
const originalConsole = { ...console }

beforeAll(async () => {
  // Setup test database
  await setupTestDatabase()
  
  // Reduce console noise during tests
  console.log = vi.fn()
  console.info = vi.fn()
  console.warn = vi.fn()
  // Keep error for debugging
})

afterAll(async () => {
  // Restore console
  Object.assign(console, originalConsole)
  
  // Cleanup test database
  await cleanupTestDatabase()
})

// Global test cleanup
afterEach(() => {
  vi.clearAllMocks()
})

// Handle unhandled promise rejections in tests
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason)
})