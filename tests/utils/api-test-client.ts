import { Express } from 'express'
import request from 'supertest'
import { Server } from 'http'

export interface RequestOptions {
  headers?: Record<string, string>
  timeout?: number
  auth?: {
    token?: string
    basic?: { username: string; password: string }
  }
}

export interface APIResponse<T = any> {
  status: number
  statusText: string
  data: T
  headers: Record<string, string>
  body: any
}

export class APITestClient {
  private app: Express
  private server?: Server
  private baseURL: string = ''
  private authToken?: string
  private defaultHeaders: Record<string, string> = {
    'Content-Type': 'application/json'
  }

  constructor(app: Express) {
    this.app = app
  }

  /**
   * 設置認證 token
   */
  setAuthToken(token: string): void {
    this.authToken = token
    this.defaultHeaders['Authorization'] = `Bearer ${token}`
  }

  /**
   * 清除認證
   */
  clearAuth(): void {
    this.authToken = undefined
    delete this.defaultHeaders['Authorization']
  }

  /**
   * 設置預設標頭
   */
  setDefaultHeaders(headers: Record<string, string>): void {
    this.defaultHeaders = { ...this.defaultHeaders, ...headers }
  }

  /**
   * GET 請求
   */
  async get<T = any>(url: string, options: RequestOptions = {}): Promise<APIResponse<T>> {
    const headers = { ...this.defaultHeaders, ...options.headers }
    
    const response = await request(this.app)
      .get(url)
      .set(headers)
      .timeout(options.timeout || 5000)

    return this.formatResponse<T>(response)
  }

  /**
   * POST 請求
   */
  async post<T = any>(url: string, data: any = {}, options: RequestOptions = {}): Promise<APIResponse<T>> {
    const headers = { ...this.defaultHeaders, ...options.headers }
    
    const response = await request(this.app)
      .post(url)
      .set(headers)
      .send(data)
      .timeout(options.timeout || 5000)

    return this.formatResponse<T>(response)
  }

  /**
   * PUT 請求
   */
  async put<T = any>(url: string, data: any = {}, options: RequestOptions = {}): Promise<APIResponse<T>> {
    const headers = { ...this.defaultHeaders, ...options.headers }
    
    const response = await request(this.app)
      .put(url)
      .set(headers)
      .send(data)
      .timeout(options.timeout || 5000)

    return this.formatResponse<T>(response)
  }

  /**
   * PATCH 請求
   */
  async patch<T = any>(url: string, data: any = {}, options: RequestOptions = {}): Promise<APIResponse<T>> {
    const headers = { ...this.defaultHeaders, ...options.headers }
    
    const response = await request(this.app)
      .patch(url)
      .set(headers)
      .send(data)
      .timeout(options.timeout || 5000)

    return this.formatResponse<T>(response)
  }

  /**
   * DELETE 請求
   */
  async delete<T = any>(url: string, options: RequestOptions = {}): Promise<APIResponse<T>> {
    const headers = { ...this.defaultHeaders, ...options.headers }
    
    const response = await request(this.app)
      .delete(url)
      .set(headers)
      .timeout(options.timeout || 5000)

    return this.formatResponse<T>(response)
  }

  /**
   * 格式化回應
   */
  private formatResponse<T>(response: request.Response): APIResponse<T> {
    return {
      status: response.status,
      statusText: response.statusText || this.getStatusText(response.status),
      data: response.body,
      headers: response.headers,
      body: response.body
    }
  }

  /**
   * 取得狀態碼對應的文字
   */
  private getStatusText(status: number): string {
    const statusTexts: Record<number, string> = {
      200: 'OK',
      201: 'Created',
      204: 'No Content',
      400: 'Bad Request',
      401: 'Unauthorized',
      403: 'Forbidden',
      404: 'Not Found',
      409: 'Conflict',
      422: 'Unprocessable Entity',
      500: 'Internal Server Error'
    }
    return statusTexts[status] || 'Unknown'
  }

  /**
   * 驗證回應狀態碼
   */
  expectStatus(response: APIResponse, expectedStatus: number): void {
    if (response.status !== expectedStatus) {
      throw new Error(
        `Expected status ${expectedStatus} but got ${response.status}. Response: ${JSON.stringify(response.data)}`
      )
    }
  }

  /**
   * 驗證回應包含特定欄位
   */
  expectResponseToHaveProperty(response: APIResponse, property: string): void {
    if (!response.data || typeof response.data !== 'object') {
      throw new Error('Response data is not an object')
    }
    
    if (!(property in response.data)) {
      throw new Error(`Expected response to have property '${property}'. Got: ${JSON.stringify(response.data)}`)
    }
  }

  /**
   * 驗證回應資料結構
   */
  expectResponseStructure(response: APIResponse, expectedStructure: Record<string, any>): void {
    const data = response.data
    
    for (const [key, expectedType] of Object.entries(expectedStructure)) {
      if (!(key in data)) {
        throw new Error(`Missing property '${key}' in response`)
      }
      
      const actualType = typeof data[key]
      if (actualType !== expectedType && expectedType !== 'any') {
        throw new Error(`Property '${key}' should be ${expectedType} but got ${actualType}`)
      }
    }
  }

  /**
   * 驗證錯誤回應格式
   */
  expectErrorResponse(response: APIResponse, expectedStatus: number, expectedMessage?: string): void {
    this.expectStatus(response, expectedStatus)
    
    if (!response.data || typeof response.data !== 'object') {
      throw new Error('Error response should be an object')
    }
    
    if (!('error' in response.data) && !('message' in response.data)) {
      throw new Error('Error response should contain error or message field')
    }
    
    if (expectedMessage) {
      const actualMessage = response.data.error || response.data.message
      if (actualMessage !== expectedMessage) {
        throw new Error(`Expected error message '${expectedMessage}' but got '${actualMessage}'`)
      }
    }
  }
}