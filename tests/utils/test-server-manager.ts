import express, { Express } from 'express'
import { Server } from 'http'
import cors from 'cors'
import { DatabaseTestManager } from './database-test-manager'
import { setupRoutes } from '../../server/routes'
import { errorHandler } from '../../server/middleware/error-handler'
import { notFoundHandler } from '../../server/middleware/not-found'

export interface TestServerConfig {
  port?: number
  host?: string
  database?: {
    url?: string
    resetOnStart?: boolean
    seedData?: boolean
  }
  middleware?: {
    cors?: boolean
    logging?: boolean
    errorHandling?: boolean
    bodyParser?: boolean
  }
  routes?: string[]
  timeout?: number
}

export class TestServerManager {
  private app: Express
  private server?: Server
  private config: TestServerConfig
  private databaseManager?: DatabaseTestManager
  private isRunning: boolean = false

  constructor(config: TestServerConfig = {}) {
    this.config = {
      port: 0, // 使用隨機可用 port
      host: 'localhost',
      database: {
        resetOnStart: true,
        seedData: false,
        ...config.database
      },
      middleware: {
        cors: true,
        logging: false,
        errorHandling: true,
        bodyParser: true,
        ...config.middleware
      },
      timeout: 10000,
      ...config
    }
    
    this.app = express()
    this.setupMiddleware()
  }

  /**
   * 設置中介軟體
   */
  private setupMiddleware(): void {
    // CORS
    if (this.config.middleware?.cors) {
      this.app.use(cors({
        origin: true,
        credentials: true
      }))
    }

    // Body parser
    if (this.config.middleware?.bodyParser) {
      this.app.use(express.json({ limit: '10mb' }))
      this.app.use(express.urlencoded({ extended: true, limit: '10mb' }))
    }

    // 請求日誌 (測試環境通常關閉)
    if (this.config.middleware?.logging) {
      this.app.use((req, res, next) => {
        console.log(`${req.method} ${req.path}`)
        next()
      })
    }

    // 設置路由
    this.setupRoutes()

    // 錯誤處理中介軟體
    if (this.config.middleware?.errorHandling) {
      this.app.use(notFoundHandler)
      this.app.use(errorHandler)
    }
  }

  /**
   * 設置路由
   */
  private setupRoutes(): void {
    try {
      // 健康檢查端點
      this.app.get('/health', (req, res) => {
        res.json({ 
          status: 'ok', 
          timestamp: new Date().toISOString(),
          environment: 'test'
        })
      })

      // 設置 API 路由
      setupRoutes(this.app)
    } catch (error) {
      console.error('Failed to setup routes:', error)
      throw error
    }
  }

  /**
   * 啟動測試伺服器
   */
  async start(): Promise<void> {
    if (this.isRunning) {
      return
    }

    try {
      // 初始化資料庫
      if (this.config.database?.resetOnStart) {
        this.databaseManager = new DatabaseTestManager()
        await this.databaseManager.initializeTestDatabase()
        
        if (this.config.database.seedData) {
          await this.databaseManager.seedTestData()
        }
      }

      // 啟動伺服器
      await new Promise<void>((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error(`Server failed to start within ${this.config.timeout}ms`))
        }, this.config.timeout)

        this.server = this.app.listen(this.config.port, this.config.host, () => {
          clearTimeout(timeout)
          this.isRunning = true
          resolve()
        })

        this.server.on('error', (error) => {
          clearTimeout(timeout)
          reject(error)
        })
      })

    } catch (error) {
      console.error('Failed to start test server:', error)
      throw error
    }
  }

  /**
   * 停止測試伺服器
   */
  async stop(): Promise<void> {
    if (!this.isRunning || !this.server) {
      return
    }

    try {
      // 關閉伺服器
      await new Promise<void>((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('Server failed to stop gracefully'))
        }, 5000)

        this.server!.close((error) => {
          clearTimeout(timeout)
          if (error) {
            reject(error)
          } else {
            resolve()
          }
        })
      })

      // 清理資料庫
      if (this.databaseManager) {
        await this.databaseManager.cleanup()
      }

      this.isRunning = false
      this.server = undefined

    } catch (error) {
      console.error('Failed to stop test server:', error)
      throw error
    }
  }

  /**
   * 重置伺服器狀態
   */
  async reset(): Promise<void> {
    if (this.databaseManager) {
      await this.databaseManager.resetDatabase()
    }
  }

  /**
   * 取得伺服器基礎 URL
   */
  getBaseURL(): string {
    if (!this.isRunning || !this.server) {
      throw new Error('Server is not running')
    }

    const address = this.server.address()
    if (!address || typeof address === 'string') {
      throw new Error('Unable to get server address')
    }

    return `http://${this.config.host}:${address.port}`
  }

  /**
   * 取得 Express 應用實例
   */
  getApp(): Express {
    return this.app
  }

  /**
   * 檢查伺服器是否運行中
   */
  isServerRunning(): boolean {
    return this.isRunning
  }

  /**
   * 取得伺服器 port
   */
  getPort(): number {
    if (!this.server) {
      throw new Error('Server is not running')
    }

    const address = this.server.address()
    if (!address || typeof address === 'string') {
      throw new Error('Unable to get server port')
    }

    return address.port
  }

  /**
   * 等待伺服器準備就緒
   */
  async waitForReady(timeout: number = 5000): Promise<void> {
    const startTime = Date.now()
    
    while (Date.now() - startTime < timeout) {
      if (this.isRunning) {
        try {
          // 嘗試健康檢查
          const response = await fetch(`${this.getBaseURL()}/health`)
          if (response.ok) {
            return
          }
        } catch {
          // 繼續等待
        }
      }
      
      await new Promise(resolve => setTimeout(resolve, 100))
    }
    
    throw new Error(`Server not ready within ${timeout}ms`)
  }
}