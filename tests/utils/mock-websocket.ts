/**
 * Mock WebSocket implementation for testing WebSocket functionality
 * without requiring actual network connections
 */

export interface MockWebSocketEventMap {
  'open': Event
  'close': CloseEvent
  'message': MessageEvent
  'error': Event
}

export type MockWebSocketEventListener<K extends keyof MockWebSocketEventMap> = 
  (event: MockWebSocketEventMap[K]) => void

export class MockWebSocket implements WebSocket {
  // WebSocket constants
  static readonly CONNECTING = 0
  static readonly OPEN = 1
  static readonly CLOSING = 2
  static readonly CLOSED = 3

  readonly CONNECTING = 0
  readonly OPEN = 1
  readonly CLOSING = 2
  readonly CLOSED = 3

  // WebSocket properties
  public url: string
  public readyState: number = MockWebSocket.CONNECTING
  public protocol: string = ''
  public extensions: string = ''
  public bufferedAmount: number = 0
  public binaryType: BinaryType = 'blob'

  // Event handlers
  public onopen: ((event: Event) => void) | null = null
  public onclose: ((event: CloseEvent) => void) | null = null
  public onmessage: ((event: MessageEvent) => void) | null = null
  public onerror: ((event: Event) => void) | null = null

  // Internal state
  private eventListeners: Map<string, Set<EventListener>> = new Map()
  private messageQueue: Array<string | ArrayBuffer | Blob> = []
  private connectionDelay: number = 0
  private shouldFailConnection: boolean = false
  private closeCode: number = 1000
  private closeReason: string = ''
  private networkDelay: number = 0

  constructor(url: string, protocols?: string | string[]) {
    this.url = url
    
    if (typeof protocols === 'string') {
      this.protocol = protocols
    } else if (Array.isArray(protocols) && protocols.length > 0) {
      this.protocol = protocols[0]
    }

    // Simulate connection establishment
    setTimeout(() => {
      this.establishConnection()
    }, this.connectionDelay)
  }

  /**
   * Send data through the WebSocket
   */
  send(data: string | ArrayBuffer | Blob): void {
    if (this.readyState !== MockWebSocket.OPEN) {
      throw new DOMException('WebSocket is not open', 'InvalidStateError')
    }

    // Simulate network delay
    setTimeout(() => {
      this.messageQueue.push(data)
      
      // Simulate message echo for testing
      const messageEvent = new MessageEvent('message', {
        data: data,
        origin: this.url,
        lastEventId: '',
        source: null,
        ports: []
      })

      this.dispatchEvent(messageEvent)
    }, this.networkDelay)
  }

  /**
   * Close the WebSocket connection
   */
  close(code?: number, reason?: string): void {
    if (this.readyState === MockWebSocket.CLOSED || this.readyState === MockWebSocket.CLOSING) {
      return
    }

    this.readyState = MockWebSocket.CLOSING
    this.closeCode = code || 1000
    this.closeReason = reason || ''

    setTimeout(() => {
      this.readyState = MockWebSocket.CLOSED
      
      const closeEvent = new CloseEvent('close', {
        code: this.closeCode,
        reason: this.closeReason,
        wasClean: this.closeCode === 1000
      })

      this.dispatchEvent(closeEvent)
    }, 10)
  }

  /**
   * Add event listener
   */
  addEventListener<K extends keyof MockWebSocketEventMap>(
    type: K,
    listener: MockWebSocketEventListener<K>,
    options?: boolean | AddEventListenerOptions
  ): void {
    if (!this.eventListeners.has(type)) {
      this.eventListeners.set(type, new Set())
    }
    this.eventListeners.get(type)!.add(listener as EventListener)
  }

  /**
   * Remove event listener
   */
  removeEventListener<K extends keyof MockWebSocketEventMap>(
    type: K,
    listener: MockWebSocketEventListener<K>,
    options?: boolean | EventListenerOptions
  ): void {
    const listeners = this.eventListeners.get(type)
    if (listeners) {
      listeners.delete(listener as EventListener)
    }
  }

  /**
   * Dispatch event to all listeners
   */
  dispatchEvent(event: Event): boolean {
    // Call the specific event handler
    switch (event.type) {
      case 'open':
        if (this.onopen) this.onopen(event)
        break
      case 'close':
        if (this.onclose) this.onclose(event as CloseEvent)
        break
      case 'message':
        if (this.onmessage) this.onmessage(event as MessageEvent)
        break
      case 'error':
        if (this.onerror) this.onerror(event)
        break
    }

    // Call all registered event listeners
    const listeners = this.eventListeners.get(event.type)
    if (listeners) {
      listeners.forEach(listener => {
        try {
          listener(event)
        } catch (error) {
          console.error('Error in WebSocket event listener:', error)
        }
      })
    }

    return true
  }

  /**
   * Establish the WebSocket connection
   */
  private establishConnection(): void {
    if (this.shouldFailConnection) {
      this.readyState = MockWebSocket.CLOSED
      const errorEvent = new Event('error')
      this.dispatchEvent(errorEvent)
      
      const closeEvent = new CloseEvent('close', {
        code: 1006,
        reason: 'Connection failed',
        wasClean: false
      })
      this.dispatchEvent(closeEvent)
      return
    }

    this.readyState = MockWebSocket.OPEN
    const openEvent = new Event('open')
    this.dispatchEvent(openEvent)
  }

  // Test utility methods

  /**
   * Simulate receiving a message from the server
   */
  simulateMessage(data: string | ArrayBuffer | Blob): void {
    if (this.readyState !== MockWebSocket.OPEN) {
      return
    }

    const messageEvent = new MessageEvent('message', {
      data: data,
      origin: this.url,
      lastEventId: '',
      source: null,
      ports: []
    })

    setTimeout(() => {
      this.dispatchEvent(messageEvent)
    }, this.networkDelay)
  }

  /**
   * Simulate connection error
   */
  simulateError(errorMessage?: string): void {
    const errorEvent = new Event('error')
    ;(errorEvent as any).message = errorMessage || 'WebSocket error'
    this.dispatchEvent(errorEvent)
  }

  /**
   * Simulate server-initiated close
   */
  simulateServerClose(code: number = 1000, reason: string = ''): void {
    if (this.readyState !== MockWebSocket.OPEN) {
      return
    }

    this.readyState = MockWebSocket.CLOSING
    
    setTimeout(() => {
      this.readyState = MockWebSocket.CLOSED
      
      const closeEvent = new CloseEvent('close', {
        code: code,
        reason: reason,
        wasClean: code === 1000
      })

      this.dispatchEvent(closeEvent)
    }, 10)
  }

  /**
   * Set connection delay for testing
   */
  setConnectionDelay(delay: number): void {
    this.connectionDelay = delay
  }

  /**
   * Set network delay for message simulation
   */
  setNetworkDelay(delay: number): void {
    this.networkDelay = delay
  }

  /**
   * Configure connection to fail
   */
  setConnectionFailure(shouldFail: boolean): void {
    this.shouldFailConnection = shouldFail
  }

  /**
   * Get sent messages for testing
   */
  getSentMessages(): Array<string | ArrayBuffer | Blob> {
    return [...this.messageQueue]
  }

  /**
   * Clear message queue
   */
  clearMessageQueue(): void {
    this.messageQueue = []
  }

  /**
   * Reset the mock WebSocket to initial state
   */
  reset(): void {
    this.readyState = MockWebSocket.CONNECTING
    this.messageQueue = []
    this.eventListeners.clear()
    this.onopen = null
    this.onclose = null
    this.onmessage = null
    this.onerror = null
    this.connectionDelay = 0
    this.networkDelay = 0
    this.shouldFailConnection = false
    this.closeCode = 1000
    this.closeReason = ''
  }
}

/**
 * Global WebSocket mock for testing
 */
export function mockWebSocket(): void {
  ;(global as any).WebSocket = MockWebSocket
  ;(window as any).WebSocket = MockWebSocket
}

/**
 * Restore original WebSocket implementation
 */
export function restoreWebSocket(): void {
  // Note: In a real implementation, you would store the original WebSocket
  // and restore it here. For testing purposes, we'll just delete the mock.
  delete (global as any).WebSocket
  delete (window as any).WebSocket
}