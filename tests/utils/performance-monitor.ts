import { writeFile, readFile } from 'fs/promises'
import { join } from 'path'
import { existsSync } from 'fs'

export interface PerformanceMetrics {
  totalExecutionTime: number
  averageTestTime: number
  slowestTests: TestPerformance[]
  fastestTests: TestPerformance[]
  memoryUsage: MemoryMetrics
  testDistribution: TestDistribution
  regressionAlerts: RegressionAlert[]
  trends: PerformanceTrends
}

export interface TestPerformance {
  name: string
  file: string
  duration: number
  memoryDelta: number
  category: 'unit' | 'integration' | 'server'
  timestamp: Date
}

export interface MemoryMetrics {
  heapUsed: number
  heapTotal: number
  external: number
  rss: number
  peak: number
  gc: {
    collections: number
    duration: number
  }
}

export interface TestDistribution {
  unit: number
  integration: number
  server: number
  total: number
}

export interface RegressionAlert {
  testName: string
  file: string
  currentDuration: number
  previousDuration: number
  regressionPercentage: number
  severity: 'low' | 'medium' | 'high' | 'critical'
  threshold: number
  recommendations: string[]
}

export interface PerformanceTrends {
  executionTime: TrendPoint[]
  memoryUsage: TrendPoint[]
  testCount: TrendPoint[]
  regressionCount: TrendPoint[]
}

export interface TrendPoint {
  timestamp: Date
  value: number
  label?: string
}

export interface PerformanceConfig {
  historyFile: string
  regressionThresholds: {
    low: number      // 10% slower
    medium: number   // 25% slower
    high: number     // 50% slower
    critical: number // 100% slower
  }
  slowTestThreshold: number // milliseconds
  memoryThreshold: number   // MB
  maxHistoryEntries: number
}

export class PerformanceMonitor {
  private config: PerformanceConfig
  private currentMetrics: PerformanceMetrics | null = null
  private historicalData: PerformanceHistory[] = []

  constructor(config: Partial<PerformanceConfig> = {}) {
    this.config = {
      historyFile: 'test-performance-history.json',
      regressionThresholds: {
        low: 10,      // 10% slower
        medium: 25,   // 25% slower
        high: 50,     // 50% slower
        critical: 100 // 100% slower
      },
      slowTestThreshold: 1000, // 1 second
      memoryThreshold: 100,    // 100 MB
      maxHistoryEntries: 100,
      ...config
    }
  }

  /**
   * Start performance monitoring session
   */
  startMonitoring(): PerformanceSession {
    const session = new PerformanceSession()
    session.start()
    return session
  }

  /**
   * Record test performance
   */
  recordTestPerformance(testName: string, file: string, duration: number, category: 'unit' | 'integration' | 'server'): void {
    const memoryUsage = process.memoryUsage()
    
    const performance: TestPerformance = {
      name: testName,
      file,
      duration,
      memoryDelta: memoryUsage.heapUsed,
      category,
      timestamp: new Date()
    }

    // Store for analysis
    if (!this.currentMetrics) {
      this.initializeMetrics()
    }

    this.updateMetrics(performance)
  }

  /**
   * Analyze performance and detect regressions
   */
  async analyzePerformance(): Promise<PerformanceMetrics> {
    if (!this.currentMetrics) {
      throw new Error('No performance data collected')
    }

    // Load historical data
    await this.loadHistoricalData()

    // Detect regressions
    this.currentMetrics.regressionAlerts = this.detectRegressions()

    // Calculate trends
    this.currentMetrics.trends = this.calculateTrends()

    // Save current metrics to history
    await this.saveToHistory()

    return this.currentMetrics
  }

  /**
   * Generate performance report
   */
  async generatePerformanceReport(metrics: PerformanceMetrics): Promise<string> {
    const htmlContent = this.generatePerformanceHTML(metrics)
    const filePath = join('test-reports', 'performance-report.html')
    
    await writeFile(filePath, htmlContent, 'utf-8')
    return filePath
  }

  /**
   * Check for performance regressions
   */
  detectRegressions(): RegressionAlert[] {
    const alerts: RegressionAlert[] = []
    
    if (this.historicalData.length === 0) {
      return alerts // No historical data to compare
    }

    const previousRun = this.historicalData[this.historicalData.length - 1]
    const currentTests = this.getAllCurrentTests()

    for (const currentTest of currentTests) {
      const previousTest = previousRun.tests.find(t => 
        t.name === currentTest.name && t.file === currentTest.file
      )

      if (previousTest) {
        const regressionPercentage = ((currentTest.duration - previousTest.duration) / previousTest.duration) * 100

        if (regressionPercentage > this.config.regressionThresholds.low) {
          const severity = this.calculateRegressionSeverity(regressionPercentage)
          
          alerts.push({
            testName: currentTest.name,
            file: currentTest.file,
            currentDuration: currentTest.duration,
            previousDuration: previousTest.duration,
            regressionPercentage,
            severity,
            threshold: this.getThresholdForSeverity(severity),
            recommendations: this.generateRegressionRecommendations(currentTest, regressionPercentage)
          })
        }
      }
    }

    return alerts.sort((a, b) => b.regressionPercentage - a.regressionPercentage)
  }

  /**
   * Calculate trends from historical data
   */
  private calculateTrends(): PerformanceTrends {
    const trends: PerformanceTrends = {
      executionTime: [],
      memoryUsage: [],
      testCount: [],
      regressionCount: []
    }

    // Calculate trends from last 10 runs
    const recentHistory = this.historicalData.slice(-10)
    
    for (const entry of recentHistory) {
      trends.executionTime.push({
        timestamp: entry.timestamp,
        value: entry.totalExecutionTime
      })
      
      trends.memoryUsage.push({
        timestamp: entry.timestamp,
        value: entry.memoryUsage.heapUsed / 1024 / 1024 // Convert to MB
      })
      
      trends.testCount.push({
        timestamp: entry.timestamp,
        value: entry.testCount
      })
      
      trends.regressionCount.push({
        timestamp: entry.timestamp,
        value: entry.regressionCount || 0
      })
    }

    return trends
  }

  /**
   * Initialize metrics structure
   */
  private initializeMetrics(): void {
    this.currentMetrics = {
      totalExecutionTime: 0,
      averageTestTime: 0,
      slowestTests: [],
      fastestTests: [],
      memoryUsage: {
        heapUsed: 0,
        heapTotal: 0,
        external: 0,
        rss: 0,
        peak: 0,
        gc: { collections: 0, duration: 0 }
      },
      testDistribution: {
        unit: 0,
        integration: 0,
        server: 0,
        total: 0
      },
      regressionAlerts: [],
      trends: {
        executionTime: [],
        memoryUsage: [],
        testCount: [],
        regressionCount: []
      }
    }
  }

  /**
   * Update metrics with new test performance
   */
  private updateMetrics(performance: TestPerformance): void {
    if (!this.currentMetrics) return

    // Update totals
    this.currentMetrics.totalExecutionTime += performance.duration
    this.currentMetrics.testDistribution[performance.category]++
    this.currentMetrics.testDistribution.total++

    // Update average
    this.currentMetrics.averageTestTime = 
      this.currentMetrics.totalExecutionTime / this.currentMetrics.testDistribution.total

    // Update slowest/fastest tests
    this.updateTestRankings(performance)

    // Update memory metrics
    const memoryUsage = process.memoryUsage()
    this.currentMetrics.memoryUsage = {
      heapUsed: memoryUsage.heapUsed,
      heapTotal: memoryUsage.heapTotal,
      external: memoryUsage.external,
      rss: memoryUsage.rss,
      peak: Math.max(this.currentMetrics.memoryUsage.peak, memoryUsage.heapUsed),
      gc: this.currentMetrics.memoryUsage.gc // GC data would need separate collection
    }
  }

  /**
   * Update slowest/fastest test rankings
   */
  private updateTestRankings(performance: TestPerformance): void {
    if (!this.currentMetrics) return

    // Update slowest tests
    this.currentMetrics.slowestTests.push(performance)
    this.currentMetrics.slowestTests.sort((a, b) => b.duration - a.duration)
    this.currentMetrics.slowestTests = this.currentMetrics.slowestTests.slice(0, 10)

    // Update fastest tests
    this.currentMetrics.fastestTests.push(performance)
    this.currentMetrics.fastestTests.sort((a, b) => a.duration - b.duration)
    this.currentMetrics.fastestTests = this.currentMetrics.fastestTests.slice(0, 10)
  }

  /**
   * Get all current tests for regression analysis
   */
  private getAllCurrentTests(): TestPerformance[] {
    if (!this.currentMetrics) return []
    
    return [
      ...this.currentMetrics.slowestTests,
      ...this.currentMetrics.fastestTests
    ].filter((test, index, array) => 
      array.findIndex(t => t.name === test.name && t.file === test.file) === index
    )
  }

  /**
   * Calculate regression severity
   */
  private calculateRegressionSeverity(percentage: number): 'low' | 'medium' | 'high' | 'critical' {
    if (percentage >= this.config.regressionThresholds.critical) return 'critical'
    if (percentage >= this.config.regressionThresholds.high) return 'high'
    if (percentage >= this.config.regressionThresholds.medium) return 'medium'
    return 'low'
  }

  /**
   * Get threshold value for severity level
   */
  private getThresholdForSeverity(severity: 'low' | 'medium' | 'high' | 'critical'): number {
    return this.config.regressionThresholds[severity]
  }

  /**
   * Generate recommendations for performance regression
   */
  private generateRegressionRecommendations(test: TestPerformance, regressionPercentage: number): string[] {
    const recommendations: string[] = []

    if (regressionPercentage > 50) {
      recommendations.push('Review recent changes that might affect test performance')
      recommendations.push('Check for memory leaks or inefficient operations')
    }

    if (test.duration > this.config.slowTestThreshold) {
      recommendations.push('Consider breaking down large test into smaller units')
      recommendations.push('Optimize test setup and teardown operations')
    }

    if (test.category === 'integration') {
      recommendations.push('Review database operations and API calls for efficiency')
      recommendations.push('Consider using test doubles for external dependencies')
    }

    if (test.memoryDelta > this.config.memoryThreshold * 1024 * 1024) {
      recommendations.push('Investigate potential memory leaks in test or code under test')
      recommendations.push('Ensure proper cleanup of resources after test completion')
    }

    return recommendations
  }

  /**
   * Load historical performance data
   */
  private async loadHistoricalData(): Promise<void> {
    try {
      if (existsSync(this.config.historyFile)) {
        const historyContent = await readFile(this.config.historyFile, 'utf-8')
        this.historicalData = JSON.parse(historyContent)
      }
    } catch (error) {
      console.warn(`Failed to load performance history: ${error.message}`)
      this.historicalData = []
    }
  }

  /**
   * Save current metrics to history
   */
  private async saveToHistory(): Promise<void> {
    if (!this.currentMetrics) return

    const historyEntry: PerformanceHistory = {
      timestamp: new Date(),
      totalExecutionTime: this.currentMetrics.totalExecutionTime,
      averageTestTime: this.currentMetrics.averageTestTime,
      testCount: this.currentMetrics.testDistribution.total,
      memoryUsage: this.currentMetrics.memoryUsage,
      regressionCount: this.currentMetrics.regressionAlerts.length,
      tests: this.getAllCurrentTests()
    }

    this.historicalData.push(historyEntry)

    // Keep only recent entries
    if (this.historicalData.length > this.config.maxHistoryEntries) {
      this.historicalData = this.historicalData.slice(-this.config.maxHistoryEntries)
    }

    try {
      await writeFile(this.config.historyFile, JSON.stringify(this.historicalData, null, 2), 'utf-8')
    } catch (error) {
      console.warn(`Failed to save performance history: ${error.message}`)
    }
  }

  /**
   * Generate performance HTML report
   */
  private generatePerformanceHTML(metrics: PerformanceMetrics): string {
    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Performance Report</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); color: white; padding: 30px; border-radius: 8px 8px 0 0; }
        .header h1 { margin: 0; font-size: 2.5em; }
        .content { padding: 30px; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .metric { background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center; border-left: 4px solid #3b82f6; }
        .metric h3 { margin: 0 0 10px 0; color: #666; font-size: 0.9em; text-transform: uppercase; }
        .metric .value { font-size: 2em; font-weight: bold; color: #333; }
        .section { margin-bottom: 40px; }
        .section h2 { color: #333; border-bottom: 2px solid #eee; padding-bottom: 10px; }
        .alert { padding: 15px; border-radius: 8px; margin-bottom: 15px; }
        .alert.critical { background: #fee2e2; border-left: 4px solid #ef4444; }
        .alert.high { background: #fef3c7; border-left: 4px solid #f59e0b; }
        .alert.medium { background: #dbeafe; border-left: 4px solid #3b82f6; }
        .alert.low { background: #f3f4f6; border-left: 4px solid #6b7280; }
        .test-list { margin-top: 20px; }
        .test-item { background: #f8f9fa; margin-bottom: 10px; padding: 15px; border-radius: 8px; display: flex; justify-content: space-between; align-items: center; }
        .test-name { font-weight: bold; }
        .test-file { color: #666; font-size: 0.9em; }
        .test-duration { font-weight: bold; color: #3b82f6; }
        .memory-info { background: #f0f9ff; padding: 20px; border-radius: 8px; margin-top: 20px; }
        .memory-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; }
        .memory-item { text-align: center; }
        .memory-item .label { font-size: 0.8em; color: #666; text-transform: uppercase; }
        .memory-item .value { font-size: 1.5em; font-weight: bold; color: #333; }
        .recommendations { background: #f0f9ff; border: 1px solid #3b82f6; border-radius: 8px; padding: 20px; }
        .recommendations ul { margin: 0; padding-left: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Performance Report</h1>
            <div>Generated: ${new Date().toLocaleString()}</div>
        </div>
        
        <div class="content">
            <div class="section">
                <h2>Performance Summary</h2>
                <div class="summary">
                    <div class="metric">
                        <h3>Total Time</h3>
                        <div class="value">${(metrics.totalExecutionTime / 1000).toFixed(2)}s</div>
                    </div>
                    <div class="metric">
                        <h3>Average Test</h3>
                        <div class="value">${metrics.averageTestTime.toFixed(0)}ms</div>
                    </div>
                    <div class="metric">
                        <h3>Total Tests</h3>
                        <div class="value">${metrics.testDistribution.total}</div>
                    </div>
                    <div class="metric">
                        <h3>Memory Peak</h3>
                        <div class="value">${(metrics.memoryUsage.peak / 1024 / 1024).toFixed(1)}MB</div>
                    </div>
                </div>
            </div>

            ${metrics.regressionAlerts.length > 0 ? `
            <div class="section">
                <h2>Performance Regressions (${metrics.regressionAlerts.length})</h2>
                ${metrics.regressionAlerts.map(alert => `
                    <div class="alert ${alert.severity}">
                        <h4>${alert.testName}</h4>
                        <p><strong>File:</strong> ${alert.file}</p>
                        <p><strong>Regression:</strong> ${alert.regressionPercentage.toFixed(1)}% slower (${alert.previousDuration}ms → ${alert.currentDuration}ms)</p>
                        <div class="recommendations">
                            <strong>Recommendations:</strong>
                            <ul>
                                ${alert.recommendations.map(rec => `<li>${rec}</li>`).join('')}
                            </ul>
                        </div>
                    </div>
                `).join('')}
            </div>
            ` : ''}

            <div class="section">
                <h2>Slowest Tests</h2>
                <div class="test-list">
                    ${metrics.slowestTests.slice(0, 10).map(test => `
                        <div class="test-item">
                            <div>
                                <div class="test-name">${test.name}</div>
                                <div class="test-file">${test.file}</div>
                            </div>
                            <div class="test-duration">${test.duration}ms</div>
                        </div>
                    `).join('')}
                </div>
            </div>

            <div class="section">
                <h2>Memory Usage</h2>
                <div class="memory-info">
                    <div class="memory-grid">
                        <div class="memory-item">
                            <div class="label">Heap Used</div>
                            <div class="value">${(metrics.memoryUsage.heapUsed / 1024 / 1024).toFixed(1)}MB</div>
                        </div>
                        <div class="memory-item">
                            <div class="label">Heap Total</div>
                            <div class="value">${(metrics.memoryUsage.heapTotal / 1024 / 1024).toFixed(1)}MB</div>
                        </div>
                        <div class="memory-item">
                            <div class="label">RSS</div>
                            <div class="value">${(metrics.memoryUsage.rss / 1024 / 1024).toFixed(1)}MB</div>
                        </div>
                        <div class="memory-item">
                            <div class="label">External</div>
                            <div class="value">${(metrics.memoryUsage.external / 1024 / 1024).toFixed(1)}MB</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="section">
                <h2>Test Distribution</h2>
                <div class="summary">
                    <div class="metric">
                        <h3>Unit Tests</h3>
                        <div class="value">${metrics.testDistribution.unit}</div>
                    </div>
                    <div class="metric">
                        <h3>Integration Tests</h3>
                        <div class="value">${metrics.testDistribution.integration}</div>
                    </div>
                    <div class="metric">
                        <h3>Server Tests</h3>
                        <div class="value">${metrics.testDistribution.server}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
    `.trim()
  }
}

/**
 * Performance monitoring session
 */
export class PerformanceSession {
  private startTime: number = 0
  private endTime: number = 0
  private memoryStart: NodeJS.MemoryUsage | null = null

  start(): void {
    this.startTime = performance.now()
    this.memoryStart = process.memoryUsage()
  }

  end(): { duration: number; memoryDelta: number } {
    this.endTime = performance.now()
    const memoryEnd = process.memoryUsage()
    
    return {
      duration: this.endTime - this.startTime,
      memoryDelta: this.memoryStart ? memoryEnd.heapUsed - this.memoryStart.heapUsed : 0
    }
  }

  getDuration(): number {
    return this.endTime > 0 ? this.endTime - this.startTime : performance.now() - this.startTime
  }
}

// Additional interfaces
export interface PerformanceHistory {
  timestamp: Date
  totalExecutionTime: number
  averageTestTime: number
  testCount: number
  memoryUsage: MemoryMetrics
  regressionCount: number
  tests: TestPerformance[]
}
