import { existsSync, readFileSync, writeFileSync, mkdirSync } from 'fs'
import { resolve } from 'path'
import { execSync } from 'child_process'
import { PerformanceMonitor } from './performance-monitor'

/**
 * 測試品質報告生成器
 * 用於生成測試品質報告，包括測試覆蓋率、測試執行時間和測試結果
 */
export class TestQualityReporter {
  private performanceMonitor: PerformanceMonitor
  private testResultsDir: string
  private coverageDir: string
  private qualityReportPath: string
  private thresholds: {
    coverage: {
      lines: number
      functions: number
      branches: number
      statements: number
    }
    performance: {
      maxTestDuration: number
      maxSuiteDuration: number
    }
  }

  /**
   * 建立測試品質報告生成器
   * @param options 選項
   */
  constructor(options: {
    testResultsDir?: string
    coverageDir?: string
    qualityReportPath?: string
    thresholds?: {
      coverage?: {
        lines?: number
        functions?: number
        branches?: number
        statements?: number
      }
      performance?: {
        maxTestDuration?: number
        maxSuiteDuration?: number
      }
    }
  } = {}) {
    this.performanceMonitor = new PerformanceMonitor()
    this.testResultsDir = options.testResultsDir || resolve(process.cwd(), 'test-results')
    this.coverageDir = options.coverageDir || resolve(process.cwd(), 'coverage')
    this.qualityReportPath = options.qualityReportPath || resolve(this.testResultsDir, 'quality-report.json')
    
    // 設置預設閾值
    this.thresholds = {
      coverage: {
        lines: options.thresholds?.coverage?.lines || 70,
        functions: options.thresholds?.coverage?.functions || 70,
        branches: options.thresholds?.coverage?.branches || 60,
        statements: options.thresholds?.coverage?.statements || 70
      },
      performance: {
        maxTestDuration: options.thresholds?.performance?.maxTestDuration || 5000,
        maxSuiteDuration: options.thresholds?.performance?.maxSuiteDuration || 30000
      }
    }
    
    // 確保目錄存在
    this.ensureDirectoriesExist()
  }

  /**
   * 確保必要的目錄存在
   */
  private ensureDirectoriesExist(): void {
    if (!existsSync(this.testResultsDir)) {
      mkdirSync(this.testResultsDir, { recursive: true })
    }
  }

  /**
   * 執行測試並收集結果
   * @param testCommand 測試命令
   * @returns 測試結果
   */
  public runTests(testCommand: string): { success: boolean; output: string } {
    try {
      // 開始計時
      this.performanceMonitor.start('test-execution')
      
      // 執行測試命令
      const output = execSync(testCommand, { encoding: 'utf-8' })
      
      // 停止計時
      const performance = this.performanceMonitor.stop('test-execution')
      
      return {
        success: true,
        output
      }
    } catch (error) {
      // 停止計時
      this.performanceMonitor.stop('test-execution')
      
      return {
        success: false,
        output: error instanceof Error ? error.message : String(error)
      }
    }
  }

  /**
   * 收集覆蓋率數據
   * @returns 覆蓋率數據
   */
  public collectCoverageData(): {
    lines: { covered: number; total: number; percentage: number }
    functions: { covered: number; total: number; percentage: number }
    branches: { covered: number; total: number; percentage: number }
    statements: { covered: number; total: number; percentage: number }
  } | null {
    try {
      // 檢查覆蓋率報告是否存在
      const coverageSummaryPath = resolve(this.coverageDir, 'coverage-summary.json')
      
      if (!existsSync(coverageSummaryPath)) {
        return null
      }
      
      // 讀取覆蓋率報告
      const coverageSummary = JSON.parse(readFileSync(coverageSummaryPath, 'utf-8'))
      const total = coverageSummary.total
      
      return {
        lines: {
          covered: total.lines.covered,
          total: total.lines.total,
          percentage: total.lines.pct
        },
        functions: {
          covered: total.functions.covered,
          total: total.functions.total,
          percentage: total.functions.pct
        },
        branches: {
          covered: total.branches.covered,
          total: total.branches.total,
          percentage: total.branches.pct
        },
        statements: {
          covered: total.statements.covered,
          total: total.statements.total,
          percentage: total.statements.pct
        }
      }
    } catch (error) {
      console.error('無法收集覆蓋率數據:', error)
      return null
    }
  }

  /**
   * 收集測試結果數據
   * @returns 測試結果數據
   */
  public collectTestResults(): {
    total: number
    passed: number
    failed: number
    skipped: number
    duration: number
  } | null {
    try {
      // 檢查測試結果報告是否存在
      const junitPath = resolve(this.testResultsDir, 'junit.xml')
      
      if (!existsSync(junitPath)) {
        return null
      }
      
      // 讀取測試結果報告
      const junitContent = readFileSync(junitPath, 'utf-8')
      
      // 解析 XML 內容（簡單解析，實際應使用 XML 解析庫）
      const totalMatch = junitContent.match(/tests="(\d+)"/)
      const failuresMatch = junitContent.match(/failures="(\d+)"/)
      const errorsMatch = junitContent.match(/errors="(\d+)"/)
      const timeMatch = junitContent.match(/time="([\d\.]+)"/)
      
      if (!totalMatch || !failuresMatch || !errorsMatch || !timeMatch) {
        return null
      }
      
      const total = parseInt(totalMatch[1])
      const failures = parseInt(failuresMatch[1])
      const errors = parseInt(errorsMatch[1])
      const time = parseFloat(timeMatch[1])
      
      return {
        total,
        passed: total - failures - errors,
        failed: failures + errors,
        skipped: 0, // JUnit XML 不一定包含跳過的測試數量
        duration: time * 1000 // 轉換為毫秒
      }
    } catch (error) {
      console.error('無法收集測試結果數據:', error)
      return null
    }
  }

  /**
   * 收集效能數據
   * @returns 效能數據
   */
  public collectPerformanceData(): {
    slowTests: Array<{ name: string; duration: number }>
    totalDuration: number
    averageDuration: number
  } {
    // 這裡我們使用 PerformanceMonitor 收集的數據
    // 在實際應用中，可以從測試結果中提取更詳細的效能數據
    const performanceData = this.performanceMonitor.getAll()
    
    // 找出慢測試（這裡是模擬數據）
    const slowTests = [
      { name: 'slow-test-1', duration: 2000 },
      { name: 'slow-test-2', duration: 1500 }
    ]
    
    // 計算總時間和平均時間
    const totalDuration = performanceData.reduce((total, data) => total + data.duration, 0)
    const averageDuration = performanceData.length > 0 ? totalDuration / performanceData.length : 0
    
    return {
      slowTests,
      totalDuration,
      averageDuration
    }
  }

  /**
   * 生成測試品質報告
   * @returns 測試品質報告
   */
  public generateQualityReport(): {
    timestamp: string
    coverage: ReturnType<TestQualityReporter['collectCoverageData']>
    testResults: ReturnType<TestQualityReporter['collectTestResults']>
    performance: ReturnType<TestQualityReporter['collectPerformanceData']>
    thresholds: typeof this.thresholds
    quality: {
      coverageScore: number
      performanceScore: number
      stabilityScore: number
      overallScore: number
    }
  } {
    // 收集數據
    const coverage = this.collectCoverageData()
    const testResults = this.collectTestResults()
    const performance = this.collectPerformanceData()
    
    // 計算品質分數
    const coverageScore = this.calculateCoverageScore(coverage)
    const performanceScore = this.calculatePerformanceScore(performance)
    const stabilityScore = this.calculateStabilityScore(testResults)
    
    // 計算總體分數
    const overallScore = (coverageScore + performanceScore + stabilityScore) / 3
    
    // 生成報告
    const report = {
      timestamp: new Date().toISOString(),
      coverage,
      testResults,
      performance,
      thresholds: this.thresholds,
      quality: {
        coverageScore,
        performanceScore,
        stabilityScore,
        overallScore
      }
    }
    
    // 保存報告
    writeFileSync(this.qualityReportPath, JSON.stringify(report, null, 2))
    
    return report
  }

  /**
   * 計算覆蓋率分數
   * @param coverage 覆蓋率數據
   * @returns 覆蓋率分數 (0-100)
   */
  private calculateCoverageScore(coverage: ReturnType<TestQualityReporter['collectCoverageData']>): number {
    if (!coverage) {
      return 0
    }
    
    // 計算各項覆蓋率與閾值的比例
    const linesScore = Math.min(100, (coverage.lines.percentage / this.thresholds.coverage.lines) * 100)
    const functionsScore = Math.min(100, (coverage.functions.percentage / this.thresholds.coverage.functions) * 100)
    const branchesScore = Math.min(100, (coverage.branches.percentage / this.thresholds.coverage.branches) * 100)
    const statementsScore = Math.min(100, (coverage.statements.percentage / this.thresholds.coverage.statements) * 100)
    
    // 計算平均分數
    return (linesScore + functionsScore + branchesScore + statementsScore) / 4
  }

  /**
   * 計算效能分數
   * @param performance 效能數據
   * @returns 效能分數 (0-100)
   */
  private calculatePerformanceScore(performance: ReturnType<TestQualityReporter['collectPerformanceData']>): number {
    // 計算慢測試的比例
    const slowTestsRatio = performance.slowTests.length > 0 ? 
      performance.slowTests.length / (performance.totalDuration / performance.averageDuration) : 0
    
    // 計算總時間與閾值的比例
    const durationScore = Math.min(100, (this.thresholds.performance.maxSuiteDuration / performance.totalDuration) * 100)
    
    // 計算平均分數
    return Math.max(0, 100 - (slowTestsRatio * 100) * 0.3 + durationScore * 0.7)
  }

  /**
   * 計算穩定性分數
   * @param testResults 測試結果數據
   * @returns 穩定性分數 (0-100)
   */
  private calculateStabilityScore(testResults: ReturnType<TestQualityReporter['collectTestResults']>): number {
    if (!testResults) {
      return 0
    }
    
    // 計算通過率
    const passRate = testResults.total > 0 ? (testResults.passed / testResults.total) * 100 : 0
    
    // 計算穩定性分數
    return passRate
  }

  /**
   * 生成 HTML 報告
   * @returns HTML 報告路徑
   */
  public generateHtmlReport(): string {
    // 讀取 JSON 報告
    const reportPath = this.qualityReportPath
    
    if (!existsSync(reportPath)) {
      throw new Error('找不到測試品質報告')
    }
    
    const report = JSON.parse(readFileSync(reportPath, 'utf-8'))
    
    // 生成 HTML 報告
    const htmlReportPath = resolve(this.testResultsDir, 'quality-report.html')
    
    const html = `
<!DOCTYPE html>
<html lang="zh-TW">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>測試品質報告</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    h1, h2, h3 {
      color: #2c3e50;
    }
    .report-header {
      margin-bottom: 30px;
      border-bottom: 1px solid #eee;
      padding-bottom: 20px;
    }
    .report-section {
      margin-bottom: 30px;
    }
    .score-card {
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      gap: 20px;
      margin-bottom: 20px;
    }
    .score-item {
      flex: 1;
      min-width: 200px;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }
    .score-value {
      font-size: 2em;
      font-weight: bold;
      margin: 10px 0;
    }
    .good { background-color: #d4edda; color: #155724; }
    .warning { background-color: #fff3cd; color: #856404; }
    .danger { background-color: #f8d7da; color: #721c24; }
    table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 20px;
    }
    th, td {
      padding: 12px 15px;
      text-align: left;
      border-bottom: 1px solid #ddd;
    }
    th {
      background-color: #f8f9fa;
    }
    .progress-bar {
      height: 20px;
      background-color: #e9ecef;
      border-radius: 5px;
      overflow: hidden;
      margin-bottom: 10px;
    }
    .progress-bar-fill {
      height: 100%;
      background-color: #4caf50;
    }
    .timestamp {
      color: #6c757d;
      font-style: italic;
    }
  </style>
</head>
<body>
  <div class="report-header">
    <h1>測試品質報告</h1>
    <p class="timestamp">生成時間: ${new Date(report.timestamp).toLocaleString()}</p>
  </div>
  
  <div class="report-section">
    <h2>總體品質分數</h2>
    <div class="score-card">
      <div class="score-item ${report.quality.overallScore >= 80 ? 'good' : report.quality.overallScore >= 60 ? 'warning' : 'danger'}">
        <h3>總體分數</h3>
        <div class="score-value">${report.quality.overallScore.toFixed(1)}</div>
        <div class="progress-bar">
          <div class="progress-bar-fill" style="width: ${report.quality.overallScore}%"></div>
        </div>
      </div>
      <div class="score-item ${report.quality.coverageScore >= 80 ? 'good' : report.quality.coverageScore >= 60 ? 'warning' : 'danger'}">
        <h3>覆蓋率分數</h3>
        <div class="score-value">${report.quality.coverageScore.toFixed(1)}</div>
        <div class="progress-bar">
          <div class="progress-bar-fill" style="width: ${report.quality.coverageScore}%"></div>
        </div>
      </div>
      <div class="score-item ${report.quality.performanceScore >= 80 ? 'good' : report.quality.performanceScore >= 60 ? 'warning' : 'danger'}">
        <h3>效能分數</h3>
        <div class="score-value">${report.quality.performanceScore.toFixed(1)}</div>
        <div class="progress-bar">
          <div class="progress-bar-fill" style="width: ${report.quality.performanceScore}%"></div>
        </div>
      </div>
      <div class="score-item ${report.quality.stabilityScore >= 80 ? 'good' : report.quality.stabilityScore >= 60 ? 'warning' : 'danger'}">
        <h3>穩定性分數</h3>
        <div class="score-value">${report.quality.stabilityScore.toFixed(1)}</div>
        <div class="progress-bar">
          <div class="progress-bar-fill" style="width: ${report.quality.stabilityScore}%"></div>
        </div>
      </div>
    </div>
  </div>
  
  ${report.coverage ? `
  <div class="report-section">
    <h2>測試覆蓋率</h2>
    <table>
      <thead>
        <tr>
          <th>類型</th>
          <th>覆蓋率</th>
          <th>已覆蓋</th>
          <th>總數</th>
          <th>閾值</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td>行覆蓋率</td>
          <td>${report.coverage.lines.percentage.toFixed(2)}%</td>
          <td>${report.coverage.lines.covered}</td>
          <td>${report.coverage.lines.total}</td>
          <td>${report.thresholds.coverage.lines}%</td>
        </tr>
        <tr>
          <td>函數覆蓋率</td>
          <td>${report.coverage.functions.percentage.toFixed(2)}%</td>
          <td>${report.coverage.functions.covered}</td>
          <td>${report.coverage.functions.total}</td>
          <td>${report.thresholds.coverage.functions}%</td>
        </tr>
        <tr>
          <td>分支覆蓋率</td>
          <td>${report.coverage.branches.percentage.toFixed(2)}%</td>
          <td>${report.coverage.branches.covered}</td>
          <td>${report.coverage.branches.total}</td>
          <td>${report.thresholds.coverage.branches}%</td>
        </tr>
        <tr>
          <td>語句覆蓋率</td>
          <td>${report.coverage.statements.percentage.toFixed(2)}%</td>
          <td>${report.coverage.statements.covered}</td>
          <td>${report.coverage.statements.total}</td>
          <td>${report.thresholds.coverage.statements}%</td>
        </tr>
      </tbody>
    </table>
  </div>
  ` : ''}
  
  ${report.testResults ? `
  <div class="report-section">
    <h2>測試結果</h2>
    <table>
      <thead>
        <tr>
          <th>總測試數</th>
          <th>通過</th>
          <th>失敗</th>
          <th>跳過</th>
          <th>執行時間</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td>${report.testResults.total}</td>
          <td>${report.testResults.passed}</td>
          <td>${report.testResults.failed}</td>
          <td>${report.testResults.skipped}</td>
          <td>${(report.testResults.duration / 1000).toFixed(2)}秒</td>
        </tr>
      </tbody>
    </table>
  </div>
  ` : ''}
  
  <div class="report-section">
    <h2>效能分析</h2>
    <h3>慢測試</h3>
    <table>
      <thead>
        <tr>
          <th>測試名稱</th>
          <th>執行時間</th>
        </tr>
      </thead>
      <tbody>
        ${report.performance.slowTests.map(test => `
        <tr>
          <td>${test.name}</td>
          <td>${(test.duration / 1000).toFixed(2)}秒</td>
        </tr>
        `).join('')}
      </tbody>
    </table>
    
    <h3>總體效能</h3>
    <table>
      <thead>
        <tr>
          <th>總執行時間</th>
          <th>平均執行時間</th>
          <th>最大執行時間閾值</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td>${(report.performance.totalDuration / 1000).toFixed(2)}秒</td>
          <td>${(report.performance.averageDuration / 1000).toFixed(2)}秒</td>
          <td>${(report.thresholds.performance.maxSuiteDuration / 1000).toFixed(2)}秒</td>
        </tr>
      </tbody>
    </table>
  </div>
</body>
</html>
    `
    
    writeFileSync(htmlReportPath, html)
    
    return htmlReportPath
  }
}