// 通用測試輔助函數
import { vi } from 'vitest'

/**
 * 等待條件滿足的輔助函數
 */
export async function waitFor(
  condition: () => boolean | Promise<boolean>,
  timeout: number = 5000,
  interval: number = 100
): Promise<void> {
  const startTime = Date.now()
  
  while (Date.now() - startTime < timeout) {
    const result = await condition()
    if (result) {
      return
    }
    await new Promise(resolve => setTimeout(resolve, interval))
  }
  
  throw new Error(`Condition not met within ${timeout}ms`)
}

/**
 * 等待 DOM 元素出現
 */
export async function waitForElement(
  selector: string,
  timeout: number = 5000
): Promise<Element> {
  return waitFor(
    () => {
      const element = document.querySelector(selector)
      return element !== null
    },
    timeout
  ).then(() => {
    const element = document.querySelector(selector)
    if (!element) {
      throw new Error(`Element with selector "${selector}" not found`)
    }
    return element
  })
}

/**
 * 時間控制工具
 */
export const timeUtils = {
  /**
   * 推進定時器
   */
  advanceTimers(ms: number): void {
    vi.advanceTimersByTime(ms)
  },

  /**
   * 執行所有定時器
   */
  runAllTimers(): void {
    vi.runAllTimers()
  },

  /**
   * 使用假時間
   */
  useFakeTimers(): void {
    vi.useFakeTimers()
  },

  /**
   * 使用真實時間
   */
  useRealTimers(): void {
    vi.useRealTimers()
  }
}

/**
 * 隨機資料生成工具
 */
export const randomUtils = {
  /**
   * 生成隨機字串
   */
  generateRandomString(length: number = 10): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
    let result = ''
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    return result
  },

  /**
   * 生成隨機 Email
   */
  generateRandomEmail(): string {
    const username = this.generateRandomString(8)
    const domain = this.generateRandomString(6)
    return `${username}@${domain}.com`
  },

  /**
   * 生成隨機 ID
   */
  generateRandomId(): string {
    return Date.now().toString() + Math.random().toString(36).substr(2, 9)
  },

  /**
   * 生成隨機數字
   */
  generateRandomNumber(min: number = 0, max: number = 100): number {
    return Math.floor(Math.random() * (max - min + 1)) + min
  },

  /**
   * 生成隨機布林值
   */
  generateRandomBoolean(): boolean {
    return Math.random() < 0.5
  }
}

/**
 * 檔案操作工具
 */
export const fileUtils = {
  /**
   * 建立臨時檔案
   */
  async createTempFile(content: string, filename?: string): Promise<string> {
    const fs = await import('fs/promises')
    const path = await import('path')
    const os = await import('os')
    
    const tempDir = os.tmpdir()
    const fileName = filename || `temp-${randomUtils.generateRandomId()}.txt`
    const filePath = path.join(tempDir, fileName)
    
    await fs.writeFile(filePath, content, 'utf-8')
    return filePath
  },

  /**
   * 清理臨時檔案
   */
  async cleanupTempFiles(filePaths: string[]): Promise<void> {
    const fs = await import('fs/promises')
    
    for (const filePath of filePaths) {
      try {
        await fs.unlink(filePath)
      } catch (error) {
        // 忽略檔案不存在的錯誤
        if ((error as any).code !== 'ENOENT') {
          console.warn(`Failed to cleanup temp file: ${filePath}`, error)
        }
      }
    }
  }
}

/**
 * 測試資料清理工具
 */
export const cleanupUtils = {
  /**
   * 清理 DOM
   */
  cleanupDOM(): void {
    if (typeof document !== 'undefined') {
      document.body.innerHTML = ''
    }
  },

  /**
   * 清理所有 Mock
   */
  clearAllMocks(): void {
    vi.clearAllMocks()
  },

  /**
   * 重置所有 Mock
   */
  resetAllMocks(): void {
    vi.resetAllMocks()
  },

  /**
   * 恢復所有 Mock
   */
  restoreAllMocks(): void {
    vi.restoreAllMocks()
  }
}

/**
 * 異步測試工具
 */
export const asyncUtils = {
  /**
   * 延遲執行
   */
  delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  },

  /**
   * 等待下一個 tick
   */
  nextTick(): Promise<void> {
    return new Promise(resolve => process.nextTick(resolve))
  },

  /**
   * 等待 Promise 解決
   */
  async waitForPromise<T>(promise: Promise<T>, timeout: number = 5000): Promise<T> {
    return Promise.race([
      promise,
      new Promise<never>((_, reject) => 
        setTimeout(() => reject(new Error(`Promise timeout after ${timeout}ms`)), timeout)
      )
    ])
  }
}

/**
 * 斷言輔助工具
 */
export const assertUtils = {
  /**
   * 斷言陣列包含特定項目
   */
  expectArrayToContain<T>(array: T[], item: T): void {
    expect(array).toContain(item)
  },

  /**
   * 斷言物件具有特定屬性
   */
  expectObjectToHaveProperty(obj: any, property: string): void {
    expect(obj).toHaveProperty(property)
  },

  /**
   * 斷言函數被呼叫特定次數
   */
  expectFunctionToBeCalledTimes(fn: any, times: number): void {
    expect(fn).toHaveBeenCalledTimes(times)
  },

  /**
   * 斷言字串符合正則表達式
   */
  expectStringToMatch(str: string, regex: RegExp): void {
    expect(str).toMatch(regex)
  }
}