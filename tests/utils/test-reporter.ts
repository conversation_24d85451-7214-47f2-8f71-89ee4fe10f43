import { writeFile, mkdir } from 'fs/promises'
import { join, dirname } from 'path'
import { existsSync } from 'fs'

export interface TestCase {
  name: string
  status: 'passed' | 'failed' | 'skipped'
  duration: number
  error?: TestError
  assertions: number
  startTime: Date
  endTime: Date
}

export interface TestSuite {
  name: string
  file: string
  tests: TestCase[]
  duration: number
  status: 'passed' | 'failed' | 'skipped'
  startTime: Date
  endTime: Date
  setupDuration?: number
  teardownDuration?: number
}

export interface TestError {
  type: string
  message: string
  stack?: string
  context?: Record<string, any>
  suggestions?: string[]
}

export interface TestResults {
  summary: {
    total: number
    passed: number
    failed: number
    skipped: number
    duration: number
    startTime: Date
    endTime: Date
    environment: string
    nodeVersion: string
    vitestVersion: string
  }
  suites: TestSuite[]
  coverage?: CoverageData
  performance: PerformanceMetrics
}

export interface CoverageData {
  lines: {
    total: number
    covered: number
    percentage: number
  }
  functions: {
    total: number
    covered: number
    percentage: number
  }
  branches: {
    total: number
    covered: number
    percentage: number
  }
  statements: {
    total: number
    covered: number
    percentage: number
  }
  files: CoverageFileData[]
  thresholds: CoverageThresholds
}

export interface CoverageFileData {
  path: string
  lines: { total: number; covered: number; percentage: number }
  functions: { total: number; covered: number; percentage: number }
  branches: { total: number; covered: number; percentage: number }
  statements: { total: number; covered: number; percentage: number }
  uncoveredLines: number[]
}

export interface CoverageThresholds {
  lines: number
  functions: number
  branches: number
  statements: number
}

export interface PerformanceMetrics {
  totalExecutionTime: number
  averageTestTime: number
  slowestTests: Array<{ name: string; duration: number; file: string }>
  fastestTests: Array<{ name: string; duration: number; file: string }>
  memoryUsage: {
    heapUsed: number
    heapTotal: number
    external: number
    rss: number
  }
  testDistribution: {
    unit: number
    integration: number
    server: number
  }
}

export interface TestReport {
  metadata: {
    generatedAt: Date
    version: string
    environment: string
    reportType: 'summary' | 'detailed' | 'coverage'
  }
  results: TestResults
  insights: {
    overallHealth: 'excellent' | 'good' | 'fair' | 'poor'
    recommendations: string[]
    trends: {
      passRate: number
      averageDuration: number
      coverageChange: number
    }
  }
}

export class TestReporter {
  private results: TestResults | null = null
  private outputDir: string
  private config: ReporterConfig

  constructor(config: ReporterConfig = {}) {
    this.config = {
      outputDir: 'test-reports',
      formats: ['html', 'json'],
      includePerformance: true,
      includeCoverage: true,
      thresholds: {
        lines: 80,
        functions: 80,
        branches: 70,
        statements: 80
      },
      ...config
    }
    this.outputDir = this.config.outputDir
  }

  /**
   * Generate comprehensive test report
   */
  async generateReport(results: TestResults): Promise<TestReport> {
    this.results = results
    
    const report: TestReport = {
      metadata: {
        generatedAt: new Date(),
        version: '1.0.0',
        environment: results.summary.environment,
        reportType: 'detailed'
      },
      results,
      insights: this.generateInsights(results)
    }

    return report
  }

  /**
   * Generate coverage report
   */
  async generateCoverageReport(coverage: CoverageData): Promise<CoverageReport> {
    const report: CoverageReport = {
      metadata: {
        generatedAt: new Date(),
        version: '1.0.0',
        type: 'coverage'
      },
      coverage,
      analysis: this.analyzeCoverage(coverage),
      recommendations: this.generateCoverageRecommendations(coverage)
    }

    return report
  }

  /**
   * Export report to HTML format
   */
  async exportToHTML(report: TestReport): Promise<string> {
    const htmlContent = this.generateHTMLReport(report)
    const filePath = join(this.outputDir, 'test-report.html')
    
    await this.ensureDirectoryExists(dirname(filePath))
    await writeFile(filePath, htmlContent, 'utf-8')
    
    return filePath
  }

  /**
   * Export report to JSON format
   */
  async exportToJSON(report: TestReport): Promise<string> {
    const jsonContent = JSON.stringify(report, null, 2)
    const filePath = join(this.outputDir, 'test-report.json')
    
    await this.ensureDirectoryExists(dirname(filePath))
    await writeFile(filePath, jsonContent, 'utf-8')
    
    return filePath
  }

  /**
   * Export report to XML format
   */
  async exportToXML(report: TestReport): Promise<string> {
    const xmlContent = this.generateXMLReport(report)
    const filePath = join(this.outputDir, 'test-report.xml')
    
    await this.ensureDirectoryExists(dirname(filePath))
    await writeFile(filePath, xmlContent, 'utf-8')
    
    return filePath
  }

  /**
   * Generate JUnit XML report for CI systems
   */
  async generateJUnitReport(results: TestResults): Promise<string> {
    const xmlContent = this.generateJUnitXML(results)
    const filePath = join(this.outputDir, 'junit-report.xml')
    
    await this.ensureDirectoryExists(dirname(filePath))
    await writeFile(filePath, xmlContent, 'utf-8')
    
    return filePath
  }

  /**
   * Generate coverage XML report
   */
  async generateCoverageXML(coverage: CoverageData): Promise<string> {
    const xmlContent = this.generateCoverageXMLContent(coverage)
    const filePath = join(this.outputDir, 'coverage-report.xml')
    
    await this.ensureDirectoryExists(dirname(filePath))
    await writeFile(filePath, xmlContent, 'utf-8')
    
    return filePath
  }

  /**
   * Generate insights from test results
   */
  private generateInsights(results: TestResults): TestReport['insights'] {
    const passRate = (results.summary.passed / results.summary.total) * 100
    const avgDuration = results.summary.duration / results.summary.total
    
    let overallHealth: 'excellent' | 'good' | 'fair' | 'poor' = 'poor'
    if (passRate >= 95 && avgDuration < 1000) overallHealth = 'excellent'
    else if (passRate >= 90 && avgDuration < 2000) overallHealth = 'good'
    else if (passRate >= 80) overallHealth = 'fair'

    const recommendations: string[] = []
    
    if (passRate < 90) {
      recommendations.push(`Improve test pass rate (currently ${passRate.toFixed(1)}%)`)
    }
    
    if (avgDuration > 2000) {
      recommendations.push(`Optimize test performance (average ${avgDuration.toFixed(0)}ms per test)`)
    }
    
    if (results.coverage && results.coverage.lines.percentage < this.config.thresholds.lines) {
      recommendations.push(`Increase line coverage (currently ${results.coverage.lines.percentage.toFixed(1)}%)`)
    }

    const slowTests = results.performance.slowestTests
    if (slowTests.length > 0 && slowTests[0].duration > 5000) {
      recommendations.push(`Review slow tests: ${slowTests[0].name} (${slowTests[0].duration}ms)`)
    }

    return {
      overallHealth,
      recommendations,
      trends: {
        passRate,
        averageDuration: avgDuration,
        coverageChange: results.coverage ? results.coverage.lines.percentage : 0
      }
    }
  }

  /**
   * Analyze coverage data
   */
  private analyzeCoverage(coverage: CoverageData): CoverageAnalysis {
    const totalFiles = coverage.files.length
    const wellCoveredFiles = coverage.files.filter(f => f.lines.percentage >= 80).length
    const poorlyCoveredFiles = coverage.files.filter(f => f.lines.percentage < 50).length
    
    return {
      overallScore: (coverage.lines.percentage + coverage.functions.percentage + 
                    coverage.branches.percentage + coverage.statements.percentage) / 4,
      fileDistribution: {
        wellCovered: wellCoveredFiles,
        poorlyCovered: poorlyCoveredFiles,
        total: totalFiles
      },
      criticalFiles: coverage.files
        .filter(f => f.lines.percentage < 50)
        .sort((a, b) => a.lines.percentage - b.lines.percentage)
        .slice(0, 5)
    }
  }

  /**
   * Generate coverage recommendations
   */
  private generateCoverageRecommendations(coverage: CoverageData): string[] {
    const recommendations: string[] = []
    
    if (coverage.lines.percentage < this.config.thresholds.lines) {
      recommendations.push(`Increase line coverage from ${coverage.lines.percentage.toFixed(1)}% to ${this.config.thresholds.lines}%`)
    }
    
    if (coverage.functions.percentage < this.config.thresholds.functions) {
      recommendations.push(`Increase function coverage from ${coverage.functions.percentage.toFixed(1)}% to ${this.config.thresholds.functions}%`)
    }
    
    if (coverage.branches.percentage < this.config.thresholds.branches) {
      recommendations.push(`Increase branch coverage from ${coverage.branches.percentage.toFixed(1)}% to ${this.config.thresholds.branches}%`)
    }

    const poorFiles = coverage.files.filter(f => f.lines.percentage < 50)
    if (poorFiles.length > 0) {
      recommendations.push(`Focus on improving coverage for ${poorFiles.length} poorly covered files`)
    }

    return recommendations
  }

  /**
   * Generate HTML report content
   */
  private generateHTMLReport(report: TestReport): string {
    const { results, insights } = report
    const { summary, suites, coverage, performance } = results

    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Report - ${report.metadata.generatedAt.toISOString()}</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 8px 8px 0 0; }
        .header h1 { margin: 0; font-size: 2.5em; }
        .header .meta { opacity: 0.9; margin-top: 10px; }
        .content { padding: 30px; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .metric { background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center; border-left: 4px solid #007bff; }
        .metric.passed { border-left-color: #28a745; }
        .metric.failed { border-left-color: #dc3545; }
        .metric.skipped { border-left-color: #ffc107; }
        .metric h3 { margin: 0 0 10px 0; color: #666; font-size: 0.9em; text-transform: uppercase; }
        .metric .value { font-size: 2em; font-weight: bold; color: #333; }
        .section { margin-bottom: 40px; }
        .section h2 { color: #333; border-bottom: 2px solid #eee; padding-bottom: 10px; }
        .health-badge { display: inline-block; padding: 5px 15px; border-radius: 20px; color: white; font-weight: bold; text-transform: uppercase; font-size: 0.8em; }
        .health-excellent { background: #28a745; }
        .health-good { background: #17a2b8; }
        .health-fair { background: #ffc107; color: #333; }
        .health-poor { background: #dc3545; }
        .recommendations { background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 20px; }
        .recommendations ul { margin: 0; padding-left: 20px; }
        .test-suites { margin-top: 20px; }
        .suite { background: #f8f9fa; margin-bottom: 15px; border-radius: 8px; overflow: hidden; }
        .suite-header { background: #e9ecef; padding: 15px; font-weight: bold; cursor: pointer; }
        .suite-content { padding: 15px; display: none; }
        .test-case { padding: 10px; border-bottom: 1px solid #eee; display: flex; justify-content: space-between; align-items: center; }
        .test-case:last-child { border-bottom: none; }
        .status { padding: 3px 8px; border-radius: 4px; font-size: 0.8em; font-weight: bold; }
        .status.passed { background: #d4edda; color: #155724; }
        .status.failed { background: #f8d7da; color: #721c24; }
        .status.skipped { background: #fff3cd; color: #856404; }
        .coverage-bar { background: #e9ecef; height: 20px; border-radius: 10px; overflow: hidden; margin: 10px 0; }
        .coverage-fill { height: 100%; background: linear-gradient(90deg, #dc3545 0%, #ffc107 50%, #28a745 100%); transition: width 0.3s ease; }
        .performance-chart { background: #f8f9fa; padding: 20px; border-radius: 8px; margin-top: 20px; }
        table { width: 100%; border-collapse: collapse; margin-top: 15px; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #eee; }
        th { background: #f8f9fa; font-weight: 600; }
        .duration { color: #666; font-size: 0.9em; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Test Report</h1>
            <div class="meta">
                Generated: ${report.metadata.generatedAt.toLocaleString()} | 
                Environment: ${report.metadata.environment} | 
                Version: ${report.metadata.version}
            </div>
        </div>
        
        <div class="content">
            <div class="section">
                <h2>Overall Health <span class="health-badge health-${insights.overallHealth}">${insights.overallHealth}</span></h2>
                <div class="summary">
                    <div class="metric passed">
                        <h3>Passed</h3>
                        <div class="value">${summary.passed}</div>
                    </div>
                    <div class="metric failed">
                        <h3>Failed</h3>
                        <div class="value">${summary.failed}</div>
                    </div>
                    <div class="metric skipped">
                        <h3>Skipped</h3>
                        <div class="value">${summary.skipped}</div>
                    </div>
                    <div class="metric">
                        <h3>Total Duration</h3>
                        <div class="value">${(summary.duration / 1000).toFixed(2)}s</div>
                    </div>
                    <div class="metric">
                        <h3>Pass Rate</h3>
                        <div class="value">${((summary.passed / summary.total) * 100).toFixed(1)}%</div>
                    </div>
                </div>
            </div>

            ${insights.recommendations.length > 0 ? `
            <div class="section">
                <h2>Recommendations</h2>
                <div class="recommendations">
                    <ul>
                        ${insights.recommendations.map(rec => `<li>${rec}</li>`).join('')}
                    </ul>
                </div>
            </div>
            ` : ''}

            ${coverage ? `
            <div class="section">
                <h2>Code Coverage</h2>
                <div class="coverage-metrics">
                    <div>
                        <strong>Lines:</strong> ${coverage.lines.percentage.toFixed(1)}% (${coverage.lines.covered}/${coverage.lines.total})
                        <div class="coverage-bar">
                            <div class="coverage-fill" style="width: ${coverage.lines.percentage}%"></div>
                        </div>
                    </div>
                    <div>
                        <strong>Functions:</strong> ${coverage.functions.percentage.toFixed(1)}% (${coverage.functions.covered}/${coverage.functions.total})
                        <div class="coverage-bar">
                            <div class="coverage-fill" style="width: ${coverage.functions.percentage}%"></div>
                        </div>
                    </div>
                    <div>
                        <strong>Branches:</strong> ${coverage.branches.percentage.toFixed(1)}% (${coverage.branches.covered}/${coverage.branches.total})
                        <div class="coverage-bar">
                            <div class="coverage-fill" style="width: ${coverage.branches.percentage}%"></div>
                        </div>
                    </div>
                </div>
            </div>
            ` : ''}

            <div class="section">
                <h2>Performance Metrics</h2>
                <div class="performance-chart">
                    <p><strong>Average Test Time:</strong> ${performance.averageTestTime.toFixed(2)}ms</p>
                    <p><strong>Total Execution Time:</strong> ${(performance.totalExecutionTime / 1000).toFixed(2)}s</p>
                    
                    <h4>Slowest Tests</h4>
                    <table>
                        <thead>
                            <tr><th>Test Name</th><th>File</th><th>Duration</th></tr>
                        </thead>
                        <tbody>
                            ${performance.slowestTests.slice(0, 5).map(test => `
                                <tr>
                                    <td>${test.name}</td>
                                    <td>${test.file}</td>
                                    <td class="duration">${test.duration}ms</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="section">
                <h2>Test Suites</h2>
                <div class="test-suites">
                    ${suites.map(suite => `
                        <div class="suite">
                            <div class="suite-header" onclick="toggleSuite(this)">
                                ${suite.name} 
                                <span class="status ${suite.status}">${suite.status}</span>
                                <span class="duration">${suite.duration}ms</span>
                            </div>
                            <div class="suite-content">
                                ${suite.tests.map(test => `
                                    <div class="test-case">
                                        <span>${test.name}</span>
                                        <div>
                                            <span class="status ${test.status}">${test.status}</span>
                                            <span class="duration">${test.duration}ms</span>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        </div>
    </div>

    <script>
        function toggleSuite(header) {
            const content = header.nextElementSibling;
            content.style.display = content.style.display === 'block' ? 'none' : 'block';
        }
    </script>
</body>
</html>
    `.trim()
  }

  /**
   * Generate XML report content
   */
  private generateXMLReport(report: TestReport): string {
    const { results } = report
    const { summary, suites } = results

    return `<?xml version="1.0" encoding="UTF-8"?>
<testReport>
    <metadata>
        <generatedAt>${report.metadata.generatedAt.toISOString()}</generatedAt>
        <version>${report.metadata.version}</version>
        <environment>${report.metadata.environment}</environment>
    </metadata>
    <summary>
        <total>${summary.total}</total>
        <passed>${summary.passed}</passed>
        <failed>${summary.failed}</failed>
        <skipped>${summary.skipped}</skipped>
        <duration>${summary.duration}</duration>
        <passRate>${((summary.passed / summary.total) * 100).toFixed(2)}</passRate>
    </summary>
    <testSuites>
        ${suites.map(suite => `
        <testSuite name="${this.escapeXml(suite.name)}" file="${this.escapeXml(suite.file)}" status="${suite.status}" duration="${suite.duration}">
            ${suite.tests.map(test => `
            <testCase name="${this.escapeXml(test.name)}" status="${test.status}" duration="${test.duration}" assertions="${test.assertions}">
                ${test.error ? `
                <error type="${this.escapeXml(test.error.type)}" message="${this.escapeXml(test.error.message)}">
                    ${test.error.stack ? `<stackTrace>${this.escapeXml(test.error.stack)}</stackTrace>` : ''}
                </error>
                ` : ''}
            </testCase>
            `).join('')}
        </testSuite>
        `).join('')}
    </testSuites>
</testReport>`
  }

  /**
   * Generate JUnit XML format
   */
  private generateJUnitXML(results: TestResults): string {
    const { summary, suites } = results

    return `<?xml version="1.0" encoding="UTF-8"?>
<testsuites name="UIGen Vue Test Suite" tests="${summary.total}" failures="${summary.failed}" skipped="${summary.skipped}" time="${(summary.duration / 1000).toFixed(3)}">
    ${suites.map(suite => `
    <testsuite name="${this.escapeXml(suite.name)}" tests="${suite.tests.length}" failures="${suite.tests.filter(t => t.status === 'failed').length}" skipped="${suite.tests.filter(t => t.status === 'skipped').length}" time="${(suite.duration / 1000).toFixed(3)}" file="${this.escapeXml(suite.file)}">
        ${suite.tests.map(test => `
        <testcase name="${this.escapeXml(test.name)}" classname="${this.escapeXml(suite.name)}" time="${(test.duration / 1000).toFixed(3)}">
            ${test.status === 'failed' && test.error ? `
            <failure message="${this.escapeXml(test.error.message)}" type="${this.escapeXml(test.error.type)}">
                ${test.error.stack ? this.escapeXml(test.error.stack) : ''}
            </failure>
            ` : ''}
            ${test.status === 'skipped' ? '<skipped/>' : ''}
        </testcase>
        `).join('')}
    </testsuite>
    `).join('')}
</testsuites>`
  }

  /**
   * Generate coverage XML content
   */
  private generateCoverageXMLContent(coverage: CoverageData): string {
    return `<?xml version="1.0" encoding="UTF-8"?>
<coverage>
    <summary>
        <lines total="${coverage.lines.total}" covered="${coverage.lines.covered}" percentage="${coverage.lines.percentage.toFixed(2)}"/>
        <functions total="${coverage.functions.total}" covered="${coverage.functions.covered}" percentage="${coverage.functions.percentage.toFixed(2)}"/>
        <branches total="${coverage.branches.total}" covered="${coverage.branches.covered}" percentage="${coverage.branches.percentage.toFixed(2)}"/>
        <statements total="${coverage.statements.total}" covered="${coverage.statements.covered}" percentage="${coverage.statements.percentage.toFixed(2)}"/>
    </summary>
    <files>
        ${coverage.files.map(file => `
        <file path="${this.escapeXml(file.path)}">
            <lines total="${file.lines.total}" covered="${file.lines.covered}" percentage="${file.lines.percentage.toFixed(2)}"/>
            <functions total="${file.functions.total}" covered="${file.functions.covered}" percentage="${file.functions.percentage.toFixed(2)}"/>
            <branches total="${file.branches.total}" covered="${file.branches.covered}" percentage="${file.branches.percentage.toFixed(2)}"/>
            <statements total="${file.statements.total}" covered="${file.statements.covered}" percentage="${file.statements.percentage.toFixed(2)}"/>
            <uncoveredLines>${file.uncoveredLines.join(',')}</uncoveredLines>
        </file>
        `).join('')}
    </files>
</coverage>`
  }

  /**
   * Escape XML special characters
   */
  private escapeXml(text: string): string {
    return text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&apos;')
  }

  /**
   * Ensure directory exists
   */
  private async ensureDirectoryExists(dirPath: string): Promise<void> {
    if (!existsSync(dirPath)) {
      await mkdir(dirPath, { recursive: true })
    }
  }
}

// Additional interfaces
export interface ReporterConfig {
  outputDir?: string
  formats?: Array<'html' | 'json' | 'xml' | 'junit'>
  includePerformance?: boolean
  includeCoverage?: boolean
  thresholds?: CoverageThresholds
}

export interface CoverageReport {
  metadata: {
    generatedAt: Date
    version: string
    type: 'coverage'
  }
  coverage: CoverageData
  analysis: CoverageAnalysis
  recommendations: string[]
}

export interface CoverageAnalysis {
  overallScore: number
  fileDistribution: {
    wellCovered: number
    poorlyCovered: number
    total: number
  }
  criticalFiles: CoverageFileData[]
}