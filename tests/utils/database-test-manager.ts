import { PrismaClient } from '@prisma/client'
import { execSync } from 'child_process'
import { randomUUID } from 'crypto'

export interface TestDataOptions {
  cleanup?: boolean
  transaction?: boolean
}

export class DatabaseTestManager {
  private prisma: PrismaClient
  private testDatabaseUrl: string
  private createdRecords: Map<string, string[]> = new Map()

  constructor() {
    // 使用測試資料庫 URL
    this.testDatabaseUrl = process.env.TEST_DATABASE_URL || process.env.DATABASE_URL || ''
    
    if (!this.testDatabaseUrl.includes('test')) {
      throw new Error('Test database URL must contain "test" to prevent accidental data loss')
    }

    this.prisma = new PrismaClient({
      datasources: {
        db: {
          url: this.testDatabaseUrl
        }
      }
    })
  }

  /**
   * 初始化測試資料庫
   */
  async initializeTestDatabase(): Promise<void> {
    try {
      // 確保資料庫連接
      await this.prisma.$connect()
      
      // 重置資料庫結構
      await this.resetDatabase()
      
      console.log('Test database initialized successfully')
    } catch (error) {
      console.error('Failed to initialize test database:', error)
      throw error
    }
  }

  /**
   * 重置資料庫
   */
  async resetDatabase(): Promise<void> {
    try {
      // 清理所有資料表（按照外鍵依賴順序）
      await this.prisma.file.deleteMany()
      await this.prisma.project.deleteMany()
      await this.prisma.user.deleteMany()
      
      // 清理記錄追蹤
      this.createdRecords.clear()
      
      console.log('Database reset completed')
    } catch (error) {
      console.error('Failed to reset database:', error)
      throw error
    }
  }

  /**
   * 清理測試資料
   */
  async cleanupTestData(): Promise<void> {
    try {
      // 按照建立的相反順序清理
      const tables = Array.from(this.createdRecords.keys()).reverse()
      
      for (const table of tables) {
        const ids = this.createdRecords.get(table) || []
        
        switch (table) {
          case 'file':
            await this.prisma.file.deleteMany({
              where: { id: { in: ids } }
            })
            break
          case 'project':
            await this.prisma.project.deleteMany({
              where: { id: { in: ids } }
            })
            break
          case 'user':
            await this.prisma.user.deleteMany({
              where: { id: { in: ids } }
            })
            break
        }
      }
      
      this.createdRecords.clear()
      console.log('Test data cleanup completed')
    } catch (error) {
      console.error('Failed to cleanup test data:', error)
      throw error
    }
  }

  /**
   * 建立測試資料
   */
  async createTestData<T>(
    entity: string, 
    data: any, 
    options: TestDataOptions = {}
  ): Promise<T> {
    try {
      let result: any

      switch (entity) {
        case 'user':
          result = await this.prisma.user.create({
            data: {
              id: data.id || randomUUID(),
              email: data.email || `test-${randomUUID()}@example.com`,
              name: data.name || 'Test User',
              ...data
            }
          })
          break

        case 'project':
          result = await this.prisma.project.create({
            data: {
              id: data.id || randomUUID(),
              name: data.name || `Test Project ${Date.now()}`,
              description: data.description || 'Test project description',
              userId: data.userId,
              ...data
            }
          })
          break

        case 'file':
          result = await this.prisma.file.create({
            data: {
              id: data.id || randomUUID(),
              name: data.name || `test-file-${Date.now()}.txt`,
              content: data.content || 'Test file content',
              projectId: data.projectId,
              ...data
            }
          })
          break

        default:
          throw new Error(`Unsupported entity type: ${entity}`)
      }

      // 追蹤建立的記錄
      if (options.cleanup !== false) {
        if (!this.createdRecords.has(entity)) {
          this.createdRecords.set(entity, [])
        }
        this.createdRecords.get(entity)!.push(result.id)
      }

      return result as T
    } catch (error) {
      console.error(`Failed to create test ${entity}:`, error)
      throw error
    }
  }

  /**
   * 在事務中執行操作
   */
  async withTransaction<T>(callback: (prisma: any) => Promise<T>): Promise<T> {
    return await this.prisma.$transaction(async (prisma) => {
      return await callback(prisma)
    })
  }

  /**
   * 種子測試資料
   */
  async seedTestData(): Promise<void> {
    try {
      // 建立測試用戶
      const testUser = await this.createTestData('user', {
        email: '<EMAIL>',
        name: 'Test User'
      })

      // 建立測試專案
      const testProject = await this.createTestData('project', {
        name: 'Test Project',
        description: 'A test project for API testing',
        userId: testUser.id
      })

      // 建立測試檔案
      await this.createTestData('file', {
        name: 'test.txt',
        content: 'Hello, World!',
        projectId: testProject.id
      })

      console.log('Test data seeded successfully')
    } catch (error) {
      console.error('Failed to seed test data:', error)
      throw error
    }
  }

  /**
   * 取得 Prisma 客戶端
   */
  getPrismaClient(): PrismaClient {
    return this.prisma
  }

  /**
   * 關閉資料庫連接
   */
  async cleanup(): Promise<void> {
    try {
      await this.cleanupTestData()
      await this.prisma.$disconnect()
      console.log('Database connection closed')
    } catch (error) {
      console.error('Failed to cleanup database:', error)
      throw error
    }
  }

  /**
   * 檢查資料庫連接
   */
  async checkConnection(): Promise<boolean> {
    try {
      await this.prisma.$queryRaw`SELECT 1`
      return true
    } catch {
      return false
    }
  }

  /**
   * 取得資料表記錄數量
   */
  async getTableCounts(): Promise<Record<string, number>> {
    try {
      const [userCount, projectCount, fileCount] = await Promise.all([
        this.prisma.user.count(),
        this.prisma.project.count(),
        this.prisma.file.count()
      ])

      return {
        users: userCount,
        projects: projectCount,
        files: fileCount
      }
    } catch (error) {
      console.error('Failed to get table counts:', error)
      throw error
    }
  }
}