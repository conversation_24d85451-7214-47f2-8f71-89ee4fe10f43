// Mock 物件工廠系統
import { vi } from 'vitest'
import { randomUtils } from './test-helpers'

/**
 * 專案 Mock 工廠
 */
export const projectMockFactory = {
  /**
   * 建立基本專案 Mock
   */
  createProject(overrides: Partial<any> = {}) {
    return {
      id: randomUtils.generateRandomId(),
      name: `測試專案-${randomUtils.generateRandomString(6)}`,
      description: '測試專案描述',
      createdAt: new Date(),
      updatedAt: new Date(),
      files: [],
      ...overrides
    }
  },

  /**
   * 建立多個專案 Mock
   */
  createProjects(count: number = 3, overrides: Partial<any> = {}) {
    return Array.from({ length: count }, (_, index) => 
      this.createProject({ 
        name: `測試專案-${index + 1}`,
        ...overrides 
      })
    )
  }
}

/**
 * 檔案 Mock 工廠
 */
export const fileMockFactory = {
  /**
   * 建立基本檔案 Mock
   */
  createFile(overrides: Partial<any> = {}) {
    return {
      id: randomUtils.generateRandomId(),
      name: `TestFile-${randomUtils.generateRandomString(4)}.vue`,
      content: '<template><div>測試內容</div></template>',
      projectId: randomUtils.generateRandomId(),
      createdAt: new Date(),
      updatedAt: new Date(),
      ...overrides
    }
  },

  /**
   * 建立多個檔案 Mock
   */
  createFiles(count: number = 3, projectId?: string, overrides: Partial<any> = {}) {
    const baseProjectId = projectId || randomUtils.generateRandomId()
    return Array.from({ length: count }, (_, index) => 
      this.createFile({ 
        name: `TestFile-${index + 1}.vue`,
        projectId: baseProjectId,
        ...overrides 
      })
    )
  }
}

/**
 * AI 提供者 Mock 工廠
 */
export const aiProviderMockFactory = {
  /**
   * 建立 AI 提供者 Mock
   */
  createProvider(overrides: Partial<any> = {}) {
    return {
      id: randomUtils.generateRandomId(),
      name: `Mock AI Provider`,
      type: 'mock',
      isHealthy: true,
      responseTime: randomUtils.generateRandomNumber(50, 200),
      generateContent: vi.fn().mockResolvedValue('AI 生成的內容'),
      generateStream: vi.fn().mockImplementation(async function* () {
        yield '這是'
        yield ' AI '
        yield '串流回應'
      }),
      healthCheck: vi.fn().mockResolvedValue({ healthy: true, responseTime: 100 }),
      ...overrides
    }
  }
}

/**
 * WebSocket Mock 工廠
 */
export const webSocketMockFactory = {
  /**
   * 建立 WebSocket Mock
   */
  createWebSocket(overrides: Partial<any> = {}) {
    const mockWs = {
      readyState: 1, // OPEN
      send: vi.fn(),
      close: vi.fn(),
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
      onopen: null,
      onclose: null,
      onmessage: null,
      onerror: null,
      ...overrides
    }

    // 模擬事件觸發
    mockWs.triggerOpen = () => {
      if (mockWs.onopen) mockWs.onopen({} as Event)
    }
    
    mockWs.triggerMessage = (data: any) => {
      if (mockWs.onmessage) {
        mockWs.onmessage({ data: JSON.stringify(data) } as MessageEvent)
      }
    }
    
    mockWs.triggerClose = (code = 1000, reason = '正常關閉') => {
      if (mockWs.onclose) {
        mockWs.onclose({ code, reason } as CloseEvent)
      }
    }
    
    mockWs.triggerError = (error: any) => {
      if (mockWs.onerror) mockWs.onerror(error)
    }

    return mockWs
  }
}

/**
 * HTTP 請求 Mock 工廠
 */
export const httpMockFactory = {
  /**
   * 建立成功回應 Mock
   */
  createSuccessResponse(data: any, status: number = 200) {
    return {
      status,
      statusText: 'OK',
      data,
      headers: { 'content-type': 'application/json' }
    }
  },

  /**
   * 建立錯誤回應 Mock
   */
  createErrorResponse(message: string, status: number = 400) {
    return {
      status,
      statusText: 'Error',
      data: { error: message },
      headers: { 'content-type': 'application/json' }
    }
  }
}

/**
 * Vue 組件 Mock 工廠
 */
export const vueMockFactory = {
  /**
   * 建立 Vue 組件 Mock
   */
  createComponent(name: string, overrides: Partial<any> = {}) {
    return {
      name,
      template: `<div class="${name.toLowerCase()}">${name} Component</div>`,
      props: {},
      data: () => ({}),
      methods: {},
      mounted: vi.fn(),
      unmounted: vi.fn(),
      ...overrides
    }
  },

  /**
   * 建立 Pinia Store Mock
   */
  createStore(name: string, overrides: Partial<any> = {}) {
    return {
      $id: name,
      $state: {},
      $patch: vi.fn(),
      $reset: vi.fn(),
      $subscribe: vi.fn(),
      $dispose: vi.fn(),
      ...overrides
    }
  }
}