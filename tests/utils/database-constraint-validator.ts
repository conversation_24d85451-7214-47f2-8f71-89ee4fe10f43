import { PrismaClient } from '@prisma/client'

export interface ConstraintError {
  type: 'FOREIGN_KEY' | 'UNIQUE' | 'NOT_NULL' | 'CHECK'
  table: string
  column?: string
  constraint: string
  message: string
  suggestion: string
}

export interface ValidationResult {
  isValid: boolean
  errors: ConstraintError[]
  warnings: string[]
}

/**
 * Database Constraint Validator for ensuring proper test data relationships
 */
export class DatabaseConstraintValidator {
  private prisma: PrismaClient

  constructor(prisma: PrismaClient) {
    this.prisma = prisma
  }

  /**
   * Validate foreign key constraints before creating test data
   */
  async validateForeignKeys(data: any, tableName: string): Promise<ValidationResult> {
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: []
    }

    try {
      switch (tableName) {
        case 'project':
          await this.validateProjectConstraints(data, result)
          break
        case 'file':
          await this.validateFileConstraints(data, result)
          break
        default:
          result.warnings.push(`No validation rules defined for table: ${tableName}`)
      }
    } catch (error) {
      result.isValid = false
      result.errors.push({
        type: 'FOREIGN_KEY',
        table: tableName,
        constraint: 'unknown',
        message: `Validation failed: ${error.message}`,
        suggestion: 'Check database connection and table structure'
      })
    }

    return result
  }

  /**
   * Validate project constraints
   */
  private async validateProjectConstraints(data: any, result: ValidationResult): Promise<void> {
    // Check required fields
    if (!data.name || typeof data.name !== 'string') {
      result.isValid = false
      result.errors.push({
        type: 'NOT_NULL',
        table: 'project',
        column: 'name',
        constraint: 'NOT_NULL',
        message: 'Project name is required and must be a string',
        suggestion: 'Provide a valid project name'
      })
    }

    // Check name length
    if (data.name && data.name.length > 255) {
      result.isValid = false
      result.errors.push({
        type: 'CHECK',
        table: 'project',
        column: 'name',
        constraint: 'LENGTH_CHECK',
        message: 'Project name exceeds maximum length of 255 characters',
        suggestion: 'Shorten the project name'
      })
    }

    // Check for duplicate names (if unique constraint exists)
    if (data.name) {
      const existingProject = await this.prisma.project.findFirst({
        where: { name: data.name }
      })

      if (existingProject && existingProject.id !== data.id) {
        result.warnings.push(`Project with name "${data.name}" already exists`)
      }
    }
  }

  /**
   * Validate file constraints
   */
  private async validateFileConstraints(data: any, result: ValidationResult): Promise<void> {
    // Check required fields
    if (!data.name || typeof data.name !== 'string') {
      result.isValid = false
      result.errors.push({
        type: 'NOT_NULL',
        table: 'file',
        column: 'name',
        constraint: 'NOT_NULL',
        message: 'File name is required and must be a string',
        suggestion: 'Provide a valid file name'
      })
    }

    if (!data.projectId || typeof data.projectId !== 'string') {
      result.isValid = false
      result.errors.push({
        type: 'NOT_NULL',
        table: 'file',
        column: 'projectId',
        constraint: 'NOT_NULL',
        message: 'Project ID is required for file creation',
        suggestion: 'Provide a valid project ID'
      })
    }

    // Validate foreign key relationship
    if (data.projectId) {
      const parentProject = await this.prisma.project.findUnique({
        where: { id: data.projectId }
      })

      if (!parentProject) {
        result.isValid = false
        result.errors.push({
          type: 'FOREIGN_KEY',
          table: 'file',
          column: 'projectId',
          constraint: 'FK_file_project',
          message: `Referenced project with ID "${data.projectId}" does not exist`,
          suggestion: 'Create the parent project first or use an existing project ID'
        })
      }
    }

    // Check unique constraint for file name within project
    if (data.name && data.projectId) {
      const existingFile = await this.prisma.file.findFirst({
        where: {
          name: data.name,
          projectId: data.projectId
        }
      })

      if (existingFile && existingFile.id !== data.id) {
        result.isValid = false
        result.errors.push({
          type: 'UNIQUE',
          table: 'file',
          column: 'name',
          constraint: 'UNIQUE_file_name_project',
          message: `File with name "${data.name}" already exists in project "${data.projectId}"`,
          suggestion: 'Use a different file name or update the existing file'
        })
      }
    }

    // Validate file name format
    if (data.name) {
      const invalidChars = /[<>:"/\\|?*]/
      if (invalidChars.test(data.name)) {
        result.warnings.push(`File name "${data.name}" contains invalid characters`)
      }

      if (data.name.length > 255) {
        result.isValid = false
        result.errors.push({
          type: 'CHECK',
          table: 'file',
          column: 'name',
          constraint: 'LENGTH_CHECK',
          message: 'File name exceeds maximum length of 255 characters',
          suggestion: 'Shorten the file name'
        })
      }
    }
  }

  /**
   * Validate data integrity across multiple records
   */
  async validateDataIntegrity(records: Array<{ table: string; data: any }>): Promise<ValidationResult> {
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: []
    }

    // Group records by table
    const recordsByTable = records.reduce((acc, record) => {
      if (!acc[record.table]) {
        acc[record.table] = []
      }
      acc[record.table].push(record.data)
      return acc
    }, {} as Record<string, any[]>)

    // Validate cross-table relationships
    if (recordsByTable.file && recordsByTable.project) {
      await this.validateFileProjectRelationships(
        recordsByTable.file,
        recordsByTable.project,
        result
      )
    }

    // Check for duplicate IDs within the same batch
    for (const [table, tableRecords] of Object.entries(recordsByTable)) {
      const ids = tableRecords.map(r => r.id).filter(Boolean)
      const duplicateIds = ids.filter((id, index) => ids.indexOf(id) !== index)
      
      if (duplicateIds.length > 0) {
        result.isValid = false
        result.errors.push({
          type: 'UNIQUE',
          table,
          column: 'id',
          constraint: 'PRIMARY_KEY',
          message: `Duplicate IDs found in ${table}: ${duplicateIds.join(', ')}`,
          suggestion: 'Ensure all record IDs are unique'
        })
      }
    }

    return result
  }

  /**
   * Validate file-project relationships in a batch
   */
  private async validateFileProjectRelationships(
    files: any[],
    projects: any[],
    result: ValidationResult
  ): Promise<void> {
    const projectIds = new Set(projects.map(p => p.id))
    
    for (const file of files) {
      if (file.projectId && !projectIds.has(file.projectId)) {
        // Check if project exists in database
        const existingProject = await this.prisma.project.findUnique({
          where: { id: file.projectId }
        })

        if (!existingProject) {
          result.isValid = false
          result.errors.push({
            type: 'FOREIGN_KEY',
            table: 'file',
            column: 'projectId',
            constraint: 'FK_file_project',
            message: `File "${file.name}" references non-existent project "${file.projectId}"`,
            suggestion: 'Include the referenced project in the batch or use an existing project ID'
          })
        }
      }
    }
  }

  /**
   * Get constraint suggestions for common errors
   */
  getConstraintSuggestions(error: any): string[] {
    const suggestions: string[] = []

    if (error.message?.includes('FOREIGN KEY constraint failed')) {
      suggestions.push('Ensure the referenced record exists before creating dependent records')
      suggestions.push('Create parent records before child records')
      suggestions.push('Check that foreign key values are correct')
    }

    if (error.message?.includes('UNIQUE constraint failed')) {
      suggestions.push('Use unique values for fields with unique constraints')
      suggestions.push('Check for existing records with the same values')
      suggestions.push('Consider updating existing records instead of creating new ones')
    }

    if (error.message?.includes('NOT NULL constraint failed')) {
      suggestions.push('Provide values for all required fields')
      suggestions.push('Check the database schema for required columns')
    }

    if (suggestions.length === 0) {
      suggestions.push('Check the database schema and constraints')
      suggestions.push('Verify that all required fields are provided')
      suggestions.push('Ensure data types match the schema requirements')
    }

    return suggestions
  }

  /**
   * Validate creation order for related records
   */
  validateCreationOrder(records: Array<{ table: string; data: any }>): ValidationResult {
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: []
    }

    // Define dependency order (parents before children)
    const dependencyOrder = ['project', 'file']
    const recordsByTable = records.reduce((acc, record) => {
      if (!acc[record.table]) {
        acc[record.table] = []
      }
      acc[record.table].push(record)
      return acc
    }, {} as Record<string, any[]>)

    // Check if records are in correct dependency order
    let lastValidIndex = -1
    for (const table of dependencyOrder) {
      if (recordsByTable[table]) {
        const firstIndex = records.findIndex(r => r.table === table)
        if (firstIndex < lastValidIndex) {
          result.warnings.push(
            `Records for table "${table}" should be created before dependent tables`
          )
        }
        lastValidIndex = Math.max(lastValidIndex, firstIndex)
      }
    }

    return result
  }
}