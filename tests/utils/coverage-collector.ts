import { execSync } from 'child_process'
import { readFile, writeFile } from 'fs/promises'
import { join } from 'path'
import { CoverageData, CoverageFileData, CoverageThresholds } from './test-reporter'

export interface CoverageConfig {
  include: string[]
  exclude: string[]
  thresholds: CoverageThresholds
  outputDir: string
  formats: Array<'html' | 'json' | 'lcov' | 'text'>
  reporter: Array<'html' | 'json' | 'lcov' | 'text-summary' | 'clover'>
}

export class CoverageCollector {
  private config: CoverageConfig
  private coverageData: CoverageData | null = null

  constructor(config: Partial<CoverageConfig> = {}) {
    this.config = {
      include: ['src/**/*.{ts,tsx,js,jsx}'],
      exclude: [
        'src/**/*.test.{ts,tsx,js,jsx}',
        'src/**/*.spec.{ts,tsx,js,jsx}',
        'src/**/__tests__/**',
        'src/**/__mocks__/**',
        'node_modules/**',
        'dist/**',
        'build/**'
      ],
      thresholds: {
        lines: 80,
        functions: 80,
        branches: 70,
        statements: 80
      },
      outputDir: 'coverage',
      formats: ['html', 'json', 'lcov'],
      reporter: ['html', 'json', 'lcov', 'text-summary'],
      ...config
    }
  }

  /**
   * Collect coverage data from Vitest/c8
   */
  async collectCoverage(): Promise<CoverageData> {
    try {
      // Run tests with coverage
      const coverageCommand = this.buildCoverageCommand()
      execSync(coverageCommand, { stdio: 'inherit' })

      // Read coverage data
      const coverageJson = await this.readCoverageJson()
      this.coverageData = this.parseCoverageData(coverageJson)

      return this.coverageData
    } catch (error) {
      throw new Error(`Failed to collect coverage: ${error.message}`)
    }
  }

  /**
   * Check if coverage meets thresholds
   */
  checkThresholds(coverage: CoverageData): ThresholdResult {
    const results: ThresholdResult = {
      passed: true,
      failures: [],
      summary: {
        lines: coverage.lines.percentage >= this.config.thresholds.lines,
        functions: coverage.functions.percentage >= this.config.thresholds.functions,
        branches: coverage.branches.percentage >= this.config.thresholds.branches,
        statements: coverage.statements.percentage >= this.config.thresholds.statements
      }
    }

    // Check overall thresholds
    if (coverage.lines.percentage < this.config.thresholds.lines) {
      results.passed = false
      results.failures.push({
        type: 'lines',
        actual: coverage.lines.percentage,
        expected: this.config.thresholds.lines,
        message: `Line coverage ${coverage.lines.percentage.toFixed(2)}% is below threshold ${this.config.thresholds.lines}%`
      })
    }

    if (coverage.functions.percentage < this.config.thresholds.functions) {
      results.passed = false
      results.failures.push({
        type: 'functions',
        actual: coverage.functions.percentage,
        expected: this.config.thresholds.functions,
        message: `Function coverage ${coverage.functions.percentage.toFixed(2)}% is below threshold ${this.config.thresholds.functions}%`
      })
    }

    if (coverage.branches.percentage < this.config.thresholds.branches) {
      results.passed = false
      results.failures.push({
        type: 'branches',
        actual: coverage.branches.percentage,
        expected: this.config.thresholds.branches,
        message: `Branch coverage ${coverage.branches.percentage.toFixed(2)}% is below threshold ${this.config.thresholds.branches}%`
      })
    }

    if (coverage.statements.percentage < this.config.thresholds.statements) {
      results.passed = false
      results.failures.push({
        type: 'statements',
        actual: coverage.statements.percentage,
        expected: this.config.thresholds.statements,
        message: `Statement coverage ${coverage.statements.percentage.toFixed(2)}% is below threshold ${this.config.thresholds.statements}%`
      })
    }

    return results
  }

  /**
   * Generate visual coverage report
   */
  async generateVisualReport(coverage: CoverageData): Promise<string> {
    const htmlContent = this.generateCoverageHTML(coverage)
    const filePath = join(this.config.outputDir, 'coverage-report.html')
    
    await writeFile(filePath, htmlContent, 'utf-8')
    return filePath
  }

  /**
   * Get coverage statistics
   */
  getCoverageStats(coverage: CoverageData): CoverageStats {
    const fileStats = coverage.files.map(file => ({
      path: file.path,
      coverage: (file.lines.percentage + file.functions.percentage + 
                file.branches.percentage + file.statements.percentage) / 4
    }))

    const wellCoveredFiles = fileStats.filter(f => f.coverage >= 80)
    const poorlyCoveredFiles = fileStats.filter(f => f.coverage < 50)
    const averageCoverage = fileStats.reduce((sum, f) => sum + f.coverage, 0) / fileStats.length

    return {
      totalFiles: coverage.files.length,
      wellCoveredFiles: wellCoveredFiles.length,
      poorlyCoveredFiles: poorlyCoveredFiles.length,
      averageCoverage,
      overallScore: (coverage.lines.percentage + coverage.functions.percentage + 
                    coverage.branches.percentage + coverage.statements.percentage) / 4,
      trendData: this.calculateTrends(coverage),
      hotspots: this.identifyHotspots(coverage)
    }
  }

  /**
   * Build coverage command for Vitest
   */
  private buildCoverageCommand(): string {
    const includePatterns = this.config.include.map(pattern => `--coverage.include="${pattern}"`).join(' ')
    const excludePatterns = this.config.exclude.map(pattern => `--coverage.exclude="${pattern}"`).join(' ')
    const reporters = this.config.reporter.map(reporter => `--coverage.reporter=${reporter}`).join(' ')
    
    return `npx vitest run --coverage ${includePatterns} ${excludePatterns} ${reporters} --coverage.reportsDirectory=${this.config.outputDir}`
  }

  /**
   * Read coverage JSON file
   */
  private async readCoverageJson(): Promise<any> {
    try {
      const coveragePath = join(this.config.outputDir, 'coverage-final.json')
      const coverageContent = await readFile(coveragePath, 'utf-8')
      return JSON.parse(coverageContent)
    } catch (error) {
      throw new Error(`Failed to read coverage data: ${error.message}`)
    }
  }

  /**
   * Parse coverage data from c8/v8 format
   */
  private parseCoverageData(coverageJson: any): CoverageData {
    const files: CoverageFileData[] = []
    let totalLines = 0, coveredLines = 0
    let totalFunctions = 0, coveredFunctions = 0
    let totalBranches = 0, coveredBranches = 0
    let totalStatements = 0, coveredStatements = 0

    for (const [filePath, fileData] of Object.entries(coverageJson)) {
      const data = fileData as any
      
      // Calculate file-level coverage
      const fileCoverage: CoverageFileData = {
        path: filePath,
        lines: {
          total: data.s ? Object.keys(data.s).length : 0,
          covered: data.s ? Object.values(data.s).filter((count: any) => count > 0).length : 0,
          percentage: 0
        },
        functions: {
          total: data.f ? Object.keys(data.f).length : 0,
          covered: data.f ? Object.values(data.f).filter((count: any) => count > 0).length : 0,
          percentage: 0
        },
        branches: {
          total: data.b ? Object.keys(data.b).length : 0,
          covered: data.b ? Object.values(data.b).filter((branches: any) => branches.some((count: any) => count > 0)).length : 0,
          percentage: 0
        },
        statements: {
          total: data.s ? Object.keys(data.s).length : 0,
          covered: data.s ? Object.values(data.s).filter((count: any) => count > 0).length : 0,
          percentage: 0
        },
        uncoveredLines: this.getUncoveredLines(data)
      }

      // Calculate percentages
      fileCoverage.lines.percentage = fileCoverage.lines.total > 0 ? 
        (fileCoverage.lines.covered / fileCoverage.lines.total) * 100 : 100
      fileCoverage.functions.percentage = fileCoverage.functions.total > 0 ? 
        (fileCoverage.functions.covered / fileCoverage.functions.total) * 100 : 100
      fileCoverage.branches.percentage = fileCoverage.branches.total > 0 ? 
        (fileCoverage.branches.covered / fileCoverage.branches.total) * 100 : 100
      fileCoverage.statements.percentage = fileCoverage.statements.total > 0 ? 
        (fileCoverage.statements.covered / fileCoverage.statements.total) * 100 : 100

      files.push(fileCoverage)

      // Accumulate totals
      totalLines += fileCoverage.lines.total
      coveredLines += fileCoverage.lines.covered
      totalFunctions += fileCoverage.functions.total
      coveredFunctions += fileCoverage.functions.covered
      totalBranches += fileCoverage.branches.total
      coveredBranches += fileCoverage.branches.covered
      totalStatements += fileCoverage.statements.total
      coveredStatements += fileCoverage.statements.covered
    }

    return {
      lines: {
        total: totalLines,
        covered: coveredLines,
        percentage: totalLines > 0 ? (coveredLines / totalLines) * 100 : 100
      },
      functions: {
        total: totalFunctions,
        covered: coveredFunctions,
        percentage: totalFunctions > 0 ? (coveredFunctions / totalFunctions) * 100 : 100
      },
      branches: {
        total: totalBranches,
        covered: coveredBranches,
        percentage: totalBranches > 0 ? (coveredBranches / totalBranches) * 100 : 100
      },
      statements: {
        total: totalStatements,
        covered: coveredStatements,
        percentage: totalStatements > 0 ? (coveredStatements / totalStatements) * 100 : 100
      },
      files,
      thresholds: this.config.thresholds
    }
  }

  /**
   * Get uncovered line numbers
   */
  private getUncoveredLines(fileData: any): number[] {
    const uncoveredLines: number[] = []
    
    if (fileData.statementMap && fileData.s) {
      for (const [statementId, count] of Object.entries(fileData.s)) {
        if (count === 0) {
          const statement = fileData.statementMap[statementId]
          if (statement && statement.start) {
            uncoveredLines.push(statement.start.line)
          }
        }
      }
    }

    return [...new Set(uncoveredLines)].sort((a, b) => a - b)
  }

  /**
   * Calculate coverage trends
   */
  private calculateTrends(coverage: CoverageData): TrendData {
    // This would typically compare with historical data
    // For now, return current state as baseline
    return {
      linesTrend: 0, // Percentage change from previous run
      functionsTrend: 0,
      branchesTrend: 0,
      statementsTrend: 0,
      overallTrend: 0,
      isImproving: true
    }
  }

  /**
   * Identify coverage hotspots (files needing attention)
   */
  private identifyHotspots(coverage: CoverageData): CoverageHotspot[] {
    return coverage.files
      .filter(file => {
        const avgCoverage = (file.lines.percentage + file.functions.percentage + 
                           file.branches.percentage + file.statements.percentage) / 4
        return avgCoverage < 50 || file.uncoveredLines.length > 20
      })
      .map(file => ({
        path: file.path,
        severity: file.lines.percentage < 25 ? 'critical' : 
                 file.lines.percentage < 50 ? 'high' : 'medium',
        issues: [
          ...(file.lines.percentage < 50 ? [`Low line coverage: ${file.lines.percentage.toFixed(1)}%`] : []),
          ...(file.functions.percentage < 50 ? [`Low function coverage: ${file.functions.percentage.toFixed(1)}%`] : []),
          ...(file.branches.percentage < 50 ? [`Low branch coverage: ${file.branches.percentage.toFixed(1)}%`] : []),
          ...(file.uncoveredLines.length > 20 ? [`${file.uncoveredLines.length} uncovered lines`] : [])
        ],
        recommendations: [
          'Add unit tests for uncovered functions',
          'Test edge cases and error conditions',
          'Improve branch coverage with conditional tests'
        ]
      }))
      .sort((a, b) => {
        const severityOrder = { critical: 3, high: 2, medium: 1 }
        return severityOrder[b.severity] - severityOrder[a.severity]
      })
      .slice(0, 10) // Top 10 hotspots
  }

  /**
   * Generate HTML coverage report
   */
  private generateCoverageHTML(coverage: CoverageData): string {
    const thresholdResult = this.checkThresholds(coverage)
    const stats = this.getCoverageStats(coverage)

    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Code Coverage Report</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { background: linear-gradient(135deg, #10b981 0%, #059669 100%); color: white; padding: 30px; border-radius: 8px 8px 0 0; }
        .header h1 { margin: 0; font-size: 2.5em; }
        .content { padding: 30px; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .metric { background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center; }
        .metric.good { border-left: 4px solid #10b981; }
        .metric.warning { border-left: 4px solid #f59e0b; }
        .metric.poor { border-left: 4px solid #ef4444; }
        .metric h3 { margin: 0 0 10px 0; color: #666; font-size: 0.9em; text-transform: uppercase; }
        .metric .value { font-size: 2em; font-weight: bold; color: #333; }
        .coverage-bar { background: #e5e7eb; height: 20px; border-radius: 10px; overflow: hidden; margin: 10px 0; }
        .coverage-fill { height: 100%; transition: width 0.3s ease; }
        .coverage-fill.good { background: #10b981; }
        .coverage-fill.warning { background: #f59e0b; }
        .coverage-fill.poor { background: #ef4444; }
        .section { margin-bottom: 40px; }
        .section h2 { color: #333; border-bottom: 2px solid #eee; padding-bottom: 10px; }
        .file-list { margin-top: 20px; }
        .file-item { background: #f8f9fa; margin-bottom: 10px; border-radius: 8px; padding: 15px; }
        .file-path { font-weight: bold; color: #333; margin-bottom: 10px; }
        .file-metrics { display: grid; grid-template-columns: repeat(4, 1fr); gap: 15px; }
        .file-metric { text-align: center; }
        .file-metric .label { font-size: 0.8em; color: #666; text-transform: uppercase; }
        .file-metric .value { font-weight: bold; font-size: 1.2em; }
        .hotspots { background: #fef3c7; border: 1px solid #f59e0b; border-radius: 8px; padding: 20px; }
        .hotspot { background: white; margin-bottom: 15px; padding: 15px; border-radius: 6px; border-left: 4px solid #ef4444; }
        .hotspot.high { border-left-color: #f59e0b; }
        .hotspot.medium { border-left-color: #6b7280; }
        .hotspot-path { font-weight: bold; color: #333; margin-bottom: 8px; }
        .hotspot-issues { list-style: none; padding: 0; margin: 8px 0; }
        .hotspot-issues li { background: #f3f4f6; padding: 4px 8px; margin: 4px 0; border-radius: 4px; font-size: 0.9em; }
        .threshold-status { display: inline-block; padding: 4px 8px; border-radius: 4px; font-size: 0.8em; font-weight: bold; }
        .threshold-pass { background: #d1fae5; color: #065f46; }
        .threshold-fail { background: #fee2e2; color: #991b1b; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Code Coverage Report</h1>
            <div>Generated: ${new Date().toLocaleString()}</div>
        </div>
        
        <div class="content">
            <div class="section">
                <h2>Coverage Summary 
                    <span class="threshold-status ${thresholdResult.passed ? 'threshold-pass' : 'threshold-fail'}">
                        ${thresholdResult.passed ? 'PASSED' : 'FAILED'}
                    </span>
                </h2>
                <div class="summary">
                    <div class="metric ${this.getCoverageClass(coverage.lines.percentage)}">
                        <h3>Lines</h3>
                        <div class="value">${coverage.lines.percentage.toFixed(1)}%</div>
                        <div class="coverage-bar">
                            <div class="coverage-fill ${this.getCoverageClass(coverage.lines.percentage)}" style="width: ${coverage.lines.percentage}%"></div>
                        </div>
                        <div>${coverage.lines.covered}/${coverage.lines.total}</div>
                    </div>
                    <div class="metric ${this.getCoverageClass(coverage.functions.percentage)}">
                        <h3>Functions</h3>
                        <div class="value">${coverage.functions.percentage.toFixed(1)}%</div>
                        <div class="coverage-bar">
                            <div class="coverage-fill ${this.getCoverageClass(coverage.functions.percentage)}" style="width: ${coverage.functions.percentage}%"></div>
                        </div>
                        <div>${coverage.functions.covered}/${coverage.functions.total}</div>
                    </div>
                    <div class="metric ${this.getCoverageClass(coverage.branches.percentage)}">
                        <h3>Branches</h3>
                        <div class="value">${coverage.branches.percentage.toFixed(1)}%</div>
                        <div class="coverage-bar">
                            <div class="coverage-fill ${this.getCoverageClass(coverage.branches.percentage)}" style="width: ${coverage.branches.percentage}%"></div>
                        </div>
                        <div>${coverage.branches.covered}/${coverage.branches.total}</div>
                    </div>
                    <div class="metric ${this.getCoverageClass(coverage.statements.percentage)}">
                        <h3>Statements</h3>
                        <div class="value">${coverage.statements.percentage.toFixed(1)}%</div>
                        <div class="coverage-bar">
                            <div class="coverage-fill ${this.getCoverageClass(coverage.statements.percentage)}" style="width: ${coverage.statements.percentage}%"></div>
                        </div>
                        <div>${coverage.statements.covered}/${coverage.statements.total}</div>
                    </div>
                </div>
            </div>

            ${stats.hotspots.length > 0 ? `
            <div class="section">
                <h2>Coverage Hotspots</h2>
                <div class="hotspots">
                    <p>Files that need attention to improve coverage:</p>
                    ${stats.hotspots.map(hotspot => `
                        <div class="hotspot ${hotspot.severity}">
                            <div class="hotspot-path">${hotspot.path}</div>
                            <ul class="hotspot-issues">
                                ${hotspot.issues.map(issue => `<li>${issue}</li>`).join('')}
                            </ul>
                        </div>
                    `).join('')}
                </div>
            </div>
            ` : ''}

            <div class="section">
                <h2>File Coverage Details</h2>
                <div class="file-list">
                    ${coverage.files.map(file => `
                        <div class="file-item">
                            <div class="file-path">${file.path}</div>
                            <div class="file-metrics">
                                <div class="file-metric">
                                    <div class="label">Lines</div>
                                    <div class="value">${file.lines.percentage.toFixed(1)}%</div>
                                </div>
                                <div class="file-metric">
                                    <div class="label">Functions</div>
                                    <div class="value">${file.functions.percentage.toFixed(1)}%</div>
                                </div>
                                <div class="file-metric">
                                    <div class="label">Branches</div>
                                    <div class="value">${file.branches.percentage.toFixed(1)}%</div>
                                </div>
                                <div class="file-metric">
                                    <div class="label">Statements</div>
                                    <div class="value">${file.statements.percentage.toFixed(1)}%</div>
                                </div>
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        </div>
    </div>
</body>
</html>
    `.trim()
  }

  /**
   * Get CSS class based on coverage percentage
   */
  private getCoverageClass(percentage: number): string {
    if (percentage >= 80) return 'good'
    if (percentage >= 60) return 'warning'
    return 'poor'
  }
}

// Additional interfaces
export interface ThresholdResult {
  passed: boolean
  failures: ThresholdFailure[]
  summary: {
    lines: boolean
    functions: boolean
    branches: boolean
    statements: boolean
  }
}

export interface ThresholdFailure {
  type: 'lines' | 'functions' | 'branches' | 'statements'
  actual: number
  expected: number
  message: string
}

export interface CoverageStats {
  totalFiles: number
  wellCoveredFiles: number
  poorlyCoveredFiles: number
  averageCoverage: number
  overallScore: number
  trendData: TrendData
  hotspots: CoverageHotspot[]
}

export interface TrendData {
  linesTrend: number
  functionsTrend: number
  branchesTrend: number
  statementsTrend: number
  overallTrend: number
  isImproving: boolean
}

export interface CoverageHotspot {
  path: string
  severity: 'critical' | 'high' | 'medium'
  issues: string[]
  recommendations: string[]
}