import { APITestClient } from './api-test-client'
import { TestServerManager } from './test-server-manager'
import { DatabaseTestManager } from './database-test-manager'

export interface APITestSetup {
  client: APITestClient
  server: TestServerManager
  database: DatabaseTestManager
  baseURL: string
}

export interface AuthTestUser {
  id: string
  email: string
  name: string
  token?: string
}

/**
 * API 測試工具類
 */
export class APITestUtils {
  private static instance: APITestUtils
  private setup?: APITestSetup

  static getInstance(): APITestUtils {
    if (!APITestUtils.instance) {
      APITestUtils.instance = new APITestUtils()
    }
    return APITestUtils.instance
  }

  /**
   * 設置 API 測試環境
   */
  async setupAPITest(): Promise<APITestSetup> {
    if (this.setup) {
      return this.setup
    }

    try {
      // 建立測試伺服器管理器
      const server = new TestServerManager({
        database: {
          resetOnStart: true,
          seedData: true
        },
        middleware: {
          cors: true,
          logging: false,
          errorHandling: true,
          bodyParser: true
        }
      })

      // 啟動伺服器
      await server.start()
      await server.waitForReady()

      // 建立 API 測試客戶端
      const client = new APITestClient(server.getApp())
      
      // 建立資料庫管理器
      const database = new DatabaseTestManager()

      const baseURL = server.getBaseURL()

      this.setup = {
        client,
        server,
        database,
        baseURL
      }

      return this.setup
    } catch (error) {
      console.error('Failed to setup API test environment:', error)
      throw error
    }
  }

  /**
   * 清理 API 測試環境
   */
  async cleanupAPITest(): Promise<void> {
    if (!this.setup) {
      return
    }

    try {
      await this.setup.server.stop()
      await this.setup.database.cleanup()
      this.setup = undefined
    } catch (error) {
      console.error('Failed to cleanup API test environment:', error)
      throw error
    }
  }

  /**
   * 建立測試用戶並取得認證 token
   */
  async createAuthenticatedUser(userData?: Partial<AuthTestUser>): Promise<AuthTestUser> {
    if (!this.setup) {
      throw new Error('API test environment not setup')
    }

    const user = await this.setup.database.createTestData('user', {
      email: userData?.email || `test-${Date.now()}@example.com`,
      name: userData?.name || 'Test User',
      ...userData
    })

    // 模擬登入取得 token (這裡需要根據實際認證邏輯調整)
    const token = this.generateTestToken(user.id)
    
    return {
      ...user,
      token
    }
  }

  /**
   * 生成測試用 JWT token
   */
  private generateTestToken(userId: string): string {
    // 這裡應該使用與實際應用相同的 JWT 簽名邏輯
    // 為了測試目的，這裡使用簡化版本
    const payload = {
      userId,
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + (60 * 60) // 1 hour
    }
    
    // 在實際實作中，這裡應該使用 jsonwebtoken 庫
    return `test-token-${userId}-${payload.iat}`
  }

  /**
   * 驗證 API 回應格式
   */
  validateAPIResponse(response: any, expectedFields: string[]): void {
    if (!response || typeof response !== 'object') {
      throw new Error('Response should be an object')
    }

    for (const field of expectedFields) {
      if (!(field in response)) {
        throw new Error(`Response missing required field: ${field}`)
      }
    }
  }

  /**
   * 驗證分頁回應格式
   */
  validatePaginatedResponse(response: any): void {
    this.validateAPIResponse(response, ['data', 'pagination'])
    
    if (!Array.isArray(response.data)) {
      throw new Error('Paginated response data should be an array')
    }

    this.validateAPIResponse(response.pagination, ['page', 'limit', 'total', 'totalPages'])
  }

  /**
   * 驗證錯誤回應格式
   */
  validateErrorResponse(response: any, expectedStatus: number): void {
    if (response.status !== expectedStatus) {
      throw new Error(`Expected status ${expectedStatus} but got ${response.status}`)
    }

    if (!response.data || typeof response.data !== 'object') {
      throw new Error('Error response data should be an object')
    }

    if (!('error' in response.data) && !('message' in response.data)) {
      throw new Error('Error response should contain error or message field')
    }
  }

  /**
   * 等待異步操作完成
   */
  async waitForCondition(
    condition: () => boolean | Promise<boolean>,
    timeout: number = 5000,
    interval: number = 100
  ): Promise<void> {
    const startTime = Date.now()
    
    while (Date.now() - startTime < timeout) {
      const result = await condition()
      if (result) {
        return
      }
      await new Promise(resolve => setTimeout(resolve, interval))
    }
    
    throw new Error(`Condition not met within ${timeout}ms`)
  }

  /**
   * 建立測試專案
   */
  async createTestProject(userId: string, projectData?: any): Promise<any> {
    if (!this.setup) {
      throw new Error('API test environment not setup')
    }

    return await this.setup.database.createTestData('project', {
      name: projectData?.name || `Test Project ${Date.now()}`,
      description: projectData?.description || 'Test project description',
      userId,
      ...projectData
    })
  }

  /**
   * 建立測試檔案
   */
  async createTestFile(projectId: string, fileData?: any): Promise<any> {
    if (!this.setup) {
      throw new Error('API test environment not setup')
    }

    return await this.setup.database.createTestData('file', {
      name: fileData?.name || `test-file-${Date.now()}.txt`,
      content: fileData?.content || 'Test file content',
      projectId,
      ...fileData
    })
  }

  /**
   * 重置測試環境
   */
  async resetTestEnvironment(): Promise<void> {
    if (!this.setup) {
      return
    }

    await this.setup.server.reset()
    await this.setup.database.resetDatabase()
  }

  /**
   * 取得當前設置
   */
  getSetup(): APITestSetup | undefined {
    return this.setup
  }
}

// 匯出單例實例
export const apiTestUtils = APITestUtils.getInstance()

// 測試輔助函數
export async function withAPITest<T>(
  testFn: (setup: APITestSetup) => Promise<T>
): Promise<T> {
  const utils = APITestUtils.getInstance()
  const setup = await utils.setupAPITest()
  
  try {
    return await testFn(setup)
  } finally {
    await utils.resetTestEnvironment()
  }
}

// 認證測試輔助函數
export async function withAuthenticatedUser<T>(
  testFn: (setup: APITestSetup, user: AuthTestUser) => Promise<T>,
  userData?: Partial<AuthTestUser>
): Promise<T> {
  return await withAPITest(async (setup) => {
    const utils = APITestUtils.getInstance()
    const user = await utils.createAuthenticatedUser(userData)
    
    // 設置認證 token
    setup.client.setAuthToken(user.token!)
    
    try {
      return await testFn(setup, user)
    } finally {
      setup.client.clearAuth()
    }
  })
}