import { MockWebSocket } from './mock-websocket'

export interface WebSocketTestConfig {
  url?: string
  protocols?: string | string[]
  connectionDelay?: number
  networkDelay?: number
  shouldFailConnection?: boolean
  autoConnect?: boolean
}

export interface MessageTestCase {
  name: string
  message: string | ArrayBuffer | Blob
  expectedResponse?: string | ArrayBuffer | Blob
  shouldFail?: boolean
  timeout?: number
}

export interface ConnectionTestScenario {
  name: string
  steps: Array<{
    action: 'connect' | 'disconnect' | 'send' | 'wait' | 'expect'
    data?: any
    timeout?: number
    expectedState?: number
  }>
}

/**
 * WebSocket Test Utilities for comprehensive WebSocket testing
 */
export class WebSocketTestUtils {
  private mockWebSocket: MockWebSocket | null = null
  private messageHistory: Array<{ timestamp: number; type: 'sent' | 'received'; data: any }> = []
  private connectionHistory: Array<{ timestamp: number; state: number; event: string }> = []

  /**
   * Create a mock WebSocket instance with test configuration
   */
  createMockWebSocket(config: WebSocketTestConfig = {}): MockWebSocket {
    const {
      url = 'ws://localhost:8080/test',
      protocols,
      connectionDelay = 0,
      networkDelay = 0,
      shouldFailConnection = false,
      autoConnect = true
    } = config

    this.mockWebSocket = new MockWebSocket(url, protocols)
    
    // Configure mock behavior
    this.mockWebSocket.setConnectionDelay(connectionDelay)
    this.mockWebSocket.setNetworkDelay(networkDelay)
    this.mockWebSocket.setConnectionFailure(shouldFailConnection)

    // Set up event tracking
    this.setupEventTracking()

    return this.mockWebSocket
  }

  /**
   * Set up event tracking for testing
   */
  private setupEventTracking(): void {
    if (!this.mockWebSocket) return

    const originalDispatchEvent = this.mockWebSocket.dispatchEvent.bind(this.mockWebSocket)
    
    this.mockWebSocket.dispatchEvent = (event: Event) => {
      this.connectionHistory.push({
        timestamp: Date.now(),
        state: this.mockWebSocket!.readyState,
        event: event.type
      })

      if (event.type === 'message') {
        const messageEvent = event as MessageEvent
        this.messageHistory.push({
          timestamp: Date.now(),
          type: 'received',
          data: messageEvent.data
        })
      }

      return originalDispatchEvent(event)
    }

    // Track sent messages
    const originalSend = this.mockWebSocket.send.bind(this.mockWebSocket)
    this.mockWebSocket.send = (data: string | ArrayBuffer | Blob) => {
      this.messageHistory.push({
        timestamp: Date.now(),
        type: 'sent',
        data: data
      })
      return originalSend(data)
    }
  }

  /**
   * Wait for WebSocket to reach a specific state
   */
  async waitForState(expectedState: number, timeout: number = 5000): Promise<void> {
    if (!this.mockWebSocket) {
      throw new Error('No WebSocket instance available')
    }

    return new Promise((resolve, reject) => {
      const startTime = Date.now()
      
      const checkState = () => {
        if (this.mockWebSocket!.readyState === expectedState) {
          resolve()
          return
        }

        if (Date.now() - startTime > timeout) {
          reject(new Error(`Timeout waiting for WebSocket state ${expectedState}. Current state: ${this.mockWebSocket!.readyState}`))
          return
        }

        setTimeout(checkState, 10)
      }

      checkState()
    })
  }

  /**
   * Wait for a specific message to be received
   */
  async waitForMessage(expectedMessage: string | RegExp, timeout: number = 5000): Promise<MessageEvent> {
    return new Promise((resolve, reject) => {
      const startTime = Date.now()
      let messageListener: (event: MessageEvent) => void

      const timeoutId = setTimeout(() => {
        if (this.mockWebSocket && messageListener) {
          this.mockWebSocket.removeEventListener('message', messageListener)
        }
        reject(new Error(`Timeout waiting for message: ${expectedMessage}`))
      }, timeout)

      messageListener = (event: MessageEvent) => {
        const messageData = typeof event.data === 'string' ? event.data : JSON.stringify(event.data)
        
        let matches = false
        if (typeof expectedMessage === 'string') {
          matches = messageData === expectedMessage
        } else {
          matches = expectedMessage.test(messageData)
        }

        if (matches) {
          clearTimeout(timeoutId)
          this.mockWebSocket!.removeEventListener('message', messageListener)
          resolve(event)
        }
      }

      if (this.mockWebSocket) {
        this.mockWebSocket.addEventListener('message', messageListener)
      } else {
        clearTimeout(timeoutId)
        reject(new Error('No WebSocket instance available'))
      }
    })
  }

  /**
   * Send message and wait for response
   */
  async sendAndWaitForResponse(
    message: string | ArrayBuffer | Blob,
    expectedResponse?: string | RegExp,
    timeout: number = 5000
  ): Promise<MessageEvent | null> {
    if (!this.mockWebSocket) {
      throw new Error('No WebSocket instance available')
    }

    if (this.mockWebSocket.readyState !== MockWebSocket.OPEN) {
      throw new Error('WebSocket is not open')
    }

    // Send the message
    this.mockWebSocket.send(message)

    // If no expected response, return null
    if (!expectedResponse) {
      return null
    }

    // Wait for the expected response
    return this.waitForMessage(expectedResponse, timeout)
  }

  /**
   * Simulate connection failure scenarios
   */
  simulateConnectionError(): void {
    if (this.mockWebSocket) {
      this.mockWebSocket.simulateError('Connection failed')
    }
  }

  /**
   * Simulate network delay
   */
  simulateNetworkDelay(delay: number): void {
    if (this.mockWebSocket) {
      this.mockWebSocket.setNetworkDelay(delay)
    }
  }

  /**
   * Simulate server disconnection
   */
  simulateServerDisconnect(code: number = 1000, reason: string = 'Server disconnect'): void {
    if (this.mockWebSocket) {
      this.mockWebSocket.simulateServerClose(code, reason)
    }
  }

  /**
   * Test message sending and receiving
   */
  async testMessageExchange(testCases: MessageTestCase[]): Promise<Array<{ name: string; success: boolean; error?: string }>> {
    const results: Array<{ name: string; success: boolean; error?: string }> = []

    for (const testCase of testCases) {
      try {
        const response = await this.sendAndWaitForResponse(
          testCase.message,
          testCase.expectedResponse ? new RegExp(testCase.expectedResponse.toString()) : undefined,
          testCase.timeout || 5000
        )

        if (testCase.shouldFail) {
          results.push({
            name: testCase.name,
            success: false,
            error: 'Expected test to fail but it succeeded'
          })
        } else {
          results.push({
            name: testCase.name,
            success: true
          })
        }
      } catch (error) {
        if (testCase.shouldFail) {
          results.push({
            name: testCase.name,
            success: true
          })
        } else {
          results.push({
            name: testCase.name,
            success: false,
            error: error.message
          })
        }
      }
    }

    return results
  }

  /**
   * Test connection scenarios
   */
  async testConnectionScenario(scenario: ConnectionTestScenario): Promise<{ success: boolean; error?: string }> {
    try {
      for (const step of scenario.steps) {
        switch (step.action) {
          case 'connect':
            if (!this.mockWebSocket) {
              this.createMockWebSocket()
            }
            await this.waitForState(MockWebSocket.OPEN, step.timeout || 5000)
            break

          case 'disconnect':
            if (this.mockWebSocket) {
              this.mockWebSocket.close(step.data?.code, step.data?.reason)
              await this.waitForState(MockWebSocket.CLOSED, step.timeout || 5000)
            }
            break

          case 'send':
            if (this.mockWebSocket && step.data) {
              this.mockWebSocket.send(step.data)
            }
            break

          case 'wait':
            await new Promise(resolve => setTimeout(resolve, step.timeout || 1000))
            break

          case 'expect':
            if (this.mockWebSocket && step.expectedState !== undefined) {
              if (this.mockWebSocket.readyState !== step.expectedState) {
                throw new Error(`Expected state ${step.expectedState}, got ${this.mockWebSocket.readyState}`)
              }
            }
            break
        }
      }

      return { success: true }
    } catch (error) {
      return { success: false, error: error.message }
    }
  }

  /**
   * Get message history for analysis
   */
  getMessageHistory(): Array<{ timestamp: number; type: 'sent' | 'received'; data: any }> {
    return [...this.messageHistory]
  }

  /**
   * Get connection history for analysis
   */
  getConnectionHistory(): Array<{ timestamp: number; state: number; event: string }> {
    return [...this.connectionHistory]
  }

  /**
   * Clear all history
   */
  clearHistory(): void {
    this.messageHistory = []
    this.connectionHistory = []
  }

  /**
   * Get current WebSocket instance
   */
  getWebSocket(): MockWebSocket | null {
    return this.mockWebSocket
  }

  /**
   * Reset the test utilities
   */
  reset(): void {
    if (this.mockWebSocket) {
      this.mockWebSocket.reset()
    }
    this.clearHistory()
    this.mockWebSocket = null
  }

  /**
   * Assert WebSocket state
   */
  expectState(expectedState: number): void {
    if (!this.mockWebSocket) {
      throw new Error('No WebSocket instance available')
    }

    if (this.mockWebSocket.readyState !== expectedState) {
      throw new Error(`Expected WebSocket state ${expectedState}, but got ${this.mockWebSocket.readyState}`)
    }
  }

  /**
   * Assert message was sent
   */
  expectMessageSent(message: string | ArrayBuffer | Blob): void {
    const sentMessages = this.messageHistory.filter(m => m.type === 'sent')
    const found = sentMessages.some(m => {
      if (typeof message === 'string' && typeof m.data === 'string') {
        return m.data === message
      }
      return JSON.stringify(m.data) === JSON.stringify(message)
    })

    if (!found) {
      throw new Error(`Expected message "${message}" to be sent, but it was not found in history`)
    }
  }

  /**
   * Assert message was received
   */
  expectMessageReceived(message: string | ArrayBuffer | Blob): void {
    const receivedMessages = this.messageHistory.filter(m => m.type === 'received')
    const found = receivedMessages.some(m => {
      if (typeof message === 'string' && typeof m.data === 'string') {
        return m.data === message
      }
      return JSON.stringify(m.data) === JSON.stringify(message)
    })

    if (!found) {
      throw new Error(`Expected message "${message}" to be received, but it was not found in history`)
    }
  }

  /**
   * Create heartbeat test utility
   */
  createHeartbeatTest(interval: number = 30000): {
    start: () => void
    stop: () => void
    getStats: () => { sent: number; received: number; missed: number }
  } {
    let heartbeatInterval: NodeJS.Timeout | null = null
    let stats = { sent: 0, received: 0, missed: 0 }

    return {
      start: () => {
        if (heartbeatInterval) return

        heartbeatInterval = setInterval(() => {
          if (this.mockWebSocket && this.mockWebSocket.readyState === MockWebSocket.OPEN) {
            try {
              this.mockWebSocket.send(JSON.stringify({ type: 'ping', timestamp: Date.now() }))
              stats.sent++
            } catch (error) {
              stats.missed++
            }
          }
        }, interval)
      },

      stop: () => {
        if (heartbeatInterval) {
          clearInterval(heartbeatInterval)
          heartbeatInterval = null
        }
      },

      getStats: () => ({ ...stats })
    }
  }
}