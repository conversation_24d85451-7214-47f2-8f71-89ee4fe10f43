import { PrismaClient } from '@prisma/client'
import { randomUUID } from 'crypto'
import { faker } from '@faker-js/faker'

export interface TestDataOptions {
  cleanup?: boolean
  transaction?: boolean
  overrides?: Record<string, any>
}

export interface TestDataRelations {
  projects?: number
  files?: number
  withRelations?: boolean
}

export interface TestDataBatch {
  projects: any[]
  files: any[]
  metadata: {
    createdAt: Date
    totalRecords: number
    relations: TestDataRelations
  }
}

/**
 * Test Data Factory for generating consistent test data
 */
export class TestDataFactory {
  private prisma: PrismaClient
  private createdRecords: Map<string, string[]> = new Map()
  private batchId: string

  constructor(prisma: PrismaClient) {
    this.prisma = prisma
    this.batchId = randomUUID()
  }

  /**
   * Create a project
   */
  async createProject(options: TestDataOptions = {}): Promise<any> {
    const projectData = {
      id: randomUUID(),
      name: faker.company.name() + ' Project',
      description: faker.lorem.paragraph(),
      createdAt: new Date(),
      updatedAt: new Date(),
      ...options.overrides
    }

    const project = await this.prisma.project.create({
      data: projectData
    })

    if (options.cleanup !== false) {
      this.trackRecord('project', project.id)
    }

    return project
  }

  /**
   * Create multiple projects
   */
  async createProjects(count: number, options: TestDataOptions = {}): Promise<any[]> {
    const projects = []
    
    for (let i = 0; i < count; i++) {
      const project = await this.createProject({
        ...options,
        overrides: {
          name: `Test Project ${i + 1} - ${this.batchId}`,
          description: `Test project description ${i + 1}`,
          ...options.overrides
        }
      })
      projects.push(project)
    }

    return projects
  }

  /**
   * Create a file with proper project relationship
   */
  async createFile(projectId: string, options: TestDataOptions = {}): Promise<any> {
    const fileExtensions = ['js', 'ts', 'jsx', 'tsx', 'css', 'html', 'json', 'md']
    const extension = faker.helpers.arrayElement(fileExtensions)
    
    const fileData = {
      id: randomUUID(),
      name: `${faker.system.fileName()}.${extension}`,
      content: this.generateFileContent(extension),
      projectId,
      createdAt: new Date(),
      updatedAt: new Date(),
      ...options.overrides
    }

    const file = await this.prisma.file.create({
      data: fileData
    })

    if (options.cleanup !== false) {
      this.trackRecord('file', file.id)
    }

    return file
  }

  /**
   * Create multiple files for a project
   */
  async createFiles(projectId: string, count: number, options: TestDataOptions = {}): Promise<any[]> {
    const files = []
    
    for (let i = 0; i < count; i++) {
      const file = await this.createFile(projectId, {
        ...options,
        overrides: {
          name: `test-file-${i + 1}-${this.batchId}.txt`,
          content: `Test file content ${i + 1}\n${faker.lorem.paragraphs(2)}`,
          ...options.overrides
        }
      })
      files.push(file)
    }

    return files
  }

  /**
   * Create a complete data set with relationships
   */
  async createDataSet(relations: TestDataRelations, options: TestDataOptions = {}): Promise<TestDataBatch> {
    const batch: TestDataBatch = {
      projects: [],
      files: [],
      metadata: {
        createdAt: new Date(),
        totalRecords: 0,
        relations
      }
    }

    try {
      // Create projects first (no dependencies)
      if (relations.projects && relations.projects > 0) {
        batch.projects = await this.createProjects(relations.projects, options)
      }

      // Create files (depends on projects)
      if (relations.files && relations.files > 0 && batch.projects.length > 0) {
        for (const project of batch.projects) {
          const filesPerProject = Math.ceil(relations.files / batch.projects.length)
          const files = await this.createFiles(project.id, filesPerProject, options)
          batch.files.push(...files)
        }
      }

      batch.metadata.totalRecords = batch.projects.length + batch.files.length

      return batch
    } catch (error) {
      console.error('Failed to create data set:', error)
      throw error
    }
  }

  /**
   * Create realistic project structure
   */
  async createProjectStructure(options: TestDataOptions = {}): Promise<{
    project: any
    files: any[]
  }> {
    const project = await this.createProject({
      ...options,
      overrides: {
        name: 'Sample Web Application',
        description: 'A complete web application with frontend and backend',
        ...options.overrides
      }
    })

    // Create realistic file structure
    const fileStructure = [
      { name: 'package.json', content: this.generatePackageJson() },
      { name: 'README.md', content: this.generateReadme(project.name) },
      { name: 'src/index.js', content: this.generateJavaScript() },
      { name: 'src/components/App.jsx', content: this.generateReactComponent() },
      { name: 'src/styles/main.css', content: this.generateCSS() },
      { name: 'tests/app.test.js', content: this.generateTestFile() }
    ]

    const files = []
    for (const fileData of fileStructure) {
      const file = await this.createFile(project.id, {
        ...options,
        overrides: fileData
      })
      files.push(file)
    }

    return { project, files }
  }

  /**
   * Generate realistic file content based on extension
   */
  private generateFileContent(extension: string): string {
    switch (extension) {
      case 'js':
      case 'ts':
        return this.generateJavaScript()
      case 'jsx':
      case 'tsx':
        return this.generateReactComponent()
      case 'css':
        return this.generateCSS()
      case 'html':
        return this.generateHTML()
      case 'json':
        return this.generateJSON()
      case 'md':
        return this.generateMarkdown()
      default:
        return faker.lorem.paragraphs(3)
    }
  }

  private generateJavaScript(): string {
    return `// ${faker.lorem.sentence()}
function ${faker.hacker.noun()}() {
  const ${faker.hacker.noun()} = '${faker.lorem.word()}';
  console.log('${faker.lorem.sentence()}');
  return ${faker.hacker.noun()};
}

export default ${faker.hacker.noun()};`
  }

  private generateReactComponent(): string {
    const componentName = faker.hacker.noun().charAt(0).toUpperCase() + faker.hacker.noun().slice(1)
    return `import React from 'react';

const ${componentName} = () => {
  return (
    <div className="${faker.lorem.word()}">
      <h1>${faker.lorem.sentence()}</h1>
      <p>${faker.lorem.paragraph()}</p>
    </div>
  );
};

export default ${componentName};`
  }

  private generateCSS(): string {
    return `.${faker.lorem.word()} {
  color: ${faker.internet.color()};
  background-color: ${faker.internet.color()};
  padding: ${faker.number.int({ min: 10, max: 50 })}px;
  margin: ${faker.number.int({ min: 5, max: 20 })}px;
}

.${faker.lorem.word()}-container {
  display: flex;
  justify-content: center;
  align-items: center;
}`
  }

  private generateHTML(): string {
    return `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${faker.lorem.words(3)}</title>
</head>
<body>
  <h1>${faker.lorem.sentence()}</h1>
  <p>${faker.lorem.paragraph()}</p>
</body>
</html>`
  }

  private generateJSON(): string {
    return JSON.stringify({
      name: faker.lorem.word(),
      version: faker.system.semver(),
      description: faker.lorem.sentence(),
      author: faker.person.fullName(),
      dependencies: {
        [faker.lorem.word()]: faker.system.semver(),
        [faker.lorem.word()]: faker.system.semver()
      }
    }, null, 2)
  }

  private generateMarkdown(): string {
    return `# ${faker.lorem.words(3)}

${faker.lorem.paragraph()}

## ${faker.lorem.words(2)}

${faker.lorem.paragraphs(2)}

### Features

- ${faker.lorem.sentence()}
- ${faker.lorem.sentence()}
- ${faker.lorem.sentence()}`
  }

  private generatePackageJson(): string {
    return JSON.stringify({
      name: faker.lorem.word(),
      version: "1.0.0",
      description: faker.lorem.sentence(),
      main: "index.js",
      scripts: {
        start: "node index.js",
        test: "jest",
        build: "webpack --mode production"
      },
      dependencies: {
        express: "^4.18.0",
        react: "^18.0.0"
      },
      devDependencies: {
        jest: "^29.0.0",
        webpack: "^5.0.0"
      }
    }, null, 2)
  }

  private generateReadme(projectName: string): string {
    return `# ${projectName}

${faker.lorem.paragraph()}

## Installation

\`\`\`bash
npm install
\`\`\`

## Usage

\`\`\`bash
npm start
\`\`\`

## Testing

\`\`\`bash
npm test
\`\`\`

## License

MIT`
  }

  private generateTestFile(): string {
    return `const ${faker.hacker.noun()} = require('../src/index');

describe('${faker.lorem.words(2)}', () => {
  test('${faker.lorem.sentence()}', () => {
    expect(${faker.hacker.noun()}()).toBeDefined();
  });

  test('${faker.lorem.sentence()}', () => {
    const result = ${faker.hacker.noun()}();
    expect(result).toBe('${faker.lorem.word()}');
  });
});`
  }

  /**
   * Track created records for cleanup
   */
  private trackRecord(entity: string, id: string): void {
    if (!this.createdRecords.has(entity)) {
      this.createdRecords.set(entity, [])
    }
    this.createdRecords.get(entity)!.push(id)
  }

  /**
   * Clean up all created records
   */
  async cleanup(): Promise<void> {
    try {
      // Clean up in reverse dependency order
      const entities = ['file', 'project']
      
      for (const entity of entities) {
        const ids = this.createdRecords.get(entity) || []
        if (ids.length === 0) continue

        switch (entity) {
          case 'file':
            await this.prisma.file.deleteMany({
              where: { id: { in: ids } }
            })
            break
          case 'project':
            await this.prisma.project.deleteMany({
              where: { id: { in: ids } }
            })
            break
        }
      }

      this.createdRecords.clear()
      console.log('Test data factory cleanup completed')
    } catch (error) {
      console.error('Failed to cleanup test data factory:', error)
      throw error
    }
  }

  /**
   * Get statistics about created records
   */
  getStatistics(): Record<string, number> {
    const stats: Record<string, number> = {}
    
    for (const [entity, ids] of this.createdRecords.entries()) {
      stats[entity] = ids.length
    }
    
    return stats
  }

  /**
   * Reset factory state
   */
  reset(): void {
    this.createdRecords.clear()
    this.batchId = randomUUID()
  }
}