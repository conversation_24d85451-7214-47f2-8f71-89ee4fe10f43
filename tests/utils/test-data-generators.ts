// 測試資料生成工具
import { randomUtils } from './test-helpers'

/**
 * 專案測試資料生成器
 */
export const projectDataGenerator = {
  /**
   * 生成有效的專案資料
   */
  generateValidProject() {
    return {
      name: `專案-${randomUtils.generateRandomString(6)}`,
      description: `這是一個測試專案的描述 ${randomUtils.generateRandomString(10)}`
    }
  },

  /**
   * 生成無效的專案資料
   */
  generateInvalidProject() {
    return {
      name: '', // 空名稱
      description: 'A'.repeat(1001) // 過長描述
    }
  },

  /**
   * 生成專案列表
   */
  generateProjectList(count: number = 5) {
    return Array.from({ length: count }, () => this.generateValidProject())
  }
}

/**
 * 檔案測試資料生成器
 */
export const fileDataGenerator = {
  /**
   * 生成 Vue 組件檔案
   */
  generateVueFile(componentName?: string) {
    const name = componentName || `Component${randomUtils.generateRandomString(4)}`
    return {
      name: `${name}.vue`,
      content: `<template>
  <div class="${name.toLowerCase()}">
    <h1>{{ title }}</h1>
    <p>{{ description }}</p>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const title = ref('${name}')
const description = ref('這是 ${name} 組件')
</script>

<style scoped>
.${name.toLowerCase()} {
  padding: 1rem;
}
</style>`
    }
  },

  /**
   * 生成 TypeScript 檔案
   */
  generateTsFile(fileName?: string) {
    const name = fileName || `utils-${randomUtils.generateRandomString(4)}`
    return {
      name: `${name}.ts`,
      content: `// ${name} 工具函數
export function ${name}Function() {
  return '${name} 函數執行結果'
}

export const ${name}Constant = '${name} 常數'

export interface ${name}Interface {
  id: string
  name: string
  value: number
}`
    }
  },

  /**
   * 生成 JSON 檔案
   */
  generateJsonFile(fileName?: string) {
    const name = fileName || `config-${randomUtils.generateRandomString(4)}`
    return {
      name: `${name}.json`,
      content: JSON.stringify({
        name,
        version: '1.0.0',
        description: `${name} 配置檔案`,
        settings: {
          enabled: true,
          timeout: randomUtils.generateRandomNumber(1000, 5000),
          retries: randomUtils.generateRandomNumber(1, 5)
        }
      }, null, 2)
    }
  },

  /**
   * 生成大型檔案內容
   */
  generateLargeFileContent(sizeInKB: number = 100) {
    const chunkSize = 1024 // 1KB
    const chunks = []
    
    for (let i = 0; i < sizeInKB; i++) {
      chunks.push('A'.repeat(chunkSize))
    }
    
    return chunks.join('')
  }
}

/**
 * 使用者測試資料生成器
 */
export const userDataGenerator = {
  /**
   * 生成有效使用者資料
   */
  generateValidUser() {
    return {
      id: randomUtils.generateRandomId(),
      username: `user_${randomUtils.generateRandomString(8)}`,
      email: randomUtils.generateRandomEmail(),
      password: randomUtils.generateRandomString(12),
      firstName: `名字${randomUtils.generateRandomString(3)}`,
      lastName: `姓氏${randomUtils.generateRandomString(3)}`,
      isActive: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  },

  /**
   * 生成無效使用者資料
   */
  generateInvalidUser() {
    return {
      username: '', // 空使用者名稱
      email: 'invalid-email', // 無效 email
      password: '123', // 過短密碼
      firstName: '',
      lastName: ''
    }
  }
}

/**
 * API 回應資料生成器
 */
export const apiResponseGenerator = {
  /**
   * 生成成功回應
   */
  generateSuccessResponse<T>(data: T, message?: string) {
    return {
      success: true,
      data,
      message: message || '操作成功',
      timestamp: new Date().toISOString()
    }
  },

  /**
   * 生成錯誤回應
   */
  generateErrorResponse(message: string, code?: string) {
    return {
      success: false,
      error: {
        message,
        code: code || 'UNKNOWN_ERROR',
        timestamp: new Date().toISOString()
      }
    }
  },

  /**
   * 生成分頁回應
   */
  generatePaginatedResponse<T>(items: T[], page: number = 1, limit: number = 10) {
    const total = items.length
    const totalPages = Math.ceil(total / limit)
    const startIndex = (page - 1) * limit
    const endIndex = startIndex + limit
    const paginatedItems = items.slice(startIndex, endIndex)

    return {
      success: true,
      data: paginatedItems,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    }
  }
}

/**
 * WebSocket 訊息生成器
 */
export const wsMessageGenerator = {
  /**
   * 生成聊天訊息
   */
  generateChatMessage(content?: string) {
    return {
      type: 'chat',
      data: {
        id: randomUtils.generateRandomId(),
        content: content || `測試訊息 ${randomUtils.generateRandomString(10)}`,
        timestamp: new Date().toISOString(),
        sender: 'user'
      }
    }
  },

  /**
   * 生成 AI 回應訊息
   */
  generateAiResponse(content?: string) {
    return {
      type: 'ai_response',
      data: {
        id: randomUtils.generateRandomId(),
        content: content || `AI 回應 ${randomUtils.generateRandomString(10)}`,
        timestamp: new Date().toISOString(),
        sender: 'ai'
      }
    }
  },

  /**
   * 生成串流訊息
   */
  generateStreamMessage(chunk: string, isComplete: boolean = false) {
    return {
      type: 'stream',
      data: {
        id: randomUtils.generateRandomId(),
        chunk,
        isComplete,
        timestamp: new Date().toISOString()
      }
    }
  },

  /**
   * 生成錯誤訊息
   */
  generateErrorMessage(error: string) {
    return {
      type: 'error',
      data: {
        id: randomUtils.generateRandomId(),
        error,
        timestamp: new Date().toISOString()
      }
    }
  }
}