import { describe, it, expect, beforeAll } from 'vitest'
import { execSync } from 'child_process'
import { existsSync, mkdirSync } from 'fs'
import { resolve } from 'path'

/**
 * CI 整合測試
 * 這些測試用於驗證 CI 環境中的測試執行是否正確
 */
describe('CI 整合測試', () => {
  const testResultsDir = resolve(process.cwd(), 'test-results')
  
  // 確保測試結果目錄存在
  beforeAll(() => {
    if (!existsSync(testResultsDir)) {
      mkdirSync(testResultsDir, { recursive: true })
    }
  })

  // 測試 CI 友好的測試配置
  it('應該有 CI 友好的測試配置', () => {
    // 檢查 CI 配置檔案是否存在
    const ciConfigPath = resolve(process.cwd(), 'tests/config/vitest.config.ci.ts')
    expect(existsSync(ciConfigPath)).toBe(true)
    
    // 檢查 CI 設置檔案是否存在
    const ciSetupPath = resolve(process.cwd(), 'tests/setup/ci-setup.ts')
    expect(existsSync(ciSetupPath)).toBe(true)
  })

  // 測試並行測試執行
  it('應該支援並行測試執行', () => {
    // 檢查並行配置檔案是否存在
    const parallelConfigPath = resolve(process.cwd(), 'tests/config/vitest.config.parallel.ts')
    expect(existsSync(parallelConfigPath)).toBe(true)
    
    // 檢查並行設置檔案是否存在
    const parallelSetupPath = resolve(process.cwd(), 'tests/setup/parallel-setup.ts')
    expect(existsSync(parallelSetupPath)).toBe(true)
  })

  // 測試標準格式的測試輸出
  it('應該支援標準格式的測試輸出', () => {
    // 檢查 package.json 中是否有相關腳本
    try {
      const packageJson = JSON.parse(execSync('cat package.json', { encoding: 'utf-8' }))
      expect(packageJson.scripts['test:ci:report']).toBeDefined()
      expect(packageJson.scripts['test:ci:report']).toContain('junit')
    } catch (error) {
      // 如果無法執行命令，則跳過此測試
      console.warn('無法檢查 package.json 腳本')
    }
  })

  // 測試 CI 工作流程配置
  it('應該有正確的 CI 工作流程配置', () => {
    // 檢查 GitHub Actions 工作流程檔案是否存在
    const workflowPath = resolve(process.cwd(), '.github/workflows/ci.yml')
    expect(existsSync(workflowPath)).toBe(true)
  })
})

/**
 * CI 環境測試執行驗證
 */
describe('CI 環境測試執行驗證', () => {
  // 測試 CI 環境變數
  it('應該正確設置 CI 環境變數', () => {
    // 設置 CI 環境變數用於測試
    process.env.CI = 'true'
    
    expect(process.env.CI).toBe('true')
    expect(process.env.NODE_ENV).toBe('test')
  })

  // 測試 CI 報告生成
  it('應該能夠生成 CI 報告', () => {
    // 這個測試主要是確認報告目錄存在
    const testResultsDir = resolve(process.cwd(), 'test-results')
    expect(existsSync(testResultsDir)).toBe(true)
  })
})