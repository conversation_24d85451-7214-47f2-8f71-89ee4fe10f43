import { describe, it, expect, beforeEach, afterEach } from 'vitest'
import { AIProviderManager } from '../../server/lib/ai/provider-manager'
import { VirtualFileSystem } from '../../src/lib/file-system'
import { AI_PROVIDERS, AI_MODELS } from '../../shared/types/ai'
import type { AIProviderConfig, AIMessage } from '../../shared/types/ai'

describe('AI Service Integration Tests', () => {
  let aiManager: AIProviderManager
  let fileSystem: VirtualFileSystem
  let mockProviderConfig: AIProviderConfig

  beforeEach(async () => {
    // 初始化虛擬檔案系統
    fileSystem = new VirtualFileSystem()
    
    // 初始化 AI 提供者管理器
    aiManager = new AIProviderManager()
    
    // 設定檔案系統
    aiManager.setFileSystem(fileSystem)
    
    // 配置 Mock 提供者用於測試
    mockProviderConfig = {
      id: AI_PROVIDERS.MOCK,
      name: 'Mock AI Provider',
      apiKey: 'mock-key',
      model: 'mock-model',
      isActive: true,
      priority: 1,
      maxTokens: 1000,
      temperature: 0.7,
      costPerInputToken: 0.001,
      costPerOutputToken: 0.002,
    }
    
    await aiManager.registerProvider(mockProviderConfig)
  })

  afterEach(() => {
    // 清理資源
    fileSystem = null as any
    aiManager = null as any
  })

  describe('AI Provider Registration and Management', () => {
    it('should register AI provider successfully', async () => {
      const providerInfo = aiManager.getProviderInfo(AI_PROVIDERS.MOCK)
      
      expect(providerInfo).toBeTruthy()
      expect(providerInfo?.id).toBe(AI_PROVIDERS.MOCK)
      expect(providerInfo?.name).toBe('Mock AI Provider')
      expect(providerInfo?.isActive).toBe(true)
    })

    it('should get all provider information', () => {
      const allProviders = aiManager.getAllProviderInfo()
      
      expect(allProviders).toHaveLength(1)
      expect(allProviders[0].id).toBe(AI_PROVIDERS.MOCK)
    })

    it('should perform health checks on all providers', async () => {
      await aiManager.performHealthChecks()
      
      const statuses = aiManager.getProviderStatuses()
      expect(statuses).toHaveLength(1)
      expect(statuses[0].isHealthy).toBe(true)
      expect(statuses[0].responseTime).toBeGreaterThan(0)
    })

    it('should handle multiple provider registration', async () => {
      // 註冊第二個 Mock 提供者（使用相同的 MOCK 類型但不同配置）
      const secondProviderConfig: AIProviderConfig = {
        id: AI_PROVIDERS.MOCK, // 使用相同的提供者類型
        name: 'Second Mock Provider',
        apiKey: 'mock-key-2',
        model: 'mock-model-2',
        isActive: true,
        priority: 2,
        maxTokens: 2000,
        temperature: 0.5,
        costPerInputToken: 0.002,
        costPerOutputToken: 0.003,
      }
      
      // 由於使用相同的 ID，這會覆蓋第一個提供者
      await aiManager.registerProvider(secondProviderConfig)
      
      const allProviders = aiManager.getAllProviderInfo()
      expect(allProviders).toHaveLength(1) // 只有一個，因為 ID 相同
      expect(allProviders[0].name).toBe('Second Mock Provider')
      
      const statuses = aiManager.getProviderStatuses()
      expect(statuses).toHaveLength(1)
    })
  })

  describe('Content Generation Integration', () => {
    it('should generate content successfully', async () => {
      const messages: AIMessage[] = [
        { role: 'user', content: 'Create a simple Vue button component' }
      ]
      
      const response = await aiManager.generateContent(messages)
      
      expect(response.content).toBeTruthy()
      expect(response.usage).toBeTruthy()
      expect(response.usage?.inputTokens).toBeGreaterThan(0)
      expect(response.usage?.outputTokens).toBeGreaterThan(0)
      expect(response.usage?.totalTokens).toBeGreaterThan(0)
      expect(response.model).toBe('mock-model')
    })

    it('should generate content with options', async () => {
      const messages: AIMessage[] = [
        { role: 'user', content: 'Help me with TypeScript types' }
      ]
      
      const options = {
        maxTokens: 500,
        temperature: 0.3,
        systemPrompt: 'You are a TypeScript expert'
      }
      
      const response = await aiManager.generateContent(messages, options)
      
      expect(response.content).toBeTruthy()
      expect(response.usage?.totalTokens).toBeLessThanOrEqual(500)
    })

    it('should handle conversation context', async () => {
      const messages: AIMessage[] = [
        { role: 'user', content: 'What is Vue.js?' },
        { role: 'assistant', content: 'Vue.js is a progressive JavaScript framework...' },
        { role: 'user', content: 'How do I create a component?' }
      ]
      
      const response = await aiManager.generateContent(messages)
      
      expect(response.content).toBeTruthy()
      expect(response.usage?.inputTokens).toBeGreaterThan(0)
    })

    it('should generate streaming content', async () => {
      const messages: AIMessage[] = [
        { role: 'user', content: 'Explain Vue composition API' }
      ]
      
      const chunks: string[] = []
      let finalUsage: any = null
      
      for await (const chunk of aiManager.generateContentStream(messages)) {
        if (chunk.content) {
          chunks.push(chunk.content)
        }
        if (chunk.isComplete && chunk.usage) {
          finalUsage = chunk.usage
        }
      }
      
      expect(chunks.length).toBeGreaterThan(0)
      expect(chunks.join('')).toBeTruthy()
      expect(finalUsage).toBeTruthy()
      expect(finalUsage.totalTokens).toBeGreaterThan(0)
    })
  })

  describe('Chat Flow Integration', () => {
    it('should handle complete chat conversation flow', async () => {
      // 模擬完整的聊天流程
      const conversation: AIMessage[] = []
      
      // 用戶第一個訊息
      const userMessage1: AIMessage = {
        role: 'user',
        content: 'I need to create a Vue component for a todo list'
      }
      conversation.push(userMessage1)
      
      // AI 回應
      const aiResponse1 = await aiManager.generateContent(conversation)
      expect(aiResponse1.content).toBeTruthy()
      
      conversation.push({
        role: 'assistant',
        content: aiResponse1.content
      })
      
      // 用戶後續訊息
      const userMessage2: AIMessage = {
        role: 'user',
        content: 'Add TypeScript support and make it responsive'
      }
      conversation.push(userMessage2)
      
      // AI 第二次回應
      const aiResponse2 = await aiManager.generateContent(conversation)
      expect(aiResponse2.content).toBeTruthy()
      
      // 驗證對話歷史
      expect(conversation).toHaveLength(3)
      expect(conversation[0].role).toBe('user')
      expect(conversation[1].role).toBe('assistant')
      expect(conversation[2].role).toBe('user')
    })

    it('should maintain conversation context across multiple turns', async () => {
      const conversation: AIMessage[] = []
      
      // 進行多輪對話
      const turns = [
        'Create a Vue component',
        'Add props to it',
        'Make it emit events',
        'Add TypeScript types'
      ]
      
      for (const userInput of turns) {
        conversation.push({ role: 'user', content: userInput })
        
        const response = await aiManager.generateContent(conversation)
        expect(response.content).toBeTruthy()
        
        conversation.push({ role: 'assistant', content: response.content })
      }
      
      // 驗證對話長度
      expect(conversation).toHaveLength(turns.length * 2)
      
      // 驗證對話結構
      for (let i = 0; i < conversation.length; i += 2) {
        expect(conversation[i].role).toBe('user')
        expect(conversation[i + 1].role).toBe('assistant')
      }
    })
  })

  describe('Component Generation Integration', () => {
    it('should generate Vue component and save to file system', async () => {
      const messages: AIMessage[] = [
        {
          role: 'user',
          content: 'Create a Vue 3 button component with TypeScript and Tailwind CSS'
        }
      ]
      
      const response = await aiManager.generateContent(messages)
      expect(response.content).toBeTruthy()
      
      // 模擬從 AI 回應中提取組件程式碼
      const componentCode = `<template>
  <button 
    class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
    @click="handleClick"
  >
    <slot>Click me</slot>
  </button>
</template>

<script setup lang="ts">
interface Props {
  disabled?: boolean
}

interface Emits {
  click: []
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false
})

const emit = defineEmits<Emits>()

const handleClick = () => {
  if (!props.disabled) {
    emit('click')
  }
}
</script>`
      
      // 將組件儲存到虛擬檔案系統
      const file = fileSystem.createFile('/src/components/Button.vue', componentCode)
      
      expect(file.name).toBe('Button.vue')
      expect(file.type).toBe('vue')
      expect(file.content).toContain('<template>')
      expect(file.content).toContain('<script setup lang="ts">')
      expect(file.content).toContain('interface Props')
    })

    it('should generate multiple related components', async () => {
      const componentRequests = [
        'Create a TodoItem component',
        'Create a TodoList component that uses TodoItem',
        'Create a TodoApp component that manages the todo state'
      ]
      
      const generatedFiles: string[] = []
      
      for (let i = 0; i < componentRequests.length; i++) {
        const request = componentRequests[i]
        const messages: AIMessage[] = [{ role: 'user', content: request }]
        const response = await aiManager.generateContent(messages)
        
        expect(response.content).toBeTruthy()
        
        // 模擬檔案名稱提取，使用索引確保唯一性
        const fileName = request.includes('TodoItem') ? `TodoItem${i}.vue` :
                        request.includes('TodoList') ? `TodoList${i}.vue` : `TodoApp${i}.vue`
        
        const mockCode = `<template><div><!-- ${fileName} --></div></template>
<script setup lang="ts">
// Generated by AI: ${request}
</script>`
        
        const file = fileSystem.createFile(`/src/components/${fileName}`, mockCode)
        generatedFiles.push(file.path)
      }
      
      expect(generatedFiles).toHaveLength(3)
      expect(generatedFiles.some(path => path.includes('TodoItem'))).toBe(true)
      expect(generatedFiles.some(path => path.includes('TodoList'))).toBe(true)
      expect(generatedFiles.some(path => path.includes('TodoApp'))).toBe(true)
    })

    it('should handle component modification requests', async () => {
      // 先創建一個基礎組件
      const initialCode = `<template>
  <div class="card">
    <h2>{{ title }}</h2>
    <p>{{ content }}</p>
  </div>
</template>

<script setup lang="ts">
interface Props {
  title: string
  content: string
}

defineProps<Props>()
</script>`
      
      fileSystem.createFile('/src/components/Card.vue', initialCode)
      
      // 請求修改組件
      const modificationRequest = 'Add a button to the card component and make it emit a click event'
      const messages: AIMessage[] = [
        {
          role: 'user',
          content: `Modify this Vue component: ${modificationRequest}

Current code:
\`\`\`vue
${initialCode}
\`\`\``
        }
      ]
      
      const response = await aiManager.generateContent(messages)
      expect(response.content).toBeTruthy()
      
      // 模擬修改後的程式碼
      const modifiedCode = `<template>
  <div class="card">
    <h2>{{ title }}</h2>
    <p>{{ content }}</p>
    <button @click="handleClick" class="btn">Click me</button>
  </div>
</template>

<script setup lang="ts">
interface Props {
  title: string
  content: string
}

interface Emits {
  click: []
}

defineProps<Props>()
const emit = defineEmits<Emits>()

const handleClick = () => {
  emit('click')
}
</script>`
      
      // 更新檔案
      const updatedFile = fileSystem.updateFile('/src/components/Card.vue', modifiedCode)
      
      expect(updatedFile.content).toContain('handleClick')
      expect(updatedFile.content).toContain('emit(\'click\')')
      expect(updatedFile.lastModified).toBeInstanceOf(Date)
    })
  })

  describe('Error Handling and Recovery', () => {
    it('should handle AI provider errors gracefully', async () => {
      // 測試使用不存在的提供者 ID
      const messages: AIMessage[] = [
        { role: 'user', content: 'Test message' }
      ]
      
      try {
        // 指定使用不存在的提供者
        await aiManager.generateContent(messages, undefined, 'non-existent-provider')
        // 如果沒有拋出錯誤，應該回退到 Mock 提供者
        expect(true).toBe(true) // 測試通過
      } catch (error) {
        // 如果拋出錯誤，檢查是否有適當的錯誤訊息
        expect(error).toBeInstanceOf(Error)
      }
      
      // 測試正常的錯誤處理流程
      const response = await aiManager.generateContent(messages)
      expect(response.content).toBeTruthy()
    })

    it('should update provider health status after failures', async () => {
      // 執行健康檢查
      await aiManager.performHealthChecks()
      
      const initialStatuses = aiManager.getProviderStatuses()
      const mockStatus = initialStatuses.find(s => s.id === AI_PROVIDERS.MOCK)
      
      expect(mockStatus?.isHealthy).toBe(true)
      expect(mockStatus?.lastChecked).toBeInstanceOf(Date)
    })

    it('should handle network timeouts and retries', async () => {
      const messages: AIMessage[] = [
        { role: 'user', content: 'This is a test message for timeout handling' }
      ]
      
      // 測試正常情況下的回應時間
      const startTime = Date.now()
      const response = await aiManager.generateContent(messages)
      const responseTime = Date.now() - startTime
      
      expect(response.content).toBeTruthy()
      expect(responseTime).toBeLessThan(5000) // 應該在 5 秒內完成
    })
  })

  describe('Usage Statistics and Monitoring', () => {
    it('should track usage statistics', async () => {
      const messages: AIMessage[] = [
        { role: 'user', content: 'Generate usage statistics test' }
      ]
      
      // 執行多次請求
      for (let i = 0; i < 3; i++) {
        await aiManager.generateContent(messages)
      }
      
      const statuses = aiManager.getProviderStatuses()
      const mockStatus = statuses.find(s => s.id === AI_PROVIDERS.MOCK)
      
      expect(mockStatus?.usage.requestsToday).toBe(3)
      expect(mockStatus?.usage.tokensToday).toBeGreaterThan(0)
      expect(mockStatus?.usage.costToday).toBeGreaterThan(0)
    })

    it('should calculate costs correctly', async () => {
      const messages: AIMessage[] = [
        { role: 'user', content: 'Cost calculation test message' }
      ]
      
      const response = await aiManager.generateContent(messages)
      const statuses = aiManager.getProviderStatuses()
      const mockStatus = statuses.find(s => s.id === AI_PROVIDERS.MOCK)
      
      expect(response.usage?.inputTokens).toBeGreaterThan(0)
      expect(response.usage?.outputTokens).toBeGreaterThan(0)
      expect(mockStatus?.usage.costToday).toBeGreaterThan(0)
      
      // 驗證成本計算
      const expectedCost = 
        (response.usage!.inputTokens * mockProviderConfig.costPerInputToken!) +
        (response.usage!.outputTokens * mockProviderConfig.costPerOutputToken!)
      
      expect(mockStatus?.usage.costToday).toBeCloseTo(expectedCost, 6)
    })
  })

  describe('File System Integration', () => {
    it('should integrate with virtual file system for code analysis', async () => {
      // 創建一些測試檔案
      fileSystem.createFile('/src/components/Header.vue', `<template>
  <header>
    <h1>{{ title }}</h1>
  </header>
</template>

<script setup lang="ts">
interface Props {
  title: string
}

defineProps<Props>()
</script>`)

      fileSystem.createFile('/src/utils/helpers.ts', `export function formatDate(date: Date): string {
  return date.toLocaleDateString()
}

export function capitalize(str: string): string {
  return str.charAt(0).toUpperCase() + str.slice(1)
}`)
      
      // 使用 AI 分析檔案
      const fileContent = fileSystem.readFile('/src/components/Header.vue')
      const messages: AIMessage[] = [
        {
          role: 'user',
          content: `Analyze this Vue component and suggest improvements:

\`\`\`vue
${fileContent}
\`\`\`

Please provide specific suggestions for TypeScript usage and component structure.`
        }
      ]
      
      const response = await aiManager.generateContent(messages)
      
      expect(response.content).toBeTruthy()
      expect(response.usage?.inputTokens).toBeGreaterThan(0)
    })

    it('should handle project-wide analysis', async () => {
      // 創建專案結構
      fileSystem.createDirectory('/src')
      fileSystem.createDirectory('/src/components')
      fileSystem.createDirectory('/src/utils')
      
      // 創建多個檔案
      const files = [
        { path: '/src/App.vue', content: '<template><div>App</div></template>' },
        { path: '/src/components/Button.vue', content: '<template><button>Button</button></template>' },
        { path: '/src/utils/api.ts', content: 'export const API_BASE = "http://localhost:3000"' }
      ]
      
      files.forEach(file => {
        fileSystem.createFile(file.path, file.content)
      })
      
      // 獲取專案統計
      const stats = fileSystem.getStats()
      const fileTree = fileSystem.getFileTree('/')
      
      // 使用 AI 分析整個專案
      const messages: AIMessage[] = [
        {
          role: 'user',
          content: `Analyze this Vue project structure and provide recommendations:

Project Statistics:
- Total Files: ${stats.totalFiles}
- Total Directories: ${stats.totalDirectories}

File Structure:
${JSON.stringify(fileTree, null, 2)}

Please suggest improvements for project organization and best practices.`
        }
      ]
      
      const response = await aiManager.generateContent(messages)
      
      expect(response.content).toBeTruthy()
      expect(stats.totalFiles).toBe(3)
      expect(stats.totalDirectories).toBe(4) // /, /src, /src/components, /src/utils
    })
  })
})