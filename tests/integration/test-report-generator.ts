import { writeFileSync, mkdirSync, existsSync } from 'fs'
import { join } from 'path'

export interface TestResult {
  testName: string
  status: 'passed' | 'failed' | 'skipped'
  duration: number
  error?: string
  details?: any
}

export interface TestSuite {
  suiteName: string
  results: TestResult[]
  totalTests: number
  passedTests: number
  failedTests: number
  skippedTests: number
  totalDuration: number
}

export interface TestReport {
  timestamp: Date
  environment: string
  suites: TestSuite[]
  summary: {
    totalSuites: number
    totalTests: number
    passedTests: number
    failedTests: number
    skippedTests: number
    totalDuration: number
    successRate: number
  }
}

/**
 * 測試報告生成器
 * 用於生成整合測試的詳細報告
 */
export class TestReportGenerator {
  private report: TestReport
  private outputDir: string

  constructor(outputDir: string = 'test-reports') {
    this.outputDir = outputDir
    this.report = {
      timestamp: new Date(),
      environment: process.env.NODE_ENV || 'test',
      suites: [],
      summary: {
        totalSuites: 0,
        totalTests: 0,
        passedTests: 0,
        failedTests: 0,
        skippedTests: 0,
        totalDuration: 0,
        successRate: 0
      }
    }

    // 確保輸出目錄存在
    if (!existsSync(this.outputDir)) {
      mkdirSync(this.outputDir, { recursive: true })
    }
  }

  /**
   * 添加測試套件結果
   */
  addTestSuite(suite: TestSuite): void {
    this.report.suites.push(suite)
    this.updateSummary()
  }

  /**
   * 更新總結統計
   */
  private updateSummary(): void {
    const summary = this.report.summary
    
    summary.totalSuites = this.report.suites.length
    summary.totalTests = this.report.suites.reduce((sum, suite) => sum + suite.totalTests, 0)
    summary.passedTests = this.report.suites.reduce((sum, suite) => sum + suite.passedTests, 0)
    summary.failedTests = this.report.suites.reduce((sum, suite) => sum + suite.failedTests, 0)
    summary.skippedTests = this.report.suites.reduce((sum, suite) => sum + suite.skippedTests, 0)
    summary.totalDuration = this.report.suites.reduce((sum, suite) => sum + suite.totalDuration, 0)
    summary.successRate = summary.totalTests > 0 ? (summary.passedTests / summary.totalTests) * 100 : 0
  }

  /**
   * 生成 JSON 格式報告
   */
  generateJsonReport(): string {
    const filename = `integration-test-report-${this.formatTimestamp()}.json`
    const filepath = join(this.outputDir, filename)
    
    const jsonReport = JSON.stringify(this.report, null, 2)
    writeFileSync(filepath, jsonReport, 'utf8')
    
    console.log(`📊 JSON test report generated: ${filepath}`)
    return filepath
  }

  /**
   * 生成 HTML 格式報告
   */
  generateHtmlReport(): string {
    const filename = `integration-test-report-${this.formatTimestamp()}.html`
    const filepath = join(this.outputDir, filename)
    
    const htmlContent = this.generateHtmlContent()
    writeFileSync(filepath, htmlContent, 'utf8')
    
    console.log(`📊 HTML test report generated: ${filepath}`)
    return filepath
  }

  /**
   * 生成 Markdown 格式報告
   */
  generateMarkdownReport(): string {
    const filename = `integration-test-report-${this.formatTimestamp()}.md`
    const filepath = join(this.outputDir, filename)
    
    const markdownContent = this.generateMarkdownContent()
    writeFileSync(filepath, markdownContent, 'utf8')
    
    console.log(`📊 Markdown test report generated: ${filepath}`)
    return filepath
  }

  /**
   * 生成控制台報告
   */
  generateConsoleReport(): void {
    console.log('\n' + '='.repeat(80))
    console.log('🧪 INTEGRATION TEST REPORT')
    console.log('='.repeat(80))
    console.log(`📅 Timestamp: ${this.report.timestamp.toISOString()}`)
    console.log(`🌍 Environment: ${this.report.environment}`)
    console.log(`⏱️  Total Duration: ${this.formatDuration(this.report.summary.totalDuration)}`)
    console.log()

    // 總結統計
    console.log('📊 SUMMARY')
    console.log('-'.repeat(40))
    console.log(`Total Suites: ${this.report.summary.totalSuites}`)
    console.log(`Total Tests: ${this.report.summary.totalTests}`)
    console.log(`✅ Passed: ${this.report.summary.passedTests}`)
    console.log(`❌ Failed: ${this.report.summary.failedTests}`)
    console.log(`⏭️  Skipped: ${this.report.summary.skippedTests}`)
    console.log(`📈 Success Rate: ${this.report.summary.successRate.toFixed(2)}%`)
    console.log()

    // 各測試套件詳情
    console.log('📋 TEST SUITES')
    console.log('-'.repeat(40))
    
    this.report.suites.forEach((suite, index) => {
      const status = suite.failedTests > 0 ? '❌' : '✅'
      console.log(`${status} ${suite.suiteName}`)
      console.log(`   Tests: ${suite.totalTests} | Passed: ${suite.passedTests} | Failed: ${suite.failedTests} | Skipped: ${suite.skippedTests}`)
      console.log(`   Duration: ${this.formatDuration(suite.totalDuration)}`)
      
      // 顯示失敗的測試
      if (suite.failedTests > 0) {
        const failedTests = suite.results.filter(test => test.status === 'failed')
        failedTests.forEach(test => {
          console.log(`   ❌ ${test.testName}: ${test.error || 'Unknown error'}`)
        })
      }
      
      if (index < this.report.suites.length - 1) {
        console.log()
      }
    })

    console.log('\n' + '='.repeat(80))
  }

  /**
   * 生成所有格式的報告
   */
  generateAllReports(): { json: string; html: string; markdown: string } {
    return {
      json: this.generateJsonReport(),
      html: this.generateHtmlReport(),
      markdown: this.generateMarkdownReport()
    }
  }

  /**
   * 格式化時間戳
   */
  private formatTimestamp(): string {
    return this.report.timestamp.toISOString().replace(/[:.]/g, '-').slice(0, -5)
  }

  /**
   * 格式化持續時間
   */
  private formatDuration(ms: number): string {
    if (ms < 1000) {
      return `${ms}ms`
    } else if (ms < 60000) {
      return `${(ms / 1000).toFixed(2)}s`
    } else {
      const minutes = Math.floor(ms / 60000)
      const seconds = ((ms % 60000) / 1000).toFixed(2)
      return `${minutes}m ${seconds}s`
    }
  }

  /**
   * 生成 HTML 內容
   */
  private generateHtmlContent(): string {
    return `<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Integration Test Report</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }
        .summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .summary-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        .summary-card h3 {
            margin: 0 0 10px 0;
            color: #666;
            font-size: 14px;
            text-transform: uppercase;
        }
        .summary-card .value {
            font-size: 32px;
            font-weight: bold;
            margin: 0;
        }
        .passed { color: #28a745; }
        .failed { color: #dc3545; }
        .skipped { color: #ffc107; }
        .suite {
            background: white;
            margin-bottom: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .suite-header {
            padding: 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }
        .suite-title {
            margin: 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .suite-stats {
            margin: 10px 0 0 0;
            color: #666;
            font-size: 14px;
        }
        .test-list {
            padding: 0;
            margin: 0;
            list-style: none;
        }
        .test-item {
            padding: 15px 20px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .test-item:last-child {
            border-bottom: none;
        }
        .test-name {
            flex: 1;
        }
        .test-duration {
            color: #666;
            font-size: 12px;
            margin-left: 10px;
        }
        .test-error {
            color: #dc3545;
            font-size: 12px;
            margin-top: 5px;
            font-family: monospace;
            background: #f8f9fa;
            padding: 5px;
            border-radius: 3px;
        }
        .status-icon {
            font-size: 16px;
            margin-right: 8px;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997);
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🧪 Integration Test Report</h1>
        <p>Generated on ${this.report.timestamp.toLocaleString()}</p>
        <p>Environment: ${this.report.environment}</p>
    </div>

    <div class="summary">
        <div class="summary-card">
            <h3>Total Tests</h3>
            <p class="value">${this.report.summary.totalTests}</p>
        </div>
        <div class="summary-card">
            <h3>Passed</h3>
            <p class="value passed">${this.report.summary.passedTests}</p>
        </div>
        <div class="summary-card">
            <h3>Failed</h3>
            <p class="value failed">${this.report.summary.failedTests}</p>
        </div>
        <div class="summary-card">
            <h3>Success Rate</h3>
            <p class="value">${this.report.summary.successRate.toFixed(1)}%</p>
        </div>
        <div class="summary-card">
            <h3>Duration</h3>
            <p class="value">${this.formatDuration(this.report.summary.totalDuration)}</p>
        </div>
    </div>

    <div class="progress-bar">
        <div class="progress-fill" style="width: ${this.report.summary.successRate}%"></div>
    </div>

    ${this.report.suites.map(suite => `
    <div class="suite">
        <div class="suite-header">
            <h2 class="suite-title">
                <span class="status-icon">${suite.failedTests > 0 ? '❌' : '✅'}</span>
                ${suite.suiteName}
            </h2>
            <div class="suite-stats">
                ${suite.totalTests} tests • 
                <span class="passed">${suite.passedTests} passed</span> • 
                <span class="failed">${suite.failedTests} failed</span> • 
                <span class="skipped">${suite.skippedTests} skipped</span> • 
                ${this.formatDuration(suite.totalDuration)}
            </div>
        </div>
        <ul class="test-list">
            ${suite.results.map(test => `
            <li class="test-item">
                <div class="test-name">
                    <span class="status-icon">${this.getTestStatusIcon(test.status)}</span>
                    ${test.testName}
                    ${test.error ? `<div class="test-error">${test.error}</div>` : ''}
                </div>
                <div class="test-duration">${this.formatDuration(test.duration)}</div>
            </li>
            `).join('')}
        </ul>
    </div>
    `).join('')}
</body>
</html>`
  }

  /**
   * 生成 Markdown 內容
   */
  private generateMarkdownContent(): string {
    return `# 🧪 Integration Test Report

**Generated:** ${this.report.timestamp.toISOString()}  
**Environment:** ${this.report.environment}  
**Duration:** ${this.formatDuration(this.report.summary.totalDuration)}

## 📊 Summary

| Metric | Value |
|--------|-------|
| Total Suites | ${this.report.summary.totalSuites} |
| Total Tests | ${this.report.summary.totalTests} |
| ✅ Passed | ${this.report.summary.passedTests} |
| ❌ Failed | ${this.report.summary.failedTests} |
| ⏭️ Skipped | ${this.report.summary.skippedTests} |
| 📈 Success Rate | ${this.report.summary.successRate.toFixed(2)}% |

## 📋 Test Suites

${this.report.suites.map(suite => `
### ${suite.failedTests > 0 ? '❌' : '✅'} ${suite.suiteName}

**Stats:** ${suite.totalTests} tests • ${suite.passedTests} passed • ${suite.failedTests} failed • ${suite.skippedTests} skipped • ${this.formatDuration(suite.totalDuration)}

${suite.results.map(test => `
- ${this.getTestStatusIcon(test.status)} **${test.testName}** _(${this.formatDuration(test.duration)})_${test.error ? `\n  \`\`\`\n  ${test.error}\n  \`\`\`` : ''}
`).join('')}
`).join('')}

---

*Report generated by UIGen Vue Integration Test Suite*`
  }

  /**
   * 獲取測試狀態圖標
   */
  private getTestStatusIcon(status: 'passed' | 'failed' | 'skipped'): string {
    switch (status) {
      case 'passed': return '✅'
      case 'failed': return '❌'
      case 'skipped': return '⏭️'
      default: return '❓'
    }
  }

  /**
   * 獲取當前報告
   */
  getReport(): TestReport {
    return { ...this.report }
  }
}

/**
 * 創建測試套件結果的輔助函數
 */
export function createTestSuite(
  suiteName: string,
  results: TestResult[]
): TestSuite {
  const totalTests = results.length
  const passedTests = results.filter(r => r.status === 'passed').length
  const failedTests = results.filter(r => r.status === 'failed').length
  const skippedTests = results.filter(r => r.status === 'skipped').length
  const totalDuration = results.reduce((sum, r) => sum + r.duration, 0)

  return {
    suiteName,
    results,
    totalTests,
    passedTests,
    failedTests,
    skippedTests,
    totalDuration
  }
}

/**
 * 創建測試結果的輔助函數
 */
export function createTestResult(
  testName: string,
  status: 'passed' | 'failed' | 'skipped',
  duration: number,
  error?: string,
  details?: any
): TestResult {
  return {
    testName,
    status,
    duration,
    error,
    details
  }
}