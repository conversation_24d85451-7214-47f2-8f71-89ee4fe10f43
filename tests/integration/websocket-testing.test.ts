import { describe, it, expect, beforeEach, afterEach } from 'vitest'
import { WebSocketTestUtils } from '../utils/websocket-test-utils'
import { MockWebSocket, mockWebSocket, restoreWebSocket } from '../utils/mock-websocket'

describe('WebSocket Testing Integration', () => {
  let testUtils: WebSocketTestUtils

  beforeEach(() => {
    mockWebSocket()
    testUtils = new WebSocketTestUtils()
  })

  afterEach(() => {
    testUtils.reset()
    restoreWebSocket()
  })

  describe('Complete WebSocket Workflow', () => {
    it('should handle full connection lifecycle with message exchange', async () => {
      // Step 1: Create and connect
      const ws = testUtils.createMockWebSocket({
        url: 'ws://localhost:8080/chat',
        protocols: ['chat-v1'],
        connectionDelay: 50
      })

      await testUtils.waitForState(MockWebSocket.OPEN, 1000)
      testUtils.expectState(MockWebSocket.OPEN)

      // Step 2: Send authentication message
      const authMessage = JSON.stringify({
        type: 'auth',
        token: 'test-token-123'
      })

      // Set up server response simulation
      ws.onmessage = (event) => {
        const data = JSON.parse(event.data)
        if (data.type === 'auth') {
          setTimeout(() => {
            ws.simulateMessage(JSON.stringify({
              type: 'auth_response',
              status: 'success',
              userId: 'user-123'
            }))
          }, 10)
        } else if (data.type === 'chat_message') {
          setTimeout(() => {
            ws.simulateMessage(JSON.stringify({
              type: 'message_ack',
              messageId: data.messageId,
              timestamp: Date.now()
            }))
          }, 10)
        }
      }

      const authResponse = await testUtils.sendAndWaitForResponse(
        authMessage,
        /auth_response/,
        1000
      )

      expect(authResponse).not.toBeNull()
      const authData = JSON.parse(authResponse!.data)
      expect(authData.status).toBe('success')
      expect(authData.userId).toBe('user-123')

      // Step 3: Send chat messages
      const chatMessage = JSON.stringify({
        type: 'chat_message',
        messageId: 'msg-001',
        content: 'Hello, World!',
        timestamp: Date.now()
      })

      const ackResponse = await testUtils.sendAndWaitForResponse(
        chatMessage,
        /message_ack/,
        1000
      )

      expect(ackResponse).not.toBeNull()
      const ackData = JSON.parse(ackResponse!.data)
      expect(ackData.messageId).toBe('msg-001')

      // Step 4: Verify message history
      const messageHistory = testUtils.getMessageHistory()
      expect(messageHistory.filter(m => m.type === 'sent')).toHaveLength(2)
      expect(messageHistory.filter(m => m.type === 'received')).toHaveLength(2)

      // Step 5: Clean disconnect
      ws.close(1000, 'Client disconnect')
      await testUtils.waitForState(MockWebSocket.CLOSED, 1000)

      const connectionHistory = testUtils.getConnectionHistory()
      expect(connectionHistory.some(h => h.event === 'open')).toBe(true)
      expect(connectionHistory.some(h => h.event === 'close')).toBe(true)
    })

    it('should handle connection failure and retry scenario', async () => {
      // Step 1: Attempt connection that fails
      let ws = testUtils.createMockWebSocket({
        shouldFailConnection: true
      })

      await testUtils.waitForState(MockWebSocket.CLOSED, 1000)
      testUtils.expectState(MockWebSocket.CLOSED)

      // Step 2: Retry connection successfully
      ws = testUtils.createMockWebSocket({
        shouldFailConnection: false,
        connectionDelay: 100
      })

      await testUtils.waitForState(MockWebSocket.OPEN, 1000)
      testUtils.expectState(MockWebSocket.OPEN)

      // Step 3: Verify connection works
      ws.send('retry test message')
      testUtils.expectMessageSent('retry test message')

      // Step 4: Simulate network issues during operation
      testUtils.simulateNetworkDelay(200)
      
      const startTime = Date.now()
      ws.simulateMessage('delayed response')
      
      await testUtils.waitForMessage('delayed response', 1000)
      const elapsed = Date.now() - startTime
      expect(elapsed).toBeGreaterThanOrEqual(180) // Allow some tolerance
    })
  })

  describe('Real-time Communication Patterns', () => {
    let ws: MockWebSocket

    beforeEach(async () => {
      ws = testUtils.createMockWebSocket()
      await testUtils.waitForState(MockWebSocket.OPEN)
    })

    it('should handle pub/sub pattern', async () => {
      // Set up pub/sub simulation
      const subscriptions = new Set<string>()
      
      ws.onmessage = (event) => {
        const data = JSON.parse(event.data)
        
        if (data.type === 'subscribe') {
          subscriptions.add(data.channel)
          setTimeout(() => {
            ws.simulateMessage(JSON.stringify({
              type: 'subscription_confirmed',
              channel: data.channel
            }))
          }, 10)
        } else if (data.type === 'publish') {
          if (subscriptions.has(data.channel)) {
            setTimeout(() => {
              ws.simulateMessage(JSON.stringify({
                type: 'message',
                channel: data.channel,
                data: data.data,
                timestamp: Date.now()
              }))
            }, 10)
          }
        }
      }

      // Subscribe to channel
      const subscribeMessage = JSON.stringify({
        type: 'subscribe',
        channel: 'chat-room-1'
      })

      const subConfirm = await testUtils.sendAndWaitForResponse(
        subscribeMessage,
        /subscription_confirmed/,
        1000
      )

      expect(subConfirm).not.toBeNull()

      // Publish message
      const publishMessage = JSON.stringify({
        type: 'publish',
        channel: 'chat-room-1',
        data: { message: 'Hello everyone!' }
      })

      const messageReceived = await testUtils.sendAndWaitForResponse(
        publishMessage,
        /\"type\":\"message\"/,
        1000
      )

      expect(messageReceived).not.toBeNull()
      const messageData = JSON.parse(messageReceived!.data)
      expect(messageData.channel).toBe('chat-room-1')
      expect(messageData.data.message).toBe('Hello everyone!')
    })

    it('should handle request/response pattern with correlation IDs', async () => {
      const pendingRequests = new Map<string, any>()

      ws.onmessage = (event) => {
        const data = JSON.parse(event.data)
        
        if (data.type === 'request') {
          // Simulate server processing
          setTimeout(() => {
            ws.simulateMessage(JSON.stringify({
              type: 'response',
              correlationId: data.correlationId,
              result: `Processed: ${data.payload}`,
              timestamp: Date.now()
            }))
          }, Math.random() * 100) // Random processing time
        }
      }

      // Send multiple concurrent requests
      const requests = [
        { id: 'req-1', payload: 'data-1' },
        { id: 'req-2', payload: 'data-2' },
        { id: 'req-3', payload: 'data-3' }
      ]

      const responsePromises = requests.map(req => {
        const requestMessage = JSON.stringify({
          type: 'request',
          correlationId: req.id,
          payload: req.payload
        })

        return testUtils.sendAndWaitForResponse(
          requestMessage,
          new RegExp(`"correlationId":"${req.id}"`),
          1000
        )
      })

      const responses = await Promise.all(responsePromises)

      expect(responses).toHaveLength(3)
      responses.forEach((response, index) => {
        expect(response).not.toBeNull()
        const responseData = JSON.parse(response!.data)
        expect(responseData.correlationId).toBe(requests[index].id)
        expect(responseData.result).toBe(`Processed: ${requests[index].payload}`)
      })
    })
  })

  describe('Error Handling and Recovery', () => {
    it('should handle various error scenarios', async () => {
      const ws = testUtils.createMockWebSocket()
      await testUtils.waitForState(MockWebSocket.OPEN)

      // Test 1: Connection error during operation
      const errorPromise = new Promise<void>((resolve) => {
        ws.onerror = () => resolve()
      })

      testUtils.simulateConnectionError()
      await errorPromise

      // Test 2: Server-initiated close with error code
      const closePromise = new Promise<CloseEvent>((resolve) => {
        ws.onclose = (event) => resolve(event)
      })

      testUtils.simulateServerDisconnect(1011, 'Server error')
      const closeEvent = await closePromise

      expect(closeEvent.code).toBe(1011)
      expect(closeEvent.reason).toBe('Server error')
      expect(closeEvent.wasClean).toBe(false)

      // Test 3: Verify connection is closed
      testUtils.expectState(MockWebSocket.CLOSED)
    })

    it('should handle message sending failures', async () => {
      const ws = testUtils.createMockWebSocket()
      await testUtils.waitForState(MockWebSocket.OPEN)

      // Close connection
      ws.close()
      await testUtils.waitForState(MockWebSocket.CLOSED)

      // Try to send message on closed connection
      expect(() => {
        ws.send('This should fail')
      }).toThrow('WebSocket is not open')
    })

    it('should handle timeout scenarios', async () => {
      const ws = testUtils.createMockWebSocket()
      await testUtils.waitForState(MockWebSocket.OPEN)

      // Test message timeout
      await expect(
        testUtils.waitForMessage('message-that-never-comes', 100)
      ).rejects.toThrow('Timeout waiting for message')

      // Test state timeout
      ws.close()
      await expect(
        testUtils.waitForState(MockWebSocket.OPEN, 100)
      ).rejects.toThrow('Timeout waiting for WebSocket state')
    })
  })

  describe('Performance and Load Testing', () => {
    it('should handle high-frequency message exchange', async () => {
      const ws = testUtils.createMockWebSocket()
      await testUtils.waitForState(MockWebSocket.OPEN)

      // Set up echo server
      ws.onmessage = (event) => {
        setTimeout(() => {
          ws.simulateMessage(`echo: ${event.data}`)
        }, 1)
      }

      const messageCount = 100
      const messages = Array.from({ length: messageCount }, (_, i) => `message-${i}`)
      
      const startTime = Date.now()

      // Send all messages concurrently
      const promises = messages.map(msg =>
        testUtils.sendAndWaitForResponse(msg, new RegExp(`echo: ${msg}`), 5000)
      )

      const responses = await Promise.all(promises)
      const endTime = Date.now()

      expect(responses).toHaveLength(messageCount)
      responses.forEach((response, index) => {
        expect(response?.data).toBe(`echo: ${messages[index]}`)
      })

      const duration = endTime - startTime
      console.log(`Processed ${messageCount} messages in ${duration}ms`)
      expect(duration).toBeLessThan(10000) // Should complete within 10 seconds
    })

    it('should handle connection churn', async () => {
      const connectionCount = 10
      const connections: MockWebSocket[] = []

      // Create multiple connections rapidly
      for (let i = 0; i < connectionCount; i++) {
        const ws = testUtils.createMockWebSocket({
          url: `ws://localhost:8080/test-${i}`
        })
        connections.push(ws)
      }

      // Wait for all to connect
      for (const ws of connections) {
        await testUtils.waitForState(MockWebSocket.OPEN, 1000)
        testUtils.reset() // Reset for next connection
        testUtils = new WebSocketTestUtils()
        ;(testUtils as any).mockWebSocket = ws
      }

      // Send message on each connection
      connections.forEach((ws, index) => {
        ws.send(`message from connection ${index}`)
      })

      // Close all connections
      connections.forEach(ws => ws.close())

      // Verify all are closed
      connections.forEach(ws => {
        expect(ws.readyState).toBe(MockWebSocket.CLOSED)
      })
    })
  })

  describe('Heartbeat and Keep-Alive', () => {
    it('should implement heartbeat mechanism', async () => {
      const ws = testUtils.createMockWebSocket()
      await testUtils.waitForState(MockWebSocket.OPEN)

      // Set up ping/pong handling
      ws.onmessage = (event) => {
        const data = JSON.parse(event.data)
        if (data.type === 'ping') {
          setTimeout(() => {
            ws.simulateMessage(JSON.stringify({
              type: 'pong',
              timestamp: data.timestamp
            }))
          }, 10)
        }
      }

      const heartbeat = testUtils.createHeartbeatTest(100) // 100ms interval
      heartbeat.start()

      // Let it run for a while
      await new Promise(resolve => setTimeout(resolve, 350))

      heartbeat.stop()

      const stats = heartbeat.getStats()
      expect(stats.sent).toBeGreaterThanOrEqual(3)
      expect(stats.missed).toBe(0)
    })

    it('should detect connection loss through missed heartbeats', async () => {
      const ws = testUtils.createMockWebSocket()
      await testUtils.waitForState(MockWebSocket.OPEN)

      const heartbeat = testUtils.createHeartbeatTest(50)
      heartbeat.start()

      // Simulate connection loss after first heartbeat
      setTimeout(() => {
        testUtils.simulateServerDisconnect(1006, 'Connection lost')
      }, 75)

      await testUtils.waitForState(MockWebSocket.CLOSED, 1000)

      // Continue heartbeat attempts on closed connection
      await new Promise(resolve => setTimeout(resolve, 200))

      heartbeat.stop()

      const stats = heartbeat.getStats()
      expect(stats.sent).toBeGreaterThan(0)
      // Missed heartbeats should be tracked when connection is closed
    })
  })

  describe('Protocol Compliance', () => {
    it('should handle WebSocket protocol correctly', async () => {
      const ws = testUtils.createMockWebSocket({
        protocols: ['chat', 'echo']
      })

      await testUtils.waitForState(MockWebSocket.OPEN)

      expect(ws.protocol).toBe('chat') // First protocol should be selected
      expect(ws.url).toContain('ws://')
      expect(ws.readyState).toBe(MockWebSocket.OPEN)
    })

    it('should handle close codes correctly', async () => {
      const ws = testUtils.createMockWebSocket()
      await testUtils.waitForState(MockWebSocket.OPEN)

      const testCases = [
        { code: 1000, reason: 'Normal closure', wasClean: true },
        { code: 1001, reason: 'Going away', wasClean: false },
        { code: 1006, reason: 'Abnormal closure', wasClean: false }
      ]

      for (const testCase of testCases) {
        const closePromise = new Promise<CloseEvent>((resolve) => {
          ws.onclose = (event) => resolve(event)
        })

        if (testCase.code === 1000) {
          ws.close(testCase.code, testCase.reason)
        } else {
          testUtils.simulateServerDisconnect(testCase.code, testCase.reason)
        }

        const closeEvent = await closePromise

        expect(closeEvent.code).toBe(testCase.code)
        expect(closeEvent.reason).toBe(testCase.reason)
        expect(closeEvent.wasClean).toBe(testCase.wasClean)

        // Reset for next test
        testUtils.reset()
        testUtils = new WebSocketTestUtils()
        const newWs = testUtils.createMockWebSocket()
        await testUtils.waitForState(MockWebSocket.OPEN)
        // Update ws reference for next iteration
        ;(ws as any) = newWs
      }
    })
  })
})