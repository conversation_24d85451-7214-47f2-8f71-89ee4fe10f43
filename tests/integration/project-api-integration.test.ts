import { describe, it, expect, beforeAll, afterAll, beforeEach } from 'vitest'
import { TestServerManager } from '../utils/test-server-manager'
import { APITestClient } from '../utils/api-test-client'
import { DatabaseTestManager } from '../utils/database-test-manager'
import { TestDataFactory } from '../utils/test-data-factory'

describe('Project API Integration Tests', () => {
  let serverManager: TestServerManager
  let apiClient: APITestClient
  let databaseManager: DatabaseTestManager
  let testDataFactory: TestDataFactory

  beforeAll(async () => {
    // Initialize test infrastructure
    serverManager = new TestServerManager({
      database: { resetOnStart: true, seedData: false },
      middleware: { cors: true, errorHandling: true }
    })
    
    await serverManager.start()
    
    // Initialize API client
    apiClient = new APITestClient(serverManager.getApp())
    
    // Initialize database utilities
    databaseManager = new DatabaseTestManager()
    await databaseManager.initializeTestDatabase()
    testDataFactory = new TestDataFactory(databaseManager.getPrismaClient())
  })

  afterAll(async () => {
    await serverManager.stop()
    await databaseManager.cleanup()
  })

  beforeEach(async () => {
    await serverManager.reset()
    await databaseManager.resetDatabase()
    testDataFactory.reset()
  })

  describe('Complete Project Lifecycle Integration', () => {
    it('should handle complete project creation workflow', async () => {
      // Step 1: Create project via API
      const projectData = {
        name: 'Integration Test Project',
        description: 'A project created through API integration test'
      }

      const createResponse = await apiClient.post('/api/projects', projectData)
      apiClient.expectStatus(createResponse, 201)
      apiClient.expectResponseStructure(createResponse, {
        id: 'string',
        name: 'string',
        description: 'string',
        createdAt: 'string',
        updatedAt: 'string'
      })

      const createdProject = createResponse.data
      expect(createdProject.name).toBe(projectData.name)
      expect(createdProject.description).toBe(projectData.description)

      // Step 2: Verify project exists in database
      const dbProject = await databaseManager.getPrismaClient().project.findUnique({
        where: { id: createdProject.id }
      })

      expect(dbProject).toBeDefined()
      expect(dbProject.name).toBe(projectData.name)

      // Step 3: Retrieve project via API
      const getResponse = await apiClient.get(`/api/projects/${createdProject.id}`)
      apiClient.expectStatus(getResponse, 200)
      expect(getResponse.data.id).toBe(createdProject.id)
      expect(getResponse.data.name).toBe(projectData.name)

      // Step 4: Update project via API
      const updateData = {
        name: 'Updated Integration Test Project',
        description: 'Updated description'
      }

      const updateResponse = await apiClient.put(`/api/projects/${createdProject.id}`, updateData)
      apiClient.expectStatus(updateResponse, 200)
      expect(updateResponse.data.name).toBe(updateData.name)
      expect(updateResponse.data.description).toBe(updateData.description)

      // Step 5: Verify update in database
      const updatedDbProject = await databaseManager.getPrismaClient().project.findUnique({
        where: { id: createdProject.id }
      })

      expect(updatedDbProject.name).toBe(updateData.name)
      expect(updatedDbProject.description).toBe(updateData.description)

      // Step 6: Delete project via API
      const deleteResponse = await apiClient.delete(`/api/projects/${createdProject.id}`)
      apiClient.expectStatus(deleteResponse, 204)

      // Step 7: Verify deletion in database
      const deletedDbProject = await databaseManager.getPrismaClient().project.findUnique({
        where: { id: createdProject.id }
      })

      expect(deletedDbProject).toBeNull()

      // Step 8: Verify 404 when trying to get deleted project
      const notFoundResponse = await apiClient.get(`/api/projects/${createdProject.id}`)
      apiClient.expectStatus(notFoundResponse, 404)
    })

    it('should handle project with files integration workflow', async () => {
      // Create project
      const projectResponse = await apiClient.post('/api/projects', {
        name: 'Project with Files',
        description: 'Testing project-file relationships'
      })
      apiClient.expectStatus(projectResponse, 201)
      const project = projectResponse.data

      // Create files for the project
      const fileData = [
        {
          name: 'App.vue',
          content: '<template><div>Main App Component</div></template>'
        },
        {
          name: 'Button.vue',
          content: '<template><button><slot /></button></template>'
        },
        {
          name: 'utils.js',
          content: 'export function helper() { return "help"; }'
        }
      ]

      const createdFiles = []
      for (const file of fileData) {
        const fileResponse = await apiClient.post(`/api/projects/${project.id}/files`, file)
        apiClient.expectStatus(fileResponse, 201)
        createdFiles.push(fileResponse.data)
      }

      // Get project with files
      const projectWithFilesResponse = await apiClient.get(`/api/projects/${project.id}?include=files`)
      apiClient.expectStatus(projectWithFilesResponse, 200)
      
      const projectWithFiles = projectWithFilesResponse.data
      expect(projectWithFiles.files).toHaveLength(3)
      expect(projectWithFiles.files.map(f => f.name)).toEqual(
        expect.arrayContaining(['App.vue', 'Button.vue', 'utils.js'])
      )

      // Update a file
      const updatedFileContent = '<template><div>Updated App Component</div></template>'
      const updateFileResponse = await apiClient.put(`/api/files/${createdFiles[0].id}`, {
        content: updatedFileContent
      })
      apiClient.expectStatus(updateFileResponse, 200)
      expect(updateFileResponse.data.content).toBe(updatedFileContent)

      // Delete a file
      const deleteFileResponse = await apiClient.delete(`/api/files/${createdFiles[1].id}`)
      apiClient.expectStatus(deleteFileResponse, 204)

      // Verify file deletion
      const updatedProjectResponse = await apiClient.get(`/api/projects/${project.id}?include=files`)
      expect(updatedProjectResponse.data.files).toHaveLength(2)
      expect(updatedProjectResponse.data.files.map(f => f.name)).not.toContain('Button.vue')

      // Delete project (should cascade delete remaining files)
      const deleteProjectResponse = await apiClient.delete(`/api/projects/${project.id}`)
      apiClient.expectStatus(deleteProjectResponse, 204)

      // Verify all files are deleted from database
      const remainingFiles = await databaseManager.getPrismaClient().file.findMany({
        where: { projectId: project.id }
      })
      expect(remainingFiles).toHaveLength(0)
    })
  })

  describe('API Error Handling Integration', () => {
    it('should handle validation errors properly', async () => {
      // Test missing required fields
      const invalidProjectData = {
        description: 'Missing name field'
      }

      const response = await apiClient.post('/api/projects', invalidProjectData)
      apiClient.expectErrorResponse(response, 400)
      expect(response.data.error || response.data.message).toContain('name')
    })

    it('should handle non-existent resource errors', async () => {
      const nonExistentId = 'non-existent-project-id'

      // Test GET non-existent project
      const getResponse = await apiClient.get(`/api/projects/${nonExistentId}`)
      apiClient.expectStatus(getResponse, 404)

      // Test UPDATE non-existent project
      const updateResponse = await apiClient.put(`/api/projects/${nonExistentId}`, {
        name: 'Updated Name'
      })
      apiClient.expectStatus(updateResponse, 404)

      // Test DELETE non-existent project
      const deleteResponse = await apiClient.delete(`/api/projects/${nonExistentId}`)
      apiClient.expectStatus(deleteResponse, 404)
    })

    it('should handle database constraint violations', async () => {
      // Create a project
      const project = await testDataFactory.createProject({
        name: 'Constraint Test Project'
      })

      // Create a file
      const file = await testDataFactory.createFile(project.id, {
        name: 'unique.js',
        content: 'console.log("unique");'
      })

      // Try to create another file with the same name in the same project
      const duplicateFileResponse = await apiClient.post(`/api/projects/${project.id}/files`, {
        name: 'unique.js',
        content: 'console.log("duplicate");'
      })

      // Should return appropriate error
      expect([400, 409]).toContain(duplicateFileResponse.status)
    })

    it('should handle malformed request data', async () => {
      // Test invalid JSON structure
      const response = await apiClient.post('/api/projects', {
        name: 123, // Should be string
        description: ['invalid', 'array'] // Should be string
      })

      expect([400, 422]).toContain(response.status)
    })
  })

  describe('API Performance and Scalability', () => {
    it('should handle bulk operations efficiently', async () => {
      const startTime = Date.now()

      // Create multiple projects
      const projectPromises = Array.from({ length: 10 }, (_, i) =>
        apiClient.post('/api/projects', {
          name: `Bulk Project ${i + 1}`,
          description: `Description for project ${i + 1}`
        })
      )

      const projectResponses = await Promise.all(projectPromises)
      const endTime = Date.now()

      // All requests should succeed
      projectResponses.forEach(response => {
        apiClient.expectStatus(response, 201)
      })

      // Should complete within reasonable time
      const duration = endTime - startTime
      expect(duration).toBeLessThan(5000) // 5 seconds

      // Verify all projects were created
      const allProjectsResponse = await apiClient.get('/api/projects')
      apiClient.expectStatus(allProjectsResponse, 200)
      expect(allProjectsResponse.data.length).toBeGreaterThanOrEqual(10)
    })

    it('should handle pagination correctly', async () => {
      // Create test projects
      const projects = await Promise.all(
        Array.from({ length: 15 }, (_, i) =>
          testDataFactory.createProject({
            name: `Pagination Project ${i.toString().padStart(2, '0')}`
          })
        )
      )

      // Test first page
      const page1Response = await apiClient.get('/api/projects?page=1&limit=5')
      apiClient.expectStatus(page1Response, 200)
      expect(page1Response.data.length).toBe(5)

      // Test second page
      const page2Response = await apiClient.get('/api/projects?page=2&limit=5')
      apiClient.expectStatus(page2Response, 200)
      expect(page2Response.data.length).toBe(5)

      // Verify different results
      const page1Ids = page1Response.data.map(p => p.id)
      const page2Ids = page2Response.data.map(p => p.id)
      expect(page1Ids).not.toEqual(page2Ids)

      // Test total count if supported
      const allProjectsResponse = await apiClient.get('/api/projects')
      expect(allProjectsResponse.data.length).toBeGreaterThanOrEqual(15)
    })

    it('should handle concurrent requests safely', async () => {
      // Create a project to work with
      const project = await testDataFactory.createProject({
        name: 'Concurrent Test Project'
      })

      // Make concurrent updates to the same project
      const updatePromises = Array.from({ length: 5 }, (_, i) =>
        apiClient.put(`/api/projects/${project.id}`, {
          name: `Concurrent Update ${i + 1}`,
          description: `Updated by request ${i + 1}`
        })
      )

      const updateResponses = await Promise.all(updatePromises)

      // All requests should succeed (last one wins)
      updateResponses.forEach(response => {
        apiClient.expectStatus(response, 200)
      })

      // Verify final state
      const finalProjectResponse = await apiClient.get(`/api/projects/${project.id}`)
      apiClient.expectStatus(finalProjectResponse, 200)
      
      const finalProject = finalProjectResponse.data
      expect(finalProject.name).toMatch(/^Concurrent Update \d+$/)
    })
  })

  describe('API Security and Authentication', () => {
    it('should handle CORS headers correctly', async () => {
      const response = await apiClient.get('/api/projects', {
        headers: {
          'Origin': 'http://localhost:3000'
        }
      })

      expect(response.headers['access-control-allow-origin']).toBeDefined()
    })

    it('should validate request headers', async () => {
      // Test with invalid content type
      const response = await apiClient.post('/api/projects', 
        JSON.stringify({ name: 'Test Project' }),
        {
          headers: {
            'Content-Type': 'text/plain'
          }
        }
      )

      // Should handle gracefully
      expect([400, 415]).toContain(response.status)
    })

    it('should handle request size limits', async () => {
      // Create very large request data
      const largeContent = 'x'.repeat(1024 * 1024) // 1MB content
      
      const response = await apiClient.post('/api/projects', {
        name: 'Large Project',
        description: largeContent
      })

      // Should either succeed or fail with appropriate error
      if (response.status === 413) {
        apiClient.expectErrorResponse(response, 413, 'Payload too large')
      } else {
        apiClient.expectStatus(response, 201)
      }
    })
  })

  describe('API Data Consistency', () => {
    it('should maintain data consistency across operations', async () => {
      // Create project via API
      const projectResponse = await apiClient.post('/api/projects', {
        name: 'Consistency Test Project',
        description: 'Testing data consistency'
      })
      const project = projectResponse.data

      // Create file via API
      const fileResponse = await apiClient.post(`/api/projects/${project.id}/files`, {
        name: 'consistency.js',
        content: 'console.log("consistency test");'
      })
      const file = fileResponse.data

      // Verify relationship consistency
      const projectWithFilesResponse = await apiClient.get(`/api/projects/${project.id}?include=files`)
      const projectWithFiles = projectWithFilesResponse.data
      
      expect(projectWithFiles.files).toHaveLength(1)
      expect(projectWithFiles.files[0].id).toBe(file.id)
      expect(projectWithFiles.files[0].projectId).toBe(project.id)

      // Verify file's project reference
      const fileWithProjectResponse = await apiClient.get(`/api/files/${file.id}?include=project`)
      const fileWithProject = fileWithProjectResponse.data
      
      expect(fileWithProject.project.id).toBe(project.id)
      expect(fileWithProject.project.name).toBe(project.name)

      // Update project and verify file relationship is maintained
      const updatedProjectResponse = await apiClient.put(`/api/projects/${project.id}`, {
        name: 'Updated Consistency Project'
      })
      
      const updatedFileWithProjectResponse = await apiClient.get(`/api/files/${file.id}?include=project`)
      expect(updatedFileWithProjectResponse.data.project.name).toBe('Updated Consistency Project')
    })

    it('should handle cascading operations correctly', async () => {
      // Create project with multiple files
      const project = await testDataFactory.createProject({
        name: 'Cascade Test Project'
      })

      const files = await Promise.all([
        testDataFactory.createFile(project.id, { name: 'file1.js' }),
        testDataFactory.createFile(project.id, { name: 'file2.js' }),
        testDataFactory.createFile(project.id, { name: 'file3.js' })
      ])

      // Delete project via API
      const deleteResponse = await apiClient.delete(`/api/projects/${project.id}`)
      apiClient.expectStatus(deleteResponse, 204)

      // Verify all files are also deleted
      for (const file of files) {
        const fileResponse = await apiClient.get(`/api/files/${file.id}`)
        apiClient.expectStatus(fileResponse, 404)
      }

      // Verify in database
      const remainingFiles = await databaseManager.getPrismaClient().file.findMany({
        where: { projectId: project.id }
      })
      expect(remainingFiles).toHaveLength(0)
    })
  })

  describe('API Response Format Consistency', () => {
    it('should return consistent response formats', async () => {
      // Create project
      const createResponse = await apiClient.post('/api/projects', {
        name: 'Format Test Project',
        description: 'Testing response formats'
      })

      apiClient.expectStatus(createResponse, 201)
      apiClient.expectResponseStructure(createResponse, {
        id: 'string',
        name: 'string',
        description: 'string',
        createdAt: 'string',
        updatedAt: 'string'
      })

      const project = createResponse.data

      // Get project
      const getResponse = await apiClient.get(`/api/projects/${project.id}`)
      apiClient.expectStatus(getResponse, 200)
      apiClient.expectResponseStructure(getResponse, {
        id: 'string',
        name: 'string',
        description: 'string',
        createdAt: 'string',
        updatedAt: 'string'
      })

      // Update project
      const updateResponse = await apiClient.put(`/api/projects/${project.id}`, {
        name: 'Updated Format Test Project'
      })
      apiClient.expectStatus(updateResponse, 200)
      apiClient.expectResponseStructure(updateResponse, {
        id: 'string',
        name: 'string',
        description: 'string',
        createdAt: 'string',
        updatedAt: 'string'
      })

      // List projects
      const listResponse = await apiClient.get('/api/projects')
      apiClient.expectStatus(listResponse, 200)
      expect(Array.isArray(listResponse.data)).toBe(true)
      
      if (listResponse.data.length > 0) {
        apiClient.expectResponseStructure({ data: listResponse.data[0] }, {
          id: 'string',
          name: 'string',
          description: 'string',
          createdAt: 'string',
          updatedAt: 'string'
        })
      }
    })

    it('should return consistent error response formats', async () => {
      // Test 404 error
      const notFoundResponse = await apiClient.get('/api/projects/non-existent-id')
      apiClient.expectStatus(notFoundResponse, 404)
      expect(
        notFoundResponse.data.error || 
        notFoundResponse.data.message || 
        notFoundResponse.data.msg
      ).toBeDefined()

      // Test validation error
      const validationResponse = await apiClient.post('/api/projects', {})
      expect([400, 422]).toContain(validationResponse.status)
      expect(
        validationResponse.data.error || 
        validationResponse.data.message || 
        validationResponse.data.msg
      ).toBeDefined()
    })
  })
})