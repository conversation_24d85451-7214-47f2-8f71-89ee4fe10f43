import { describe, it, expect, beforeAll, afterAll, beforeEach } from 'vitest'
import { DatabaseTestManager } from '../utils/database-test-manager'
import { TestDataFactory } from '../utils/test-data-factory'
import { DatabaseConstraintValidator } from '../utils/database-constraint-validator'

describe('Database Test Management Integration', () => {
  let databaseManager: DatabaseTestManager
  let testDataFactory: TestDataFactory
  let constraintValidator: DatabaseConstraintValidator

  beforeAll(async () => {
    databaseManager = new DatabaseTestManager()
    await databaseManager.initializeTestDatabase()
    
    const prisma = databaseManager.getPrismaClient()
    testDataFactory = new TestDataFactory(prisma)
    constraintValidator = new DatabaseConstraintValidator(prisma)
  })

  afterAll(async () => {
    await databaseManager.cleanup()
  })

  beforeEach(async () => {
    await databaseManager.resetDatabase()
    testDataFactory.reset()
  })

  describe('Complete Workflow Integration', () => {
    it('should handle complete project creation workflow', async () => {
      // Step 1: Create project structure
      const structure = await testDataFactory.createProjectStructure()
      
      expect(structure.project).toBeDefined()
      expect(structure.files.length).toBeGreaterThan(0)

      // Step 2: Verify database state
      const counts = await databaseManager.getTableCounts()
      expect(counts.projects).toBe(1)
      expect(counts.files).toBe(structure.files.length)

      // Step 3: Validate relationships
      const prisma = databaseManager.getPrismaClient()
      const projectWithFiles = await prisma.project.findUnique({
        where: { id: structure.project.id },
        include: { files: true }
      })

      expect(projectWithFiles).toBeDefined()
      expect(projectWithFiles!.files.length).toBe(structure.files.length)

      // Step 4: Cleanup and verify
      await testDataFactory.cleanup()
      const finalCounts = await databaseManager.getTableCounts()
      expect(finalCounts.projects).toBe(0)
      expect(finalCounts.files).toBe(0)
    })

    it('should handle batch data creation with proper constraints', async () => {
      const relations = {
        projects: 3,
        files: 9, // 3 files per project
        withRelations: true
      }

      // Create batch data
      const batch = await testDataFactory.createDataSet(relations)

      // Validate constraint compliance
      const records = [
        ...batch.projects.map(p => ({ table: 'project', data: p })),
        ...batch.files.map(f => ({ table: 'file', data: f }))
      ]

      const validation = await constraintValidator.validateDataIntegrity(records)
      expect(validation.isValid).toBe(true)
      expect(validation.errors).toHaveLength(0)

      // Verify database state
      const counts = await databaseManager.getTableCounts()
      expect(counts.projects).toBe(3)
      expect(counts.files).toBe(9)

      // Verify relationships
      for (const file of batch.files) {
        const parentProject = batch.projects.find(p => p.id === file.projectId)
        expect(parentProject).toBeDefined()
      }
    })

    it('should handle constraint violations gracefully', async () => {
      // Try to create file without parent project
      const invalidFileData = {
        name: 'orphan.js',
        content: 'console.log("I have no parent");',
        projectId: 'non-existent-project-id'
      }

      // Validate constraints before creation
      const validation = await constraintValidator.validateForeignKeys(invalidFileData, 'file')
      expect(validation.isValid).toBe(false)
      expect(validation.errors.length).toBeGreaterThan(0)
      expect(validation.errors[0].type).toBe('FOREIGN_KEY')

      // Attempt creation should fail
      await expect(
        databaseManager.createTestData('file', invalidFileData)
      ).rejects.toThrow()
    })
  })

  describe('Concurrent Operations', () => {
    it('should handle concurrent project creation', async () => {
      const concurrentOperations: Promise<any>[] = []
      
      // Create 10 projects concurrently
      for (let i = 0; i < 10; i++) {
        concurrentOperations.push(
          testDataFactory.createProject({
            overrides: { name: `Concurrent Project ${i}` }
          })
        )
      }

      const results = await Promise.all(concurrentOperations)
      
      expect(results).toHaveLength(10)
      results.forEach((project, index) => {
        expect(project.name).toBe(`Concurrent Project ${index}`)
      })

      // Verify all projects were created
      const counts = await databaseManager.getTableCounts()
      expect(counts.projects).toBe(10)
    })

    it('should handle concurrent file creation for same project', async () => {
      // Create parent project first
      const project = await testDataFactory.createProject()
      
      // Create multiple files concurrently for the same project
      const fileOperations: Promise<any>[] = []
      for (let i = 0; i < 20; i++) {
        fileOperations.push(
          testDataFactory.createFile(project.id, {
            overrides: { name: `concurrent-file-${i}.js` }
          })
        )
      }

      const files = await Promise.all(fileOperations)
      
      expect(files).toHaveLength(20)
      files.forEach((file, index) => {
        expect(file.projectId).toBe(project.id)
        expect(file.name).toBe(`concurrent-file-${index}.js`)
      })

      // Verify database state
      const counts = await databaseManager.getTableCounts()
      expect(counts.projects).toBe(1)
      expect(counts.files).toBe(20)
    })

    it('should maintain data integrity under concurrent access', async () => {
      const operations: Promise<{ project: any; files: any[] }>[] = []
      
      // Mix of project and file creation operations
      for (let i = 0; i < 5; i++) {
        operations.push(
          testDataFactory.createProject({
            overrides: { name: `Project ${i}` }
          }).then(async (project) => {
            // Create files for each project
            const filePromises: Promise<any>[] = []
            for (let j = 0; j < 3; j++) {
              filePromises.push(
                testDataFactory.createFile(project.id, {
                  overrides: { name: `file-${j}.js` }
                })
              )
            }
            const files = await Promise.all(filePromises)
            return { project, files }
          })
        )
      }

      const results = await Promise.all(operations)
      
      expect(results).toHaveLength(5)
      
      // Verify each project has its files
      results.forEach((result, index) => {
        expect(result.project.name).toBe(`Project ${index}`)
        expect(result.files).toHaveLength(3)
        result.files.forEach((file: any) => {
          expect(file.projectId).toBe(result.project.id)
        })
      })

      // Verify final database state
      const counts = await databaseManager.getTableCounts()
      expect(counts.projects).toBe(5)
      expect(counts.files).toBe(15)
    })
  })

  describe('Transaction Management', () => {
    it('should handle complex transactions with multiple operations', async () => {
      const result = await databaseManager.withTransaction(async (prisma) => {
        // Create multiple projects and files in a single transaction
        const projects = []
        const files = []

        for (let i = 0; i < 3; i++) {
          const project = await prisma.project.create({
            data: {
              name: `Transaction Project ${i}`,
              description: `Project created in transaction ${i}`
            }
          })
          projects.push(project)

          // Create files for each project
          for (let j = 0; j < 2; j++) {
            const file = await prisma.file.create({
              data: {
                name: `transaction-file-${i}-${j}.js`,
                content: `// File ${j} for project ${i}`,
                projectId: project.id
              }
            })
            files.push(file)
          }
        }

        return { projects, files }
      })

      expect(result.projects).toHaveLength(3)
      expect(result.files).toHaveLength(6)

      // Verify all data was committed
      const counts = await databaseManager.getTableCounts()
      expect(counts.projects).toBe(3)
      expect(counts.files).toBe(6)
    })

    it('should rollback transaction on constraint violation', async () => {
      const initialCounts = await databaseManager.getTableCounts()

      await expect(
        databaseManager.withTransaction(async (prisma) => {
          // Create a valid project
          const project = await prisma.project.create({
            data: {
              name: 'Valid Project',
              description: 'This should be rolled back'
            }
          })

          // Try to create file with invalid project ID (should fail)
          await prisma.file.create({
            data: {
              name: 'invalid.js',
              content: 'content',
              projectId: 'non-existent-id'
            }
          })

          return project
        })
      ).rejects.toThrow()

      // Verify no data was committed due to rollback
      const finalCounts = await databaseManager.getTableCounts()
      expect(finalCounts.projects).toBe(initialCounts.projects)
      expect(finalCounts.files).toBe(initialCounts.files)
    })
  })

  describe('Data Validation and Constraints', () => {
    it('should validate unique constraints', async () => {
      const project = await testDataFactory.createProject()
      
      // Create first file
      await testDataFactory.createFile(project.id, {
        overrides: { name: 'duplicate.js' }
      })

      // Try to create second file with same name in same project
      const duplicateFileData = {
        name: 'duplicate.js',
        content: 'different content',
        projectId: project.id
      }

      const validation = await constraintValidator.validateForeignKeys(duplicateFileData, 'file')
      expect(validation.isValid).toBe(false)
      expect(validation.errors.some(e => e.type === 'UNIQUE')).toBe(true)
    })

    it('should validate creation order for dependent records', async () => {
      const records = [
        {
          table: 'file',
          data: { name: 'test.js', content: 'content', projectId: 'project-1' }
        },
        {
          table: 'project',
          data: { id: 'project-1', name: 'Test Project' }
        }
      ]

      const validation = constraintValidator.validateCreationOrder(records)
      expect(validation.warnings.length).toBeGreaterThan(0)
      expect(validation.warnings[0]).toContain('should be created before dependent tables')
    })

    it('should provide helpful error suggestions', async () => {
      const mockError = {
        message: 'FOREIGN KEY constraint failed'
      }

      const suggestions = constraintValidator.getConstraintSuggestions(mockError)
      expect(suggestions.length).toBeGreaterThan(0)
      expect(suggestions.some(s => s.includes('referenced record exists'))).toBe(true)
    })
  })

  describe('Performance and Scalability', () => {
    it('should handle large batch operations efficiently', async () => {
      const startTime = Date.now()
      
      // Create large dataset
      const batch = await testDataFactory.createDataSet({
        projects: 50,
        files: 200 // 4 files per project on average
      })

      const endTime = Date.now()
      const duration = endTime - startTime

      expect(batch.projects).toHaveLength(50)
      expect(batch.files).toHaveLength(200)
      expect(duration).toBeLessThan(10000) // Should complete within 10 seconds

      // Verify database state
      const counts = await databaseManager.getTableCounts()
      expect(counts.projects).toBe(50)
      expect(counts.files).toBe(200)
    })

    it('should cleanup large datasets efficiently', async () => {
      // Create large dataset
      await testDataFactory.createDataSet({
        projects: 30,
        files: 120
      })

      // Verify data exists
      let counts = await databaseManager.getTableCounts()
      expect(counts.projects).toBe(30)
      expect(counts.files).toBe(120)

      const startTime = Date.now()
      
      // Cleanup
      await testDataFactory.cleanup()
      
      const endTime = Date.now()
      const duration = endTime - startTime

      expect(duration).toBeLessThan(5000) // Should cleanup within 5 seconds

      // Verify cleanup
      counts = await databaseManager.getTableCounts()
      expect(counts.projects).toBe(0)
      expect(counts.files).toBe(0)
    })
  })

  describe('Error Recovery', () => {
    it('should recover from database connection issues', async () => {
      // Simulate connection issue by disconnecting
      await databaseManager.getPrismaClient().$disconnect()

      // Should be able to reconnect and continue operations
      const isConnected = await databaseManager.checkConnection()
      
      if (!isConnected) {
        await databaseManager.initializeTestDatabase()
      }

      // Should be able to create data after recovery
      const project = await testDataFactory.createProject()
      expect(project).toBeDefined()
    })

    it('should handle partial failures in batch operations', async () => {
      const project = await testDataFactory.createProject()
      
      // Mix of valid and invalid file data
      const filePromises = [
        testDataFactory.createFile(project.id, { overrides: { name: 'valid1.js' } }),
        testDataFactory.createFile(project.id, { overrides: { name: 'valid2.js' } }),
        // This should fail due to invalid project ID
        testDataFactory.createFile('invalid-project-id', { overrides: { name: 'invalid.js' } })
          .catch(error => ({ error: error.message }))
      ]

      const results = await Promise.all(filePromises)
      
      // Should have 2 successful creations and 1 error
      const successful = results.filter(r => !r.error)
      const failed = results.filter(r => r.error)
      
      expect(successful).toHaveLength(2)
      expect(failed).toHaveLength(1)

      // Verify only valid files were created
      const counts = await databaseManager.getTableCounts()
      expect(counts.files).toBe(2)
    })
  })
})