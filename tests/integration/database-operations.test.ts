import { describe, it, expect, beforeEach, afterEach } from 'vitest'
import { testPrisma, cleanupTestData, createTestProject, createTestFile } from '../server/setup'
import type { Project, File } from '@prisma/client'

describe('Database Operations Integration Tests', () => {
  beforeEach(async () => {
    await cleanupTestData()
  })

  afterEach(async () => {
    await cleanupTestData()
  })

  describe('Project CRUD Operations', () => {
    it('should create project successfully', async () => {
      const projectData = {
        name: '測試專案',
        description: '這是一個測試專案'
      }

      const project = await testPrisma.project.create({
        data: projectData
      })

      expect(project.id).toBeTruthy()
      expect(project.name).toBe(projectData.name)
      expect(project.description).toBe(projectData.description)
      expect(project.createdAt).toBeInstanceOf(Date)
      expect(project.updatedAt).toBeInstanceOf(Date)
    })

    it('should read project with files', async () => {
      // 創建專案
      const project = await createTestProject({
        name: '有檔案的專案',
        description: '包含檔案的專案'
      })

      // 為專案創建檔案
      await createTestFile({
        name: 'Button.vue',
        content: '<template><button>Test</button></template>',
        projectId: project.id
      })

      await createTestFile({
        name: 'Input.vue',
        content: '<template><input type="text" /></template>',
        projectId: project.id
      })

      // 讀取專案及其檔案
      const projectWithFiles = await testPrisma.project.findUnique({
        where: { id: project.id },
        include: { files: true }
      })

      expect(projectWithFiles).toBeTruthy()
      expect(projectWithFiles!.files).toHaveLength(2)
      expect(projectWithFiles!.files[0].name).toBe('Button.vue')
      expect(projectWithFiles!.files[1].name).toBe('Input.vue')
    })

    it('should update project successfully', async () => {
      const project = await createTestProject({
        name: '原始專案名稱',
        description: '原始描述'
      })

      const updatedProject = await testPrisma.project.update({
        where: { id: project.id },
        data: {
          name: '更新後的專案名稱',
          description: '更新後的描述'
        }
      })

      expect(updatedProject.name).toBe('更新後的專案名稱')
      expect(updatedProject.description).toBe('更新後的描述')
      expect(updatedProject.updatedAt.getTime()).toBeGreaterThan(project.updatedAt.getTime())
    })

    it('should delete project and cascade delete files', async () => {
      const project = await createTestProject({
        name: '要刪除的專案'
      })

      // 為專案創建檔案
      const file = await createTestFile({
        name: 'ToDelete.vue',
        content: '<template><div>Will be deleted</div></template>',
        projectId: project.id
      })

      // 刪除專案
      await testPrisma.project.delete({
        where: { id: project.id }
      })

      // 確認專案已被刪除
      const deletedProject = await testPrisma.project.findUnique({
        where: { id: project.id }
      })
      expect(deletedProject).toBeNull()

      // 確認關聯的檔案也被刪除（CASCADE）
      const deletedFile = await testPrisma.file.findUnique({
        where: { id: file.id }
      })
      expect(deletedFile).toBeNull()
    })

    it('should handle project name uniqueness constraints', async () => {
      const projectName = '重複名稱專案'

      // 創建第一個專案
      await createTestProject({ name: projectName })

      // 嘗試創建同名專案（應該成功，因為沒有唯一性約束）
      const secondProject = await createTestProject({ name: projectName })
      expect(secondProject.name).toBe(projectName)
    })
  })

  describe('File CRUD Operations', () => {
    let testProject: Project

    beforeEach(async () => {
      testProject = await createTestProject({
        name: '檔案測試專案',
        description: '用於測試檔案操作的專案'
      })
    })

    it('should create file successfully', async () => {
      const fileData = {
        name: 'Component.vue',
        content: '<template><div>Test Component</div></template>',
        projectId: testProject.id
      }

      const file = await testPrisma.file.create({
        data: fileData
      })

      expect(file.id).toBeTruthy()
      expect(file.name).toBe(fileData.name)
      expect(file.content).toBe(fileData.content)
      expect(file.projectId).toBe(testProject.id)
      expect(file.createdAt).toBeInstanceOf(Date)
      expect(file.updatedAt).toBeInstanceOf(Date)
    })

    it('should read file with project information', async () => {
      const file = await createTestFile({
        name: 'ReadTest.vue',
        content: '<template><div>Read Test</div></template>',
        projectId: testProject.id
      })

      const fileWithProject = await testPrisma.file.findUnique({
        where: { id: file.id },
        include: { project: true }
      })

      expect(fileWithProject).toBeTruthy()
      expect(fileWithProject!.project.id).toBe(testProject.id)
      expect(fileWithProject!.project.name).toBe(testProject.name)
    })

    it('should update file content', async () => {
      const file = await createTestFile({
        name: 'UpdateTest.vue',
        content: '<template><div>Original Content</div></template>',
        projectId: testProject.id
      })

      const updatedFile = await testPrisma.file.update({
        where: { id: file.id },
        data: {
          content: '<template><div>Updated Content</div></template>'
        }
      })

      expect(updatedFile.content).toBe('<template><div>Updated Content</div></template>')
      expect(updatedFile.updatedAt.getTime()).toBeGreaterThanOrEqual(file.updatedAt.getTime())
    })

    it('should delete file successfully', async () => {
      const file = await createTestFile({
        name: 'DeleteTest.vue',
        content: '<template><div>To be deleted</div></template>',
        projectId: testProject.id
      })

      await testPrisma.file.delete({
        where: { id: file.id }
      })

      const deletedFile = await testPrisma.file.findUnique({
        where: { id: file.id }
      })

      expect(deletedFile).toBeNull()
    })

    it('should enforce unique file names per project', async () => {
      const fileName = 'UniqueTest.vue'

      // 創建第一個檔案
      await createTestFile({
        name: fileName,
        content: '<template><div>First</div></template>',
        projectId: testProject.id
      })

      // 嘗試在同一專案中創建同名檔案（應該失敗）
      await expect(
        testPrisma.file.create({
          data: {
            name: fileName,
            content: '<template><div>Second</div></template>',
            projectId: testProject.id
          }
        })
      ).rejects.toThrow()
    })

    it('should allow same file names in different projects', async () => {
      const fileName = 'SameName.vue'

      // 創建第二個專案
      const secondProject = await createTestProject({
        name: '第二個專案'
      })

      // 在第一個專案中創建檔案
      const file1 = await createTestFile({
        name: fileName,
        content: '<template><div>Project 1</div></template>',
        projectId: testProject.id
      })

      // 在第二個專案中創建同名檔案（應該成功）
      const file2 = await createTestFile({
        name: fileName,
        content: '<template><div>Project 2</div></template>',
        projectId: secondProject.id
      })

      expect(file1.name).toBe(fileName)
      expect(file2.name).toBe(fileName)
      expect(file1.projectId).toBe(testProject.id)
      expect(file2.projectId).toBe(secondProject.id)
    })
  })

  describe('Complex Queries and Relationships', () => {
    let projects: Project[]
    let files: File[]

    beforeEach(async () => {
      // 創建多個專案和檔案用於複雜查詢測試
      projects = await Promise.all([
        createTestProject({ name: 'Vue 專案', description: 'Vue.js 相關專案' }),
        createTestProject({ name: 'React 專案', description: 'React.js 相關專案' }),
        createTestProject({ name: '工具專案', description: '工具和輔助函數' })
      ])

      // 為每個專案創建檔案
      files = await Promise.all([
        // Vue 專案檔案
        createTestFile({ name: 'App.vue', content: '<template><div>Vue App</div></template>', projectId: projects[0].id }),
        createTestFile({ name: 'Button.vue', content: '<template><button>Vue Button</button></template>', projectId: projects[0].id }),
        createTestFile({ name: 'Input.vue', content: '<template><input /></template>', projectId: projects[0].id }),
        
        // React 專案檔案
        createTestFile({ name: 'App.jsx', content: 'function App() { return <div>React App</div>; }', projectId: projects[1].id }),
        createTestFile({ name: 'Button.jsx', content: 'function Button() { return <button>React Button</button>; }', projectId: projects[1].id }),
        
        // 工具專案檔案
        createTestFile({ name: 'utils.ts', content: 'export function helper() { return "help"; }', projectId: projects[2].id }),
        createTestFile({ name: 'constants.ts', content: 'export const API_URL = "http://localhost:3000";', projectId: projects[2].id })
      ])
    })

    it('should find projects with file count', async () => {
      const projectsWithFileCount = await testPrisma.project.findMany({
        include: {
          _count: {
            select: { files: true }
          }
        },
        orderBy: { name: 'asc' }
      })

      expect(projectsWithFileCount).toHaveLength(3)
      expect(projectsWithFileCount[0].name).toBe('React 專案')
      expect(projectsWithFileCount[0]._count.files).toBe(2)
      expect(projectsWithFileCount[1].name).toBe('Vue 專案')
      expect(projectsWithFileCount[1]._count.files).toBe(3)
      expect(projectsWithFileCount[2].name).toBe('工具專案')
      expect(projectsWithFileCount[2]._count.files).toBe(2)
    })

    it('should find files by extension pattern', async () => {
      const vueFiles = await testPrisma.file.findMany({
        where: {
          name: {
            endsWith: '.vue'
          }
        },
        include: { project: true }
      })

      expect(vueFiles).toHaveLength(3)
      vueFiles.forEach(file => {
        expect(file.name).toMatch(/\.vue$/)
        expect(file.project.name).toBe('Vue 專案')
      })
    })

    it('should find projects by description pattern', async () => {
      const jsProjects = await testPrisma.project.findMany({
        where: {
          description: {
            contains: '.js'
          }
        },
        include: { files: true }
      })

      expect(jsProjects).toHaveLength(2)
      expect(jsProjects.map(p => p.name)).toContain('Vue 專案')
      expect(jsProjects.map(p => p.name)).toContain('React 專案')
    })

    it('should perform aggregation queries', async () => {
      const stats = await testPrisma.file.aggregate({
        _count: {
          id: true
        },
        where: {
          name: {
            contains: 'Button'
          }
        }
      })

      expect(stats._count.id).toBe(2) // Vue Button + React Button
    })

    it('should find projects with specific file types', async () => {
      const projectsWithTypeScript = await testPrisma.project.findMany({
        where: {
          files: {
            some: {
              name: {
                endsWith: '.ts'
              }
            }
          }
        },
        include: { files: true }
      })

      expect(projectsWithTypeScript).toHaveLength(1)
      expect(projectsWithTypeScript[0].name).toBe('工具專案')
    })

    it('should perform complex filtering and sorting', async () => {
      const result = await testPrisma.project.findMany({
        where: {
          OR: [
            { name: { contains: 'Vue' } },
            { description: { contains: '工具' } }
          ]
        },
        include: {
          files: {
            orderBy: { name: 'asc' },
            take: 2
          }
        },
        orderBy: { createdAt: 'desc' }
      })

      expect(result).toHaveLength(2)
      result.forEach(project => {
        expect(project.files.length).toBeLessThanOrEqual(2)
      })
    })
  })

  describe('Transaction Operations', () => {
    it('should handle successful transaction', async () => {
      const result = await testPrisma.$transaction(async (tx) => {
        // 創建專案
        const project = await tx.project.create({
          data: {
            name: '交易測試專案',
            description: '測試交易功能'
          }
        })

        // 創建多個檔案
        const files = await Promise.all([
          tx.file.create({
            data: {
              name: 'File1.vue',
              content: '<template><div>File 1</div></template>',
              projectId: project.id
            }
          }),
          tx.file.create({
            data: {
              name: 'File2.vue',
              content: '<template><div>File 2</div></template>',
              projectId: project.id
            }
          })
        ])

        return { project, files }
      })

      expect(result.project.name).toBe('交易測試專案')
      expect(result.files).toHaveLength(2)

      // 驗證資料確實被保存
      const savedProject = await testPrisma.project.findUnique({
        where: { id: result.project.id },
        include: { files: true }
      })

      expect(savedProject).toBeTruthy()
      expect(savedProject!.files).toHaveLength(2)
    })

    it('should rollback failed transaction', async () => {
      const initialProjectCount = await testPrisma.project.count()

      try {
        await testPrisma.$transaction(async (tx) => {
          // 創建專案
          const project = await tx.project.create({
            data: {
              name: '失敗交易專案',
              description: '這個交易會失敗'
            }
          })

          // 創建檔案
          await tx.file.create({
            data: {
              name: 'ValidFile.vue',
              content: '<template><div>Valid</div></template>',
              projectId: project.id
            }
          })

          // 故意創建重複檔案名稱導致錯誤
          await tx.file.create({
            data: {
              name: 'ValidFile.vue', // 重複名稱
              content: '<template><div>Duplicate</div></template>',
              projectId: project.id
            }
          })
        })
      } catch (error) {
        // 預期會有錯誤
        expect(error).toBeTruthy()
      }

      // 驗證交易被回滾，專案數量沒有增加
      const finalProjectCount = await testPrisma.project.count()
      expect(finalProjectCount).toBe(initialProjectCount)
    })
  })

  describe('Performance and Optimization', () => {
    beforeEach(async () => {
      // 創建大量測試資料
      const projects = await Promise.all(
        Array.from({ length: 10 }, (_, i) =>
          createTestProject({
            name: `效能測試專案 ${i + 1}`,
            description: `第 ${i + 1} 個效能測試專案`
          })
        )
      )

      // 為每個專案創建多個檔案
      await Promise.all(
        projects.flatMap(project =>
          Array.from({ length: 5 }, (_, i) =>
            createTestFile({
              name: `File${i + 1}.vue`,
              content: `<template><div>File ${i + 1} in ${project.name}</div></template>`,
              projectId: project.id
            })
          )
        )
      )
    })

    it('should perform efficient pagination', async () => {
      const pageSize = 3
      const page1 = await testPrisma.project.findMany({
        take: pageSize,
        skip: 0,
        orderBy: { name: 'asc' },
        include: { files: true }
      })

      const page2 = await testPrisma.project.findMany({
        take: pageSize,
        skip: pageSize,
        orderBy: { name: 'asc' },
        include: { files: true }
      })

      expect(page1).toHaveLength(pageSize)
      expect(page2).toHaveLength(pageSize)
      expect(page1[0].name).not.toBe(page2[0].name)
    })

    it('should handle bulk operations efficiently', async () => {
      const startTime = Date.now()

      // 批量更新所有檔案
      const updateResult = await testPrisma.file.updateMany({
        where: {
          name: {
            endsWith: '.vue'
          }
        },
        data: {
          content: '<template><div>Bulk Updated</div></template>'
        }
      })

      const endTime = Date.now()
      const duration = endTime - startTime

      expect(updateResult.count).toBeGreaterThan(0)
      expect(duration).toBeLessThan(5000) // 應該在 5 秒內完成

      // 驗證更新結果
      const updatedFiles = await testPrisma.file.findMany({
        where: {
          content: '<template><div>Bulk Updated</div></template>'
        }
      })

      expect(updatedFiles.length).toBe(updateResult.count)
    })

    it('should optimize queries with selective field loading', async () => {
      const startTime = Date.now()

      // 只選擇需要的欄位
      const projects = await testPrisma.project.findMany({
        select: {
          id: true,
          name: true,
          _count: {
            select: { files: true }
          }
        }
      })

      const endTime = Date.now()
      const duration = endTime - startTime

      expect(projects).toHaveLength(10)
      expect(duration).toBeLessThan(1000) // 應該很快完成

      projects.forEach(project => {
        expect(project.id).toBeTruthy()
        expect(project.name).toBeTruthy()
        expect(project._count.files).toBe(5)
        expect((project as any).description).toBeUndefined() // 沒有選擇這個欄位
      })
    })
  })

  describe('Data Integrity and Constraints', () => {
    it('should enforce foreign key constraints', async () => {
      const nonExistentProjectId = 'non-existent-project-id'

      await expect(
        testPrisma.file.create({
          data: {
            name: 'OrphanFile.vue',
            content: '<template><div>Orphan</div></template>',
            projectId: nonExistentProjectId
          }
        })
      ).rejects.toThrow()
    })

    it('should handle concurrent modifications', async () => {
      const project = await createTestProject({
        name: '併發測試專案'
      })

      // 同時嘗試更新同一個專案
      const updates = await Promise.allSettled([
        testPrisma.project.update({
          where: { id: project.id },
          data: { name: '更新 1' }
        }),
        testPrisma.project.update({
          where: { id: project.id },
          data: { name: '更新 2' }
        }),
        testPrisma.project.update({
          where: { id: project.id },
          data: { name: '更新 3' }
        })
      ])

      // 所有更新都應該成功（最後一個會覆蓋前面的）
      const successfulUpdates = updates.filter(result => result.status === 'fulfilled')
      expect(successfulUpdates.length).toBeGreaterThan(0)

      // 檢查最終狀態
      const finalProject = await testPrisma.project.findUnique({
        where: { id: project.id }
      })

      expect(finalProject).toBeTruthy()
      expect(['更新 1', '更新 2', '更新 3']).toContain(finalProject!.name)
    })

    it('should validate data types and constraints', async () => {
      // 測試空字串（應該被允許）
      const projectWithEmptyDescription = await testPrisma.project.create({
        data: {
          name: '空描述專案',
          description: ''
        }
      })

      expect(projectWithEmptyDescription.description).toBe('')

      // 測試 null 值
      const projectWithNullDescription = await testPrisma.project.create({
        data: {
          name: '無描述專案',
          description: null
        }
      })

      expect(projectWithNullDescription.description).toBeNull()
    })
  })
})