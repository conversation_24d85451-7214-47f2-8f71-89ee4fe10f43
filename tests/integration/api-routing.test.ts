import { describe, it, expect, beforeAll, afterAll, beforeEach } from 'vitest'
import { apiTestUtils, withAPITest, withAuthenticatedUser } from '../utils/api-test-utils'

describe('API Routing Integration Tests', () => {
  beforeAll(async () => {
    await apiTestUtils.setupAPITest()
  })

  afterAll(async () => {
    await apiTestUtils.cleanupAPITest()
  })

  beforeEach(async () => {
    await apiTestUtils.resetTestEnvironment()
  })

  describe('Health Check Endpoint', () => {
    it('should respond to health check', async () => {
      await withAPITest(async ({ client }) => {
        const response = await client.get('/health')

        expect(response.status).toBe(200)
        expect(response.data.status).toBe('ok')
        expect(response.data.environment).toBe('test')
        expect(response.data.timestamp).toBeDefined()
      })
    })
  })

  describe('Project API Routes', () => {
    it('should get all projects', async () => {
      await withAuthenticatedUser(async ({ client }, user) => {
        // 建立測試專案
        await apiTestUtils.createTestProject(user.id, {
          name: 'Test Project 1'
        })

        const response = await client.get('/api/projects')

        expect(response.status).toBe(200)
        expect(Array.isArray(response.data)).toBe(true)
        expect(response.data.length).toBeGreaterThan(0)
        expect(response.data[0]).toHaveProperty('id')
        expect(response.data[0]).toHaveProperty('name')
      })
    })

    it('should create a new project', async () => {
      await withAuthenticatedUser(async ({ client }, user) => {
        const projectData = {
          name: 'New Test Project',
          description: 'A new test project'
        }

        const response = await client.post('/api/projects', projectData)

        expect(response.status).toBe(201)
        expect(response.data).toHaveProperty('id')
        expect(response.data.name).toBe(projectData.name)
        expect(response.data.description).toBe(projectData.description)
        expect(response.data.userId).toBe(user.id)
      })
    })

    it('should get project by ID', async () => {
      await withAuthenticatedUser(async ({ client }, user) => {
        const project = await apiTestUtils.createTestProject(user.id)

        const response = await client.get(`/api/projects/${project.id}`)

        expect(response.status).toBe(200)
        expect(response.data.id).toBe(project.id)
        expect(response.data.name).toBe(project.name)
      })
    })

    it('should update project', async () => {
      await withAuthenticatedUser(async ({ client }, user) => {
        const project = await apiTestUtils.createTestProject(user.id)
        const updateData = {
          name: 'Updated Project Name',
          description: 'Updated description'
        }

        const response = await client.put(`/api/projects/${project.id}`, updateData)

        expect(response.status).toBe(200)
        expect(response.data.name).toBe(updateData.name)
        expect(response.data.description).toBe(updateData.description)
      })
    })

    it('should delete project', async () => {
      await withAuthenticatedUser(async ({ client }, user) => {
        const project = await apiTestUtils.createTestProject(user.id)

        const response = await client.delete(`/api/projects/${project.id}`)

        expect(response.status).toBe(204)

        // 驗證專案已被刪除
        const getResponse = await client.get(`/api/projects/${project.id}`)
        expect(getResponse.status).toBe(404)
      })
    })

    it('should return 404 for non-existent project', async () => {
      await withAuthenticatedUser(async ({ client }) => {
        const response = await client.get('/api/projects/non-existent-id')

        expect(response.status).toBe(404)
        expect(response.data).toHaveProperty('error')
      })
    })
  })

  describe('File API Routes', () => {
    it('should get files for a project', async () => {
      await withAuthenticatedUser(async ({ client }, user) => {
        const project = await apiTestUtils.createTestProject(user.id)
        await apiTestUtils.createTestFile(project.id, {
          name: 'test.txt',
          content: 'Test content'
        })

        const response = await client.get(`/api/projects/${project.id}/files`)

        expect(response.status).toBe(200)
        expect(Array.isArray(response.data)).toBe(true)
        expect(response.data.length).toBeGreaterThan(0)
        expect(response.data[0]).toHaveProperty('id')
        expect(response.data[0]).toHaveProperty('name')
        expect(response.data[0]).toHaveProperty('content')
      })
    })

    it('should create a new file', async () => {
      await withAuthenticatedUser(async ({ client }, user) => {
        const project = await apiTestUtils.createTestProject(user.id)
        const fileData = {
          name: 'new-file.js',
          content: 'console.log("Hello, World!");'
        }

        const response = await client.post(`/api/projects/${project.id}/files`, fileData)

        expect(response.status).toBe(201)
        expect(response.data).toHaveProperty('id')
        expect(response.data.name).toBe(fileData.name)
        expect(response.data.content).toBe(fileData.content)
        expect(response.data.projectId).toBe(project.id)
      })
    })

    it('should get file by ID', async () => {
      await withAuthenticatedUser(async ({ client }, user) => {
        const project = await apiTestUtils.createTestProject(user.id)
        const file = await apiTestUtils.createTestFile(project.id)

        const response = await client.get(`/api/files/${file.id}`)

        expect(response.status).toBe(200)
        expect(response.data.id).toBe(file.id)
        expect(response.data.name).toBe(file.name)
        expect(response.data.content).toBe(file.content)
      })
    })

    it('should update file content', async () => {
      await withAuthenticatedUser(async ({ client }, user) => {
        const project = await apiTestUtils.createTestProject(user.id)
        const file = await apiTestUtils.createTestFile(project.id)
        const updateData = {
          name: 'updated-file.js',
          content: 'console.log("Updated content");'
        }

        const response = await client.put(`/api/files/${file.id}`, updateData)

        expect(response.status).toBe(200)
        expect(response.data.name).toBe(updateData.name)
        expect(response.data.content).toBe(updateData.content)
      })
    })

    it('should delete file', async () => {
      await withAuthenticatedUser(async ({ client }, user) => {
        const project = await apiTestUtils.createTestProject(user.id)
        const file = await apiTestUtils.createTestFile(project.id)

        const response = await client.delete(`/api/files/${file.id}`)

        expect(response.status).toBe(204)

        // 驗證檔案已被刪除
        const getResponse = await client.get(`/api/files/${file.id}`)
        expect(getResponse.status).toBe(404)
      })
    })
  })

  describe('Authentication Routes', () => {
    it('should require authentication for protected routes', async () => {
      await withAPITest(async ({ client }) => {
        const response = await client.get('/api/projects')

        expect(response.status).toBe(401)
        expect(response.data).toHaveProperty('error')
      })
    })

    it('should reject invalid tokens', async () => {
      await withAPITest(async ({ client }) => {
        client.setAuthToken('invalid-token')

        const response = await client.get('/api/projects')

        expect(response.status).toBe(401)
        expect(response.data).toHaveProperty('error')
      })
    })

    it('should accept valid tokens', async () => {
      await withAuthenticatedUser(async ({ client }) => {
        const response = await client.get('/api/projects')

        expect(response.status).toBe(200)
      })
    })
  })

  describe('Error Handling', () => {
    it('should handle validation errors', async () => {
      await withAuthenticatedUser(async ({ client }) => {
        const invalidData = {
          // 缺少必要欄位
        }

        const response = await client.post('/api/projects', invalidData)

        expect(response.status).toBe(400)
        expect(response.data).toHaveProperty('error')
      })
    })

    it('should handle server errors gracefully', async () => {
      await withAuthenticatedUser(async ({ client }) => {
        // 嘗試建立具有無效外鍵的資料
        const invalidData = {
          name: 'Test File',
          content: 'Test content',
          projectId: 'non-existent-project-id'
        }

        const response = await client.post('/api/projects/non-existent-project-id/files', invalidData)

        expect([400, 404, 422]).toContain(response.status)
        expect(response.data).toHaveProperty('error')
      })
    })

    it('should return proper error format', async () => {
      await withAPITest(async ({ client }) => {
        const response = await client.get('/api/non-existent-endpoint')

        expect(response.status).toBe(404)
        apiTestUtils.validateErrorResponse(response, 404)
      })
    })
  })

  describe('CORS Support', () => {
    it('should handle CORS preflight requests', async () => {
      await withAPITest(async ({ client }) => {
        const response = await client.get('/api/projects', {
          headers: {
            'Origin': 'http://localhost:3000',
            'Access-Control-Request-Method': 'GET',
            'Access-Control-Request-Headers': 'Authorization'
          }
        })

        // 即使是 401，CORS 標頭也應該存在
        expect(response.headers['access-control-allow-origin']).toBeDefined()
      })
    })
  })
})