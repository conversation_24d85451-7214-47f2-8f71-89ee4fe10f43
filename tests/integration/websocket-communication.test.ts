import { describe, it, expect, beforeAll, afterAll, beforeEach, afterEach } from 'vitest'
import { TestServerManager } from '../utils/test-server-manager'
import { WebSocketTestUtils } from '../utils/websocket-test-utils'
import { MockWebSocket } from '../utils/mock-websocket'
import { DatabaseTestManager } from '../utils/database-test-manager'
import { TestDataFactory } from '../utils/test-data-factory'

describe('WebSocket Communication Integration Tests', () => {
  let serverManager: TestServerManager
  let wsTestUtils: WebSocketTestUtils
  let databaseManager: DatabaseTestManager
  let testDataFactory: TestDataFactory

  beforeAll(async () => {
    // Initialize test infrastructure
    serverManager = new TestServerManager({
      database: { resetOnStart: true },
      middleware: { cors: true, errorHandling: true }
    })
    
    await serverManager.start()
    
    // Initialize WebSocket test utilities
    wsTestUtils = new WebSocketTestUtils()
    
    // Initialize database utilities
    databaseManager = new DatabaseTestManager()
    await databaseManager.initializeTestDatabase()
    testDataFactory = new TestDataFactory(databaseManager.getPrismaClient())
  })

  afterAll(async () => {
    await serverManager.stop()
    await databaseManager.cleanup()
  })

  beforeEach(async () => {
    await serverManager.reset()
    await databaseManager.resetDatabase()
    testDataFactory.reset()
    wsTestUtils.reset()
  })

  afterEach(() => {
    wsTestUtils.reset()
  })

  describe('Real-time Project Updates', () => {
    it('should broadcast project creation to connected clients', async () => {
      // Create multiple WebSocket connections
      const client1 = wsTestUtils.createMockWebSocket({
        url: `ws://localhost:${serverManager.getPort()}/ws`
      })
      const client2 = wsTestUtils.createMockWebSocket({
        url: `ws://localhost:${serverManager.getPort()}/ws`
      })

      // Wait for connections to be established
      await wsTestUtils.waitForState(MockWebSocket.OPEN, 1000)

      // Subscribe clients to project updates
      client1.send(JSON.stringify({
        type: 'subscribe',
        channel: 'projects'
      }))

      client2.send(JSON.stringify({
        type: 'subscribe',
        channel: 'projects'
      }))

      // Create a project (this should trigger WebSocket broadcast)
      const project = await testDataFactory.createProject({
        name: 'WebSocket Test Project',
        description: 'Testing real-time updates'
      })

      // Simulate server broadcasting the project creation
      const broadcastMessage = {
        type: 'project_created',
        data: {
          id: project.id,
          name: project.name,
          description: project.description,
          createdAt: project.createdAt.toISOString()
        }
      }

      client1.simulateMessage(JSON.stringify(broadcastMessage))
      client2.simulateMessage(JSON.stringify(broadcastMessage))

      // Verify both clients received the message
      const client1Message = await wsTestUtils.waitForMessage(/project_created/, 1000)
      const client2Message = await wsTestUtils.waitForMessage(/project_created/, 1000)

      expect(JSON.parse(client1Message.data).data.id).toBe(project.id)
      expect(JSON.parse(client2Message.data).data.id).toBe(project.id)
    })

    it('should handle project updates in real-time', async () => {
      // Create initial project
      const project = await testDataFactory.createProject({
        name: 'Original Project Name',
        description: 'Original description'
      })

      // Create WebSocket connection
      const client = wsTestUtils.createMockWebSocket()
      await wsTestUtils.waitForState(MockWebSocket.OPEN)

      // Subscribe to project updates
      client.send(JSON.stringify({
        type: 'subscribe',
        channel: `project:${project.id}`
      }))

      // Update project in database
      const updatedProject = await databaseManager.getPrismaClient().project.update({
        where: { id: project.id },
        data: {
          name: 'Updated Project Name',
          description: 'Updated description'
        }
      })

      // Simulate server broadcasting the update
      client.simulateMessage(JSON.stringify({
        type: 'project_updated',
        data: {
          id: updatedProject.id,
          name: updatedProject.name,
          description: updatedProject.description,
          updatedAt: updatedProject.updatedAt.toISOString()
        }
      }))

      // Verify client received the update
      const updateMessage = await wsTestUtils.waitForMessage(/project_updated/, 1000)
      const updateData = JSON.parse(updateMessage.data)

      expect(updateData.data.name).toBe('Updated Project Name')
      expect(updateData.data.description).toBe('Updated description')
    })

    it('should handle project deletion broadcasts', async () => {
      // Create project
      const project = await testDataFactory.createProject({
        name: 'Project to Delete'
      })

      // Create WebSocket connection
      const client = wsTestUtils.createMockWebSocket()
      await wsTestUtils.waitForState(MockWebSocket.OPEN)

      // Subscribe to project updates
      client.send(JSON.stringify({
        type: 'subscribe',
        channel: 'projects'
      }))

      // Delete project
      await databaseManager.getPrismaClient().project.delete({
        where: { id: project.id }
      })

      // Simulate server broadcasting the deletion
      client.simulateMessage(JSON.stringify({
        type: 'project_deleted',
        data: {
          id: project.id
        }
      }))

      // Verify client received the deletion message
      const deleteMessage = await wsTestUtils.waitForMessage(/project_deleted/, 1000)
      const deleteData = JSON.parse(deleteMessage.data)

      expect(deleteData.data.id).toBe(project.id)
    })
  })

  describe('Real-time File Updates', () => {
    let testProject: any

    beforeEach(async () => {
      testProject = await testDataFactory.createProject({
        name: 'File Update Test Project'
      })
    })

    it('should broadcast file creation to subscribed clients', async () => {
      // Create WebSocket connection
      const client = wsTestUtils.createMockWebSocket()
      await wsTestUtils.waitForState(MockWebSocket.OPEN)

      // Subscribe to file updates for the project
      client.send(JSON.stringify({
        type: 'subscribe',
        channel: `project:${testProject.id}:files`
      }))

      // Create a file
      const file = await testDataFactory.createFile(testProject.id, {
        name: 'NewFile.vue',
        content: '<template><div>New File</div></template>'
      })

      // Simulate server broadcasting the file creation
      client.simulateMessage(JSON.stringify({
        type: 'file_created',
        data: {
          id: file.id,
          name: file.name,
          content: file.content,
          projectId: file.projectId,
          createdAt: file.createdAt.toISOString()
        }
      }))

      // Verify client received the message
      const fileMessage = await wsTestUtils.waitForMessage(/file_created/, 1000)
      const fileData = JSON.parse(fileMessage.data)

      expect(fileData.data.id).toBe(file.id)
      expect(fileData.data.name).toBe('NewFile.vue')
      expect(fileData.data.projectId).toBe(testProject.id)
    })

    it('should handle real-time file content updates', async () => {
      // Create initial file
      const file = await testDataFactory.createFile(testProject.id, {
        name: 'EditableFile.vue',
        content: '<template><div>Original Content</div></template>'
      })

      // Create WebSocket connection
      const client = wsTestUtils.createMockWebSocket()
      await wsTestUtils.waitForState(MockWebSocket.OPEN)

      // Subscribe to file updates
      client.send(JSON.stringify({
        type: 'subscribe',
        channel: `file:${file.id}`
      }))

      // Update file content
      const updatedFile = await databaseManager.getPrismaClient().file.update({
        where: { id: file.id },
        data: {
          content: '<template><div>Updated Content</div></template>'
        }
      })

      // Simulate server broadcasting the content update
      client.simulateMessage(JSON.stringify({
        type: 'file_updated',
        data: {
          id: updatedFile.id,
          name: updatedFile.name,
          content: updatedFile.content,
          updatedAt: updatedFile.updatedAt.toISOString()
        }
      }))

      // Verify client received the update
      const updateMessage = await wsTestUtils.waitForMessage(/file_updated/, 1000)
      const updateData = JSON.parse(updateMessage.data)

      expect(updateData.data.content).toBe('<template><div>Updated Content</div></template>')
    })

    it('should handle collaborative editing scenarios', async () => {
      // Create file for collaborative editing
      const file = await testDataFactory.createFile(testProject.id, {
        name: 'CollaborativeFile.vue',
        content: '<template><div>Collaborative Content</div></template>'
      })

      // Create multiple client connections
      const client1 = wsTestUtils.createMockWebSocket()
      const client2 = wsTestUtils.createMockWebSocket()
      
      await wsTestUtils.waitForState(MockWebSocket.OPEN)

      // Both clients subscribe to file updates
      client1.send(JSON.stringify({
        type: 'subscribe',
        channel: `file:${file.id}`
      }))

      client2.send(JSON.stringify({
        type: 'subscribe',
        channel: `file:${file.id}`
      }))

      // Client 1 sends an edit operation
      client1.send(JSON.stringify({
        type: 'file_edit',
        data: {
          fileId: file.id,
          operation: {
            type: 'insert',
            position: 20,
            text: 'New '
          }
        }
      }))

      // Simulate server broadcasting the edit to all clients
      const editBroadcast = {
        type: 'file_edit_applied',
        data: {
          fileId: file.id,
          operation: {
            type: 'insert',
            position: 20,
            text: 'New '
          },
          userId: 'client1'
        }
      }

      client1.simulateMessage(JSON.stringify(editBroadcast))
      client2.simulateMessage(JSON.stringify(editBroadcast))

      // Verify both clients received the edit
      const client1Edit = await wsTestUtils.waitForMessage(/file_edit_applied/, 1000)
      const client2Edit = await wsTestUtils.waitForMessage(/file_edit_applied/, 1000)

      expect(JSON.parse(client1Edit.data).data.operation.text).toBe('New ')
      expect(JSON.parse(client2Edit.data).data.operation.text).toBe('New ')
    })
  })

  describe('Connection Management', () => {
    it('should handle connection establishment and authentication', async () => {
      // Create WebSocket connection
      const client = wsTestUtils.createMockWebSocket({
        url: `ws://localhost:${serverManager.getPort()}/ws`
      })

      // Wait for connection to be established
      await wsTestUtils.waitForState(MockWebSocket.OPEN, 1000)

      // Send authentication message
      client.send(JSON.stringify({
        type: 'authenticate',
        data: {
          token: 'test-auth-token'
        }
      }))

      // Simulate server response
      client.simulateMessage(JSON.stringify({
        type: 'authenticated',
        data: {
          userId: 'test-user-id',
          sessionId: 'test-session-id'
        }
      }))

      // Verify authentication response
      const authMessage = await wsTestUtils.waitForMessage(/authenticated/, 1000)
      const authData = JSON.parse(authMessage.data)

      expect(authData.data.userId).toBe('test-user-id')
      expect(authData.data.sessionId).toBe('test-session-id')
    })

    it('should handle connection errors gracefully', async () => {
      // Create WebSocket connection that will fail
      const client = wsTestUtils.createMockWebSocket({
        shouldFailConnection: true
      })

      // Wait for error event
      const errorPromise = new Promise<void>((resolve) => {
        client.onerror = () => {
          resolve()
        }
      })

      await errorPromise
      expect(client.readyState).toBe(MockWebSocket.CLOSED)
    })

    it('should handle reconnection scenarios', async () => {
      // Create initial connection
      const client = wsTestUtils.createMockWebSocket()
      await wsTestUtils.waitForState(MockWebSocket.OPEN)

      // Subscribe to updates
      client.send(JSON.stringify({
        type: 'subscribe',
        channel: 'projects'
      }))

      // Simulate connection loss
      wsTestUtils.simulateServerDisconnect(1006, 'Connection lost')
      await wsTestUtils.waitForState(MockWebSocket.CLOSED)

      // Simulate reconnection
      await wsTestUtils.simulateReconnection()
      await wsTestUtils.waitForState(MockWebSocket.OPEN)

      // Re-subscribe after reconnection
      client.send(JSON.stringify({
        type: 'subscribe',
        channel: 'projects'
      }))

      // Verify reconnection was successful
      expect(client.readyState).toBe(MockWebSocket.OPEN)
    })

    it('should handle heartbeat/ping-pong mechanism', async () => {
      // Create WebSocket connection
      const client = wsTestUtils.createMockWebSocket()
      await wsTestUtils.waitForState(MockWebSocket.OPEN)

      // Start heartbeat test
      const heartbeat = wsTestUtils.createHeartbeatTest(1000) // 1 second interval
      heartbeat.start()

      // Let it run for a few heartbeats
      await new Promise(resolve => setTimeout(resolve, 3500))

      heartbeat.stop()

      // Check heartbeat statistics
      const stats = heartbeat.getStats()
      expect(stats.sent).toBeGreaterThanOrEqual(3)
      expect(stats.missed).toBe(0) // No missed heartbeats in normal operation
    })
  })

  describe('Message Broadcasting and Channels', () => {
    it('should handle channel subscription and unsubscription', async () => {
      // Create WebSocket connection
      const client = wsTestUtils.createMockWebSocket()
      await wsTestUtils.waitForState(MockWebSocket.OPEN)

      // Subscribe to a channel
      client.send(JSON.stringify({
        type: 'subscribe',
        channel: 'test-channel'
      }))

      // Simulate server confirmation
      client.simulateMessage(JSON.stringify({
        type: 'subscribed',
        channel: 'test-channel'
      }))

      // Verify subscription confirmation
      const subMessage = await wsTestUtils.waitForMessage(/subscribed/, 1000)
      expect(JSON.parse(subMessage.data).channel).toBe('test-channel')

      // Unsubscribe from the channel
      client.send(JSON.stringify({
        type: 'unsubscribe',
        channel: 'test-channel'
      }))

      // Simulate server confirmation
      client.simulateMessage(JSON.stringify({
        type: 'unsubscribed',
        channel: 'test-channel'
      }))

      // Verify unsubscription confirmation
      const unsubMessage = await wsTestUtils.waitForMessage(/unsubscribed/, 1000)
      expect(JSON.parse(unsubMessage.data).channel).toBe('test-channel')
    })

    it('should broadcast messages only to subscribed clients', async () => {
      // Create multiple clients
      const subscribedClient = wsTestUtils.createMockWebSocket()
      const unsubscribedClient = wsTestUtils.createMockWebSocket()

      await wsTestUtils.waitForState(MockWebSocket.OPEN)

      // Only one client subscribes to the channel
      subscribedClient.send(JSON.stringify({
        type: 'subscribe',
        channel: 'selective-channel'
      }))

      // Simulate broadcast message to the channel
      subscribedClient.simulateMessage(JSON.stringify({
        type: 'channel_message',
        channel: 'selective-channel',
        data: { message: 'This is a selective broadcast' }
      }))

      // Verify only subscribed client receives the message
      const message = await wsTestUtils.waitForMessage(/channel_message/, 1000)
      expect(JSON.parse(message.data).data.message).toBe('This is a selective broadcast')

      // Unsubscribed client should not receive any messages
      // (In a real scenario, we would verify this by checking message history)
    })

    it('should handle message queuing for disconnected clients', async () => {
      // Create client and subscribe
      const client = wsTestUtils.createMockWebSocket()
      await wsTestUtils.waitForState(MockWebSocket.OPEN)

      client.send(JSON.stringify({
        type: 'subscribe',
        channel: 'queue-test',
        options: { queueMessages: true }
      }))

      // Simulate disconnection
      wsTestUtils.simulateServerDisconnect(1000, 'Temporary disconnect')
      await wsTestUtils.waitForState(MockWebSocket.CLOSED)

      // Simulate messages being sent while disconnected
      // (In real implementation, server would queue these)

      // Simulate reconnection
      await wsTestUtils.simulateReconnection()
      await wsTestUtils.waitForState(MockWebSocket.OPEN)

      // Simulate server sending queued messages
      const queuedMessages = [
        { type: 'queued_message', data: { id: 1, content: 'Message 1' } },
        { type: 'queued_message', data: { id: 2, content: 'Message 2' } },
        { type: 'queued_message', data: { id: 3, content: 'Message 3' } }
      ]

      for (const message of queuedMessages) {
        client.simulateMessage(JSON.stringify(message))
      }

      // Verify all queued messages are received
      for (let i = 1; i <= 3; i++) {
        const message = await wsTestUtils.waitForMessage(/queued_message/, 1000)
        const data = JSON.parse(message.data)
        expect(data.data.id).toBe(i)
        expect(data.data.content).toBe(`Message ${i}`)
      }
    })
  })

  describe('Error Handling and Recovery', () => {
    it('should handle malformed message gracefully', async () => {
      // Create WebSocket connection
      const client = wsTestUtils.createMockWebSocket()
      await wsTestUtils.waitForState(MockWebSocket.OPEN)

      // Send malformed JSON
      client.send('{ invalid json }')

      // Simulate server error response
      client.simulateMessage(JSON.stringify({
        type: 'error',
        error: {
          code: 'INVALID_JSON',
          message: 'Invalid JSON format'
        }
      }))

      // Verify error message is received
      const errorMessage = await wsTestUtils.waitForMessage(/error/, 1000)
      const errorData = JSON.parse(errorMessage.data)

      expect(errorData.error.code).toBe('INVALID_JSON')
      expect(errorData.error.message).toBe('Invalid JSON format')
    })

    it('should handle server errors during message processing', async () => {
      // Create WebSocket connection
      const client = wsTestUtils.createMockWebSocket()
      await wsTestUtils.waitForState(MockWebSocket.OPEN)

      // Send a message that would cause server error
      client.send(JSON.stringify({
        type: 'invalid_operation',
        data: { someInvalidData: true }
      }))

      // Simulate server error response
      client.simulateMessage(JSON.stringify({
        type: 'error',
        error: {
          code: 'OPERATION_FAILED',
          message: 'Unknown operation type'
        }
      }))

      // Verify error handling
      const errorMessage = await wsTestUtils.waitForMessage(/error/, 1000)
      const errorData = JSON.parse(errorMessage.data)

      expect(errorData.error.code).toBe('OPERATION_FAILED')
    })

    it('should handle network timeouts and retries', async () => {
      // Create WebSocket connection with network delay
      wsTestUtils.simulateNetworkDelay(2000) // 2 second delay

      const client = wsTestUtils.createMockWebSocket()
      await wsTestUtils.waitForState(MockWebSocket.OPEN, 3000) // Extended timeout

      // Send message with timeout handling
      const startTime = Date.now()
      
      client.send(JSON.stringify({
        type: 'ping',
        timestamp: startTime
      }))

      // Simulate delayed response
      setTimeout(() => {
        client.simulateMessage(JSON.stringify({
          type: 'pong',
          timestamp: Date.now()
        }))
      }, 1500)

      // Wait for response
      const pongMessage = await wsTestUtils.waitForMessage(/pong/, 3000)
      const pongData = JSON.parse(pongMessage.data)

      const roundTripTime = pongData.timestamp - startTime
      expect(roundTripTime).toBeGreaterThan(1500) // Should account for network delay
    })
  })

  describe('Performance and Scalability', () => {
    it('should handle high-frequency message exchange', async () => {
      // Create WebSocket connection
      const client = wsTestUtils.createMockWebSocket()
      await wsTestUtils.waitForState(MockWebSocket.OPEN)

      const messageCount = 100
      const messages = []

      // Send high-frequency messages
      const startTime = Date.now()
      
      for (let i = 0; i < messageCount; i++) {
        const message = {
          type: 'high_frequency_test',
          id: i,
          timestamp: Date.now()
        }
        
        client.send(JSON.stringify(message))
        messages.push(message)
      }

      // Simulate server echoing all messages back
      for (const message of messages) {
        client.simulateMessage(JSON.stringify({
          type: 'echo',
          original: message
        }))
      }

      // Verify all messages are received
      const receivedMessages = []
      for (let i = 0; i < messageCount; i++) {
        const echoMessage = await wsTestUtils.waitForMessage(/echo/, 1000)
        receivedMessages.push(JSON.parse(echoMessage.data))
      }

      const endTime = Date.now()
      const duration = endTime - startTime

      expect(receivedMessages).toHaveLength(messageCount)
      expect(duration).toBeLessThan(5000) // Should complete within 5 seconds

      // Verify message order and integrity
      receivedMessages.forEach((received, index) => {
        expect(received.original.id).toBe(index)
      })
    })

    it('should handle multiple concurrent connections', async () => {
      const connectionCount = 10
      const clients = []

      // Create multiple concurrent connections
      for (let i = 0; i < connectionCount; i++) {
        const client = wsTestUtils.createMockWebSocket({
          url: `ws://localhost:${serverManager.getPort()}/ws?client=${i}`
        })
        clients.push(client)
      }

      // Wait for all connections to be established
      await wsTestUtils.waitForState(MockWebSocket.OPEN, 2000)

      // Each client subscribes to a unique channel
      clients.forEach((client, index) => {
        client.send(JSON.stringify({
          type: 'subscribe',
          channel: `client-${index}`
        }))
      })

      // Broadcast a message to each client's channel
      clients.forEach((client, index) => {
        client.simulateMessage(JSON.stringify({
          type: 'personal_message',
          channel: `client-${index}`,
          data: { message: `Hello client ${index}` }
        }))
      })

      // Verify each client receives their personal message
      for (let i = 0; i < connectionCount; i++) {
        const message = await wsTestUtils.waitForMessage(/personal_message/, 1000)
        const data = JSON.parse(message.data)
        expect(data.data.message).toContain(`client ${i}`)
      }

      // Clean up connections
      clients.forEach(client => {
        if (client.readyState === MockWebSocket.OPEN) {
          client.close()
        }
      })
    })

    it('should maintain performance under load', async () => {
      // Create WebSocket connection
      const client = wsTestUtils.createMockWebSocket()
      await wsTestUtils.waitForState(MockWebSocket.OPEN)

      // Subscribe to updates
      client.send(JSON.stringify({
        type: 'subscribe',
        channel: 'load-test'
      }))

      const messageCount = 1000
      const batchSize = 50
      const startTime = Date.now()

      // Send messages in batches to simulate load
      for (let batch = 0; batch < messageCount / batchSize; batch++) {
        const batchMessages = []
        
        for (let i = 0; i < batchSize; i++) {
          const messageId = batch * batchSize + i
          batchMessages.push({
            type: 'load_test_message',
            id: messageId,
            data: `Load test message ${messageId}`
          })
        }

        // Simulate server processing batch
        batchMessages.forEach(message => {
          client.simulateMessage(JSON.stringify(message))
        })

        // Small delay between batches
        await new Promise(resolve => setTimeout(resolve, 10))
      }

      // Verify all messages are received
      const receivedMessages = []
      for (let i = 0; i < messageCount; i++) {
        const message = await wsTestUtils.waitForMessage(/load_test_message/, 100)
        receivedMessages.push(JSON.parse(message.data))
      }

      const endTime = Date.now()
      const duration = endTime - startTime

      expect(receivedMessages).toHaveLength(messageCount)
      expect(duration).toBeLessThan(10000) // Should complete within 10 seconds

      // Verify message integrity
      receivedMessages.forEach((message, index) => {
        expect(message.id).toBe(index)
      })
    })
  })

  describe('Integration with Database Operations', () => {
    it('should sync WebSocket updates with database changes', async () => {
      // Create WebSocket connection
      const client = wsTestUtils.createMockWebSocket()
      await wsTestUtils.waitForState(MockWebSocket.OPEN)

      // Subscribe to project updates
      client.send(JSON.stringify({
        type: 'subscribe',
        channel: 'projects'
      }))

      // Create project in database
      const project = await testDataFactory.createProject({
        name: 'Database Sync Test',
        description: 'Testing database synchronization'
      })

      // Simulate WebSocket notification of database change
      client.simulateMessage(JSON.stringify({
        type: 'database_sync',
        operation: 'create',
        table: 'projects',
        data: {
          id: project.id,
          name: project.name,
          description: project.description,
          createdAt: project.createdAt.toISOString()
        }
      }))

      // Verify WebSocket message matches database state
      const syncMessage = await wsTestUtils.waitForMessage(/database_sync/, 1000)
      const syncData = JSON.parse(syncMessage.data)

      expect(syncData.data.id).toBe(project.id)
      expect(syncData.data.name).toBe(project.name)

      // Verify database state
      const dbProject = await databaseManager.getPrismaClient().project.findUnique({
        where: { id: project.id }
      })

      expect(dbProject).toBeDefined()
      expect(dbProject.name).toBe(syncData.data.name)
    })

    it('should handle database transaction rollbacks in WebSocket context', async () => {
      // Create WebSocket connection
      const client = wsTestUtils.createMockWebSocket()
      await wsTestUtils.waitForState(MockWebSocket.OPEN)

      // Subscribe to updates
      client.send(JSON.stringify({
        type: 'subscribe',
        channel: 'transactions'
      }))

      // Attempt database transaction that will fail
      try {
        await databaseManager.withTransaction(async (tx) => {
          // Create project
          const project = await tx.project.create({
            data: {
              name: 'Transaction Test Project',
              description: 'This will be rolled back'
            }
          })

          // Simulate WebSocket notification of tentative change
          client.simulateMessage(JSON.stringify({
            type: 'transaction_pending',
            operation: 'create',
            table: 'projects',
            data: project
          }))

          // Force transaction failure
          throw new Error('Intentional transaction failure')
        })
      } catch (error) {
        // Simulate WebSocket notification of rollback
        client.simulateMessage(JSON.stringify({
          type: 'transaction_rolled_back',
          operation: 'create',
          table: 'projects',
          reason: error.message
        }))
      }

      // Verify rollback notification
      const rollbackMessage = await wsTestUtils.waitForMessage(/transaction_rolled_back/, 1000)
      const rollbackData = JSON.parse(rollbackMessage.data)

      expect(rollbackData.reason).toBe('Intentional transaction failure')
      expect(rollbackData.operation).toBe('create')
    })
  })
})