// 測試環境設定 - 支援多環境
import { vi } from 'vitest'
import dotenv from 'dotenv'

// 載入環境變數
dotenv.config({ path: '.env.test' })
dotenv.config({ path: '.env' })

// 設置基本測試環境變數
process.env.NODE_ENV = 'test'
process.env.DATABASE_URL = process.env.DATABASE_URL || 'file:./prisma/test.db'

// 檢測當前測試環境
const isNodeEnvironment = typeof window === 'undefined'
const isBrowserEnvironment = typeof window !== 'undefined'

if (isBrowserEnvironment) {
  // 瀏覽器環境設置 (jsdom)
  const { config } = await import('@vue/test-utils')
  
  // Mock 環境變數
  vi.mock('process', () => ({
    env: {
      VITE_APP_NAME: 'UIGen Vue Test',
      VITE_API_BASE_URL: 'http://localhost:3001',
      NODE_ENV: 'test',
    },
  }))

  // 全域測試設定
  global.ResizeObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn(),
  }))

  // Mock IntersectionObserver
  global.IntersectionObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn(),
  }))

  // Mock matchMedia
  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: vi.fn().mockImplementation(query => ({
      matches: false,
      media: query,
      onchange: null,
      addListener: vi.fn(),
      removeListener: vi.fn(),
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
      dispatchEvent: vi.fn(),
    })),
  })

  // Mock scrollTo
  Object.defineProperty(window, 'scrollTo', {
    writable: true,
    value: vi.fn(),
  })

  // 設定 Vue Test Utils 全域配置
  config.global.config.globalProperties = {
    ...config.global.config.globalProperties,
  }

  // 為 Teleport 組件設定測試環境
  beforeEach(() => {
    // 確保每個測試都有乾淨的 DOM
    document.body.innerHTML = ''
    
    // 為 Teleport 創建目標容器
    const teleportTarget = document.createElement('div')
    teleportTarget.id = 'teleport-target'
    document.body.appendChild(teleportTarget)
  })
}

if (isNodeEnvironment) {
  // Node.js 環境設置 (server tests)
  
  // Mock console methods to reduce noise in tests
  const originalConsole = { ...console }

  beforeAll(() => {
    // Reduce console noise during tests
    console.log = vi.fn()
    console.info = vi.fn()
    console.warn = vi.fn()
    // Keep error for debugging
  })

  afterAll(() => {
    // Restore console
    Object.assign(console, originalConsole)
  })

  // Handle unhandled promise rejections in tests
  process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason)
  })
}

// 通用清理
afterEach(() => {
  vi.clearAllMocks()
})