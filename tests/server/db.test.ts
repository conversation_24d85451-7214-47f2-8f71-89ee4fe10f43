import { describe, it, expect, beforeAll, afterAll, beforeEach } from 'vitest'
import { DatabaseTestManager } from '../utils/database-test-manager'
import { TestDataFactory } from '../utils/test-data-factory'
import { PrismaClient } from '@prisma/client'

describe('Database Connection Tests', () => {
  let databaseManager: DatabaseTestManager
  let testDataFactory: TestDataFactory
  let prisma: PrismaClient

  beforeAll(async () => {
    // Initialize database test manager
    databaseManager = new DatabaseTestManager()
    await databaseManager.initializeTestDatabase()
    
    // Get Prisma client instance
    prisma = databaseManager.getPrismaClient()
    testDataFactory = new TestDataFactory(prisma)
  })

  afterAll(async () => {
    // Cleanup database resources
    await databaseManager.cleanup()
  })

  beforeEach(async () => {
    // Reset database state before each test
    await databaseManager.resetDatabase()
    testDataFactory.reset()
  })

  describe('Database Connection Management', () => {
    it('should establish database connection successfully', async () => {
      // Test database connection
      const isConnected = await databaseManager.isConnected()
      expect(isConnected).toBe(true)
    })

    it('should handle database connection errors gracefully', async () => {
      // Test connection error handling
      const invalidManager = new DatabaseTestManager()
      
      // Try to connect with invalid URL
      await expect(async () => {
        await invalidManager.initializeTestDatabase()
      }).rejects.toThrow()
    })

    it('should maintain connection pool properly', async () => {
      // Test connection pool management
      const connections = []
      
      // Create multiple connections
      for (let i = 0; i < 5; i++) {
        const manager = new DatabaseTestManager()
        await manager.initializeTestDatabase()
        connections.push(manager)
      }

      // Verify all connections are active
      for (const manager of connections) {
        const isConnected = await manager.isConnected()
        expect(isConnected).toBe(true)
      }

      // Cleanup all connections
      for (const manager of connections) {
        await manager.cleanup()
      }
    })

    it('should handle connection timeout scenarios', async () => {
      // Test connection timeout handling
      const manager = new DatabaseTestManager()
      
      // Set short timeout for testing
      const startTime = Date.now()
      
      try {
        await manager.initializeTestDatabase()
        const endTime = Date.now()
        
        // Connection should complete within reasonable time
        expect(endTime - startTime).toBeLessThan(10000) // 10 seconds
      } finally {
        await manager.cleanup()
      }
    })
  })

  describe('Database Query Operations', () => {
    it('should execute basic queries successfully', async () => {
      // Test basic query execution
      const result = await prisma.$queryRaw`SELECT 1 as test`
      expect(result).toBeDefined()
      expect(Array.isArray(result)).toBe(true)
    })

    it('should handle complex queries with joins', async () => {
      // Create test data
      const project = await testDataFactory.createProject()
      const files = await testDataFactory.createFiles(project.id, 3)

      // Execute complex query with join
      const projectWithFiles = await prisma.project.findUnique({
        where: { id: project.id },
        include: { files: true }
      })

      expect(projectWithFiles).toBeDefined()
      expect(projectWithFiles!.files).toHaveLength(3)
      expect(projectWithFiles!.files.map(f => f.id)).toEqual(
        expect.arrayContaining(files.map(f => f.id))
      )
    })

    it('should handle query errors appropriately', async () => {
      // Test query error handling
      await expect(async () => {
        await prisma.$queryRaw`SELECT * FROM nonexistent_table`
      }).rejects.toThrow()
    })

    it('should support parameterized queries', async () => {
      // Create test project
      const project = await testDataFactory.createProject({
        name: 'Test Project for Query'
      })

      // Test parameterized query
      const result = await prisma.project.findMany({
        where: {
          name: {
            contains: 'Test Project'
          }
        }
      })

      expect(result).toHaveLength(1)
      expect(result[0].id).toBe(project.id)
    })

    it('should handle large result sets efficiently', async () => {
      // Create multiple projects
      const projects = await testDataFactory.createProjects(50)

      const startTime = Date.now()
      
      // Query all projects
      const allProjects = await prisma.project.findMany()
      
      const endTime = Date.now()
      const queryTime = endTime - startTime

      expect(allProjects).toHaveLength(50)
      expect(queryTime).toBeLessThan(5000) // Should complete within 5 seconds
    })
  })

  describe('Transaction Management', () => {
    it('should handle transactions correctly', async () => {
      // Test transaction execution
      const result = await databaseManager.withTransaction(async () => {
        const project = await testDataFactory.createProject({
          name: 'Transaction Test Project'
        })
        
        const files = await testDataFactory.createFiles(project.id, 2)
        
        return { project, files }
      })

      expect(result.project).toBeDefined()
      expect(result.files).toHaveLength(2)

      // Verify data was committed
      const savedProject = await prisma.project.findUnique({
        where: { id: result.project.id },
        include: { files: true }
      })

      expect(savedProject).toBeDefined()
      expect(savedProject!.files).toHaveLength(2)
    })

    it('should rollback transactions on error', async () => {
      const initialProjectCount = await prisma.project.count()

      // Test transaction rollback
      await expect(async () => {
        await databaseManager.withTransaction(async () => {
          await testDataFactory.createProject({
            name: 'Rollback Test Project'
          })
          
          // Force an error to trigger rollback
          throw new Error('Intentional error for rollback test')
        })
      }).rejects.toThrow('Intentional error for rollback test')

      // Verify no data was committed
      const finalProjectCount = await prisma.project.count()
      expect(finalProjectCount).toBe(initialProjectCount)
    })

    it('should handle nested transactions', async () => {
      // Test nested transaction handling
      const result = await databaseManager.withTransaction(async () => {
        const project = await testDataFactory.createProject({
          name: 'Outer Transaction Project'
        })

        // Nested transaction
        const files = await databaseManager.withTransaction(async () => {
          return await testDataFactory.createFiles(project.id, 3)
        })

        return { project, files }
      })

      expect(result.project).toBeDefined()
      expect(result.files).toHaveLength(3)

      // Verify all data was committed
      const savedProject = await prisma.project.findUnique({
        where: { id: result.project.id },
        include: { files: true }
      })

      expect(savedProject).toBeDefined()
      expect(savedProject!.files).toHaveLength(3)
    })

    it('should handle concurrent transactions', async () => {
      // Test concurrent transaction execution
      const transactionPromises = []

      for (let i = 0; i < 5; i++) {
        const promise = databaseManager.withTransaction(async () => {
          return await testDataFactory.createProject({
            name: `Concurrent Project ${i}`
          })
        })
        transactionPromises.push(promise)
      }

      // Wait for all transactions to complete
      const results = await Promise.all(transactionPromises)

      expect(results).toHaveLength(5)
      results.forEach((project, index) => {
        expect(project.name).toBe(`Concurrent Project ${index}`)
      })

      // Verify all projects were created
      const allProjects = await prisma.project.findMany({
        where: {
          name: {
            startsWith: 'Concurrent Project'
          }
        }
      })

      expect(allProjects).toHaveLength(5)
    })
  })

  describe('Database Schema Validation', () => {
    it('should validate database schema integrity', async () => {
      // Test schema validation
      const tables = await prisma.$queryRaw`
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public'
      ` as Array<{ table_name: string }>

      const tableNames = tables.map(t => t.table_name)
      
      // Verify required tables exist
      expect(tableNames).toContain('Project')
      expect(tableNames).toContain('File')
    })

    it('should validate foreign key constraints', async () => {
      // Test foreign key constraint validation
      const project = await testDataFactory.createProject()
      
      // Try to create file with invalid project ID
      await expect(async () => {
        await prisma.file.create({
          data: {
            name: 'test.txt',
            content: 'test content',
            projectId: 'invalid-project-id'
          }
        })
      }).rejects.toThrow()
    })

    it('should validate unique constraints', async () => {
      // Create project with unique name
      const project1 = await testDataFactory.createProject({
        name: 'Unique Project Name'
      })

      // Try to create another project with same name (if unique constraint exists)
      // Note: This test assumes there might be a unique constraint on project names
      // Adjust based on actual schema requirements
      expect(project1).toBeDefined()
    })

    it('should validate required fields', async () => {
      // Test required field validation
      await expect(async () => {
        await prisma.project.create({
          data: {
            // Missing required 'name' field
            description: 'Project without name'
          } as any
        })
      }).rejects.toThrow()
    })
  })

  describe('Database Performance', () => {
    it('should execute queries within acceptable time limits', async () => {
      // Create test data
      await testDataFactory.createProjects(10)

      const startTime = Date.now()
      
      // Execute query
      const projects = await prisma.project.findMany({
        include: { files: true }
      })
      
      const endTime = Date.now()
      const queryTime = endTime - startTime

      expect(projects).toBeDefined()
      expect(queryTime).toBeLessThan(2000) // Should complete within 2 seconds
    })

    it('should handle bulk operations efficiently', async () => {
      // Test bulk insert performance
      const projectData = Array.from({ length: 20 }, (_, i) => ({
        name: `Bulk Project ${i}`,
        description: `Description for project ${i}`
      }))

      const startTime = Date.now()
      
      // Bulk create projects
      await prisma.project.createMany({
        data: projectData
      })
      
      const endTime = Date.now()
      const insertTime = endTime - startTime

      expect(insertTime).toBeLessThan(3000) // Should complete within 3 seconds

      // Verify all projects were created
      const createdProjects = await prisma.project.findMany({
        where: {
          name: {
            startsWith: 'Bulk Project'
          }
        }
      })

      expect(createdProjects).toHaveLength(20)
    })

    it('should optimize query execution plans', async () => {
      // Create test data with relationships
      const projects = await testDataFactory.createProjects(5)
      
      for (const project of projects) {
        await testDataFactory.createFiles(project.id, 3)
      }

      const startTime = Date.now()
      
      // Execute optimized query
      const projectsWithFiles = await prisma.project.findMany({
        include: {
          files: {
            select: {
              id: true,
              name: true
            }
          }
        }
      })
      
      const endTime = Date.now()
      const queryTime = endTime - startTime

      expect(projectsWithFiles).toHaveLength(5)
      expect(projectsWithFiles.every(p => p.files.length === 3)).toBe(true)
      expect(queryTime).toBeLessThan(1500) // Should complete within 1.5 seconds
    })
  })

  describe('Database Cleanup and Maintenance', () => {
    it('should clean up test data properly', async () => {
      // Create test data
      const project = await testDataFactory.createProject()
      await testDataFactory.createFiles(project.id, 2)

      // Verify data exists
      let projectCount = await prisma.project.count()
      let fileCount = await prisma.file.count()
      
      expect(projectCount).toBeGreaterThan(0)
      expect(fileCount).toBeGreaterThan(0)

      // Clean up data
      await testDataFactory.cleanup()

      // Verify data was cleaned up
      projectCount = await prisma.project.count()
      fileCount = await prisma.file.count()
      
      expect(projectCount).toBe(0)
      expect(fileCount).toBe(0)
    })

    it('should reset database state between tests', async () => {
      // This test verifies that beforeEach cleanup works
      const initialProjectCount = await prisma.project.count()
      const initialFileCount = await prisma.file.count()

      expect(initialProjectCount).toBe(0)
      expect(initialFileCount).toBe(0)
    })

    it('should handle database maintenance operations', async () => {
      // Test database maintenance operations
      const tableCounts = await databaseManager.getTableCounts()
      
      expect(tableCounts).toBeDefined()
      expect(typeof tableCounts.projects).toBe('number')
      expect(typeof tableCounts.files).toBe('number')
    })
  })
})