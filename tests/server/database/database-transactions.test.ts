// 資料庫事務測試
import { describe, it, expect, beforeEach, afterEach } from 'vitest'
import { getDatabaseTestManager } from '../../utils/database-test-manager'
import { getTestDataFactory } from '../../utils/test-data-factory'

describe('Database Transactions', () => {
  const dbManager = getDatabaseTestManager()
  const dataFactory = getTestDataFactory()

  beforeEach(async () => {
    await dbManager.initializeTestDatabase()
    await dbManager.cleanupTestData()
  })

  afterEach(async () => {
    await dbManager.cleanupTestData()
  })

  describe('Transaction Success', () => {
    it('should commit transaction when all operations succeed', async () => {
      const result = await dbManager.withTransaction(async (tx) => {
        // 在事務中建立專案
        const project = await tx.project.create({
          data: {
            name: '事務測試專案',
            description: '測試事務提交'
          }
        })

        // 在事務中建立檔案
        const file = await tx.file.create({
          data: {
            name: 'transaction-test.vue',
            content: '<template><div>事務測試</div></template>',
            projectId: project.id
          }
        })

        return { project, file }
      })

      // 驗證資料已提交
      expect(result.project).toBeDefined()
      expect(result.file).toBeDefined()

      // 驗證資料在資料庫中存在
      const savedProject = await dbManager.getPrismaClient().project.findUnique({
        where: { id: result.project.id }
      })
      const savedFile = await dbManager.getPrismaClient().file.findUnique({
        where: { id: result.file.id }
      })

      expect(savedProject).toBeDefined()
      expect(savedFile).toBeDefined()
      expect(savedFile?.projectId).toBe(savedProject?.id)
    })

    it('should handle nested transactions correctly', async () => {
      const result = await dbManager.withTransaction(async (tx) => {
        // 外層事務：建立專案
        const project = await tx.project.create({
          data: {
            name: '巢狀事務測試',
            description: '測試巢狀事務'
          }
        })

        // 內層操作：建立多個檔案
        const files = await Promise.all([
          tx.file.create({
            data: {
              name: 'file1.vue',
              content: '<template><div>檔案1</div></template>',
              projectId: project.id
            }
          }),
          tx.file.create({
            data: {
              name: 'file2.vue',
              content: '<template><div>檔案2</div></template>',
              projectId: project.id
            }
          })
        ])

        return { project, files }
      })

      expect(result.project).toBeDefined()
      expect(result.files).toHaveLength(2)

      // 驗證所有資料都已提交
      const stats = await dbManager.getDatabaseStats()
      expect(stats.projectCount).toBe(1)
      expect(stats.fileCount).toBe(2)
    })
  })

  describe('Transaction Rollback', () => {
    it('should rollback transaction when operation fails', async () => {
      // 先建立一個專案作為基準
      const existingProject = await dataFactory.createProject({
        name: '現有專案'
      })

      await expect(
        dbManager.withTransaction(async (tx) => {
          // 建立新專案
          const newProject = await tx.project.create({
            data: {
              name: '將被回滾的專案',
              description: '這個專案應該被回滾'
            }
          })

          // 建立檔案
          await tx.file.create({
            data: {
              name: 'rollback-test.vue',
              content: '<template><div>回滾測試</div></template>',
              projectId: newProject.id
            }
          })

          // 故意造成錯誤 (嘗試建立重複的檔案名稱)
          await tx.file.create({
            data: {
              name: 'rollback-test.vue', // 重複名稱
              content: '<template><div>重複檔案</div></template>',
              projectId: newProject.id
            }
          })

          return newProject
        })
      ).rejects.toThrow()

      // 驗證事務已回滾，只有原本的專案存在
      const stats = await dbManager.getDatabaseStats()
      expect(stats.projectCount).toBe(1) // 只有 existingProject
      expect(stats.fileCount).toBe(0)

      // 驗證原有專案仍然存在
      const savedProject = await dbManager.getPrismaClient().project.findUnique({
        where: { id: existingProject.id }
      })
      expect(savedProject).toBeDefined()
    })

    it('should rollback on foreign key constraint violation', async () => {
      await expect(
        dbManager.withTransaction(async (tx) => {
          // 建立專案
          const project = await tx.project.create({
            data: {
              name: '外鍵測試專案',
              description: '測試外鍵約束回滾'
            }
          })

          // 建立正常檔案
          await tx.file.create({
            data: {
              name: 'normal-file.vue',
              content: '<template><div>正常檔案</div></template>',
              projectId: project.id
            }
          })

          // 嘗試建立檔案但使用無效的 projectId
          await tx.file.create({
            data: {
              name: 'invalid-file.vue',
              content: '<template><div>無效檔案</div></template>',
              projectId: 'invalid-project-id'
            }
          })

          return project
        })
      ).rejects.toThrow()

      // 驗證整個事務都被回滾
      const stats = await dbManager.getDatabaseStats()
      expect(stats.projectCount).toBe(0)
      expect(stats.fileCount).toBe(0)
    })
  })

  describe('Transaction Isolation', () => {
    it('should handle concurrent transactions correctly', async () => {
      // 並發執行多個事務
      const transactionPromises = Array.from({ length: 3 }, (_, i) =>
        dbManager.withTransaction(async (tx) => {
          const project = await tx.project.create({
            data: {
              name: `並發專案-${i + 1}`,
              description: `並發事務測試 ${i + 1}`
            }
          })

          const file = await tx.file.create({
            data: {
              name: `concurrent-${i + 1}.vue`,
              content: `<template><div>並發檔案 ${i + 1}</div></template>`,
              projectId: project.id
            }
          })

          return { project, file }
        })
      )

      const results = await Promise.all(transactionPromises)

      expect(results).toHaveLength(3)
      results.forEach((result, index) => {
        expect(result.project.name).toBe(`並發專案-${index + 1}`)
        expect(result.file.name).toBe(`concurrent-${index + 1}.vue`)
      })

      // 驗證所有資料都已正確提交
      const stats = await dbManager.getDatabaseStats()
      expect(stats.projectCount).toBe(3)
      expect(stats.fileCount).toBe(3)
    })

    it('should maintain data consistency during concurrent operations', async () => {
      // 建立基礎專案
      const baseProject = await dataFactory.createProject({
        name: '基礎專案'
      })

      // 並發在同一專案中建立檔案
      const filePromises = Array.from({ length: 5 }, (_, i) =>
        dbManager.withTransaction(async (tx) => {
          return await tx.file.create({
            data: {
              name: `consistency-test-${i + 1}.vue`,
              content: `<template><div>一致性測試 ${i + 1}</div></template>`,
              projectId: baseProject.id
            }
          })
        })
      )

      const files = await Promise.all(filePromises)

      expect(files).toHaveLength(5)
      files.forEach((file, index) => {
        expect(file.name).toBe(`consistency-test-${index + 1}.vue`)
        expect(file.projectId).toBe(baseProject.id)
      })

      // 驗證資料一致性
      const projectWithFiles = await dbManager.getPrismaClient().project.findUnique({
        where: { id: baseProject.id },
        include: { files: true }
      })

      expect(projectWithFiles?.files).toHaveLength(5)
    })
  })

  describe('Transaction Error Handling', () => {
    it('should provide detailed error information on transaction failure', async () => {
      try {
        await dbManager.withTransaction(async (tx) => {
          await tx.project.create({
            data: {
              name: '錯誤測試專案',
              description: '測試錯誤處理'
            }
          })

          // 故意造成錯誤
          throw new Error('自定義事務錯誤')
        })
      } catch (error) {
        expect(error).toBeInstanceOf(Error)
        expect((error as Error).message).toBe('自定義事務錯誤')
      }

      // 驗證事務已回滾
      const stats = await dbManager.getDatabaseStats()
      expect(stats.projectCount).toBe(0)
    })

    it('should handle database connection errors in transactions', async () => {
      try {
        await expect(
          dbManager.withTransaction(async (tx) => {
            // 嘗試執行無效的 SQL
            await tx.$executeRaw`INVALID SQL STATEMENT`
            return 'should not reach here'
          })
        ).rejects.toThrow()
      } catch (error) {
        // 預期會拋出錯誤
        expect(error).toBeDefined()
      }

      // 驗證資料庫管理器仍然可用
      const isConnected = await dbManager.checkConnection()
      expect(isConnected).toBe(true)
    })
  })
})