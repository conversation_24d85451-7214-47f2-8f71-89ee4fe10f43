// 資料庫約束驗證測試
import { describe, it, expect, beforeEach, afterEach } from 'vitest'
import { getDatabaseTestManager } from '../../utils/database-test-manager'
import { getTestDataFactory } from '../../utils/test-data-factory'

describe('Database Constraint Validation', () => {
  const dbManager = getDatabaseTestManager()
  const dataFactory = getTestDataFactory()

  beforeEach(async () => {
    await dbManager.initializeTestDatabase()
    await dbManager.cleanupTestData()
  })

  afterEach(async () => {
    await dbManager.cleanupTestData()
  })

  describe('Constraint Validation Methods', () => {
    it('should detect foreign key violations', async () => {
      // 建立一個專案和檔案
      const { project, files } = await dataFactory.createProjectWithFiles(2)
      
      // 手動刪除專案但保留檔案 (模擬外鍵違規)
      await dbManager.getPrismaClient().$executeRaw`DELETE FROM Project WHERE id = ${project.id}`
      
      // 驗證約束
      const violations = await dbManager.validateConstraints()
      
      expect(violations.foreignKeyViolations).toHaveLength(2)
      expect(violations.foreignKeyViolations[0].table).toBe('File')
      expect(violations.foreignKeyViolations[0].field).toBe('projectId')
      expect(violations.foreignKeyViolations[0].value).toBe(project.id)
    })

    it('should detect unique constraint violations', async () => {
      const project = await dataFactory.createProject()
      
      // 手動插入重複的檔案名稱 (繞過 Prisma 驗證)
      await dbManager.getPrismaClient().$executeRaw`
        INSERT INTO File (id, name, content, projectId, createdAt, updatedAt) 
        VALUES ('test-1', 'duplicate.vue', 'content1', ${project.id}, datetime('now'), datetime('now'))
      `
      await dbManager.getPrismaClient().$executeRaw`
        INSERT INTO File (id, name, content, projectId, createdAt, updatedAt) 
        VALUES ('test-2', 'duplicate.vue', 'content2', ${project.id}, datetime('now'), datetime('now'))
      `
      
      // 驗證約束
      const violations = await dbManager.validateConstraints()
      
      expect(violations.uniqueConstraintViolations).toHaveLength(1)
      expect(violations.uniqueConstraintViolations[0].table).toBe('File')
      expect(violations.uniqueConstraintViolations[0].constraint).toBe('projectId_name_unique')
      expect(violations.uniqueConstraintViolations[0].values.name).toBe('duplicate.vue')
      expect(violations.uniqueConstraintViolations[0].values.count).toBe(2)
    })

    it('should fix constraint violations', async () => {
      const project = await dataFactory.createProject()
      
      // 建立違規資料
      await dbManager.getPrismaClient().$executeRaw`
        INSERT INTO File (id, name, content, projectId, createdAt, updatedAt) 
        VALUES ('orphan-1', 'orphan.vue', 'content', 'non-existent-project', datetime('now'), datetime('now'))
      `
      await dbManager.getPrismaClient().$executeRaw`
        INSERT INTO File (id, name, content, projectId, createdAt, updatedAt) 
        VALUES ('dup-1', 'duplicate.vue', 'content1', ${project.id}, datetime('now'), datetime('now'))
      `
      await dbManager.getPrismaClient().$executeRaw`
        INSERT INTO File (id, name, content, projectId, createdAt, updatedAt) 
        VALUES ('dup-2', 'duplicate.vue', 'content2', ${project.id}, datetime('now'), datetime('now'))
      `
      
      // 修復違規
      await dbManager.fixConstraintViolations()
      
      // 驗證修復結果
      const violations = await dbManager.validateConstraints()
      expect(violations.foreignKeyViolations).toHaveLength(0)
      expect(violations.uniqueConstraintViolations).toHaveLength(0)
      
      // 確認只保留了有效的檔案
      const remainingFiles = await dbManager.getPrismaClient().file.findMany()
      expect(remainingFiles).toHaveLength(1)
      expect(remainingFiles[0].name).toBe('duplicate.vue')
      expect(remainingFiles[0].projectId).toBe(project.id)
    })
  })

  describe('Transaction Isolation Levels', () => {
    it('should handle serializable isolation correctly', async () => {
      const project = await dataFactory.createProject()
      
      // 並發事務測試
      const transaction1 = dbManager.withTransaction(async (tx) => {
        // 讀取專案
        const proj = await tx.project.findUnique({ where: { id: project.id } })
        
        // 模擬處理時間
        await new Promise(resolve => setTimeout(resolve, 10))
        
        // 更新專案
        return await tx.project.update({
          where: { id: project.id },
          data: { description: '事務1更新' }
        })
      })
      
      const transaction2 = dbManager.withTransaction(async (tx) => {
        // 讀取專案
        const proj = await tx.project.findUnique({ where: { id: project.id } })
        
        // 模擬處理時間
        await new Promise(resolve => setTimeout(resolve, 5))
        
        // 更新專案
        return await tx.project.update({
          where: { id: project.id },
          data: { description: '事務2更新' }
        })
      })
      
      // 等待兩個事務完成
      const [result1, result2] = await Promise.all([transaction1, transaction2])
      
      // 驗證最終狀態
      const finalProject = await dbManager.getPrismaClient().project.findUnique({
        where: { id: project.id }
      })
      
      expect(finalProject?.description).toMatch(/事務[12]更新/)
    })
  })

  describe('Error Handling Improvements', () => {
    it('should provide detailed foreign key error messages', async () => {
      await expect(
        dataFactory.createFile('non-existent-project-id', {
          name: 'test.vue',
          content: 'test content'
        })
      ).rejects.toThrow('Project with id "non-existent-project-id" does not exist')
    })

    it('should provide detailed unique constraint error messages', async () => {
      const project = await dataFactory.createProject()
      
      // 建立第一個檔案
      await dataFactory.createFile(project.id, {
        name: 'duplicate.vue',
        content: 'first content'
      })
      
      // 嘗試建立重複檔案
      await expect(
        dataFactory.createFile(project.id, {
          name: 'duplicate.vue',
          content: 'second content'
        })
      ).rejects.toThrow('File with name "duplicate.vue" already exists in project')
    })

    it('should handle transaction timeout correctly', async () => {
      // 測試事務超時 (這個測試可能需要較長時間)
      const longRunningTransaction = dbManager.withTransaction(async (tx) => {
        await tx.project.create({
          data: { name: '長時間事務測試', description: '測試超時' }
        })
        
        // 模擬長時間操作 (超過事務超時時間)
        await new Promise(resolve => setTimeout(resolve, 15000)) // 15秒
        
        return 'should timeout'
      })
      
      // 這個測試應該在 10 秒內超時
      await expect(longRunningTransaction).rejects.toThrow()
    }, 12000) // 給測試 12 秒超時時間
  })

  describe('Data Creation Order Validation', () => {
    it('should enforce correct data creation order in transactions', async () => {
      const result = await dbManager.withTransaction(async (tx) => {
        // 正確順序：先建立專案，再建立檔案
        const project = await tx.project.create({
          data: {
            name: '順序測試專案',
            description: '測試建立順序'
          }
        })
        
        const files = await Promise.all([
          tx.file.create({
            data: {
              name: 'file1.vue',
              content: 'content1',
              projectId: project.id
            }
          }),
          tx.file.create({
            data: {
              name: 'file2.vue',
              content: 'content2',
              projectId: project.id
            }
          })
        ])
        
        return { project, files }
      })
      
      expect(result.project).toBeDefined()
      expect(result.files).toHaveLength(2)
      expect(result.files[0].projectId).toBe(result.project.id)
      expect(result.files[1].projectId).toBe(result.project.id)
    })

    it('should rollback when creation order is violated', async () => {
      await expect(
        dbManager.withTransaction(async (tx) => {
          // 錯誤順序：嘗試先建立檔案，再建立專案
          const file = await tx.file.create({
            data: {
              name: 'invalid-order.vue',
              content: 'content',
              projectId: 'will-be-created-later'
            }
          })
          
          const project = await tx.project.create({
            data: {
              id: 'will-be-created-later',
              name: '後建立的專案',
              description: '這應該失敗'
            }
          })
          
          return { project, file }
        })
      ).rejects.toThrow()
      
      // 驗證沒有資料被建立
      const stats = await dbManager.getDatabaseStats()
      expect(stats.projectCount).toBe(0)
      expect(stats.fileCount).toBe(0)
    })
  })
})