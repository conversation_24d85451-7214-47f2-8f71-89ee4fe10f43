// 資料庫隔離機制測試
import { describe, it, expect, beforeEach, afterEach } from 'vitest'
import { 
  getDatabaseTestManager, 
  createIsolatedDatabaseTestManager,
  cleanupDatabaseTestManager,
  cleanupAllIsolatedManagers
} from '../../utils/database-test-manager'
import { getTestDataFactory } from '../../utils/test-data-factory'

describe('Database Isolation Mechanisms', () => {
  afterEach(async () => {
    // 清理所有隔離的管理器
    await cleanupAllIsolatedManagers()
  })

  describe('Test Instance Isolation', () => {
    it('should create isolated database instances for different tests', async () => {
      const manager1 = createIsolatedDatabaseTestManager()
      const manager2 = createIsolatedDatabaseTestManager()

      // 初始化兩個管理器
      await manager1.initializeTestDatabase()
      await manager2.initializeTestDatabase()

      // 驗證它們有不同的測試 ID
      expect(manager1.getTestId()).not.toBe(manager2.getTestId())

      // 在第一個管理器中建立資料
      const factory1 = new (await import('../../utils/test-data-factory')).TestDataFactory()
      factory1['dbManager'] = manager1
      const project1 = await factory1.createProject({ name: '隔離測試專案1' })

      // 在第二個管理器中建立資料
      const factory2 = new (await import('../../utils/test-data-factory')).TestDataFactory()
      factory2['dbManager'] = manager2
      const project2 = await factory2.createProject({ name: '隔離測試專案2' })

      // 驗證資料隔離
      const stats1 = await manager1.getDatabaseStats()
      const stats2 = await manager2.getDatabaseStats()

      expect(stats1.projectCount).toBe(1)
      expect(stats2.projectCount).toBe(1)

      // 驗證隔離狀態
      const isolation = await manager1.validateTestIsolation(manager2)
      expect(isolation.isIsolated).toBe(true)
      expect(isolation.conflicts).toHaveLength(0)

      // 清理
      await manager1.disconnect()
      await manager2.disconnect()
    })

    it('should maintain data isolation during concurrent operations', async () => {
      const managers = Array.from({ length: 3 }, () => createIsolatedDatabaseTestManager())
      
      // 並發初始化
      await Promise.all(managers.map(m => m.initializeTestDatabase()))

      // 並發建立資料
      const results = await Promise.all(
        managers.map(async (manager, index) => {
          const factory = new (await import('../../utils/test-data-factory')).TestDataFactory()
          factory['dbManager'] = manager
          
          return await factory.createProjectWithFiles(index + 1, {
            name: `並發測試專案-${index + 1}`
          })
        })
      )

      // 驗證每個管理器都有正確的資料
      for (let i = 0; i < managers.length; i++) {
        const stats = await managers[i].getDatabaseStats()
        expect(stats.projectCount).toBe(1)
        expect(stats.fileCount).toBe(i + 1)
        expect(results[i].project.name).toBe(`並發測試專案-${i + 1}`)
      }

      // 清理
      await Promise.all(managers.map(m => m.disconnect()))
    })
  })

  describe('Resource Lock Management', () => {
    it('should handle resource locks correctly', async () => {
      const manager = createIsolatedDatabaseTestManager()
      await manager.initializeTestDatabase()

      const resourceName = 'test-resource'
      const results: string[] = []

      // 並發執行需要相同資源的操作
      const operations = Array.from({ length: 3 }, (_, i) =>
        manager.withIsolatedTransaction(async (tx) => {
          // 模擬資源使用
          results.push(`Operation ${i + 1} started`)
          await new Promise(resolve => setTimeout(resolve, 50))
          results.push(`Operation ${i + 1} completed`)
          
          return `Result ${i + 1}`
        }, {
          resourceLocks: [resourceName]
        })
      )

      const operationResults = await Promise.all(operations)

      // 驗證操作結果
      expect(operationResults).toHaveLength(3)
      operationResults.forEach((result, index) => {
        expect(result).toBe(`Result ${index + 1}`)
      })

      // 驗證資源鎖確保了順序執行
      expect(results).toHaveLength(6)
      
      await manager.disconnect()
    })

    it('should release resource locks after transaction completion', async () => {
      const manager = createIsolatedDatabaseTestManager()
      await manager.initializeTestDatabase()

      const resourceName = 'test-resource-release'

      // 執行第一個事務
      await manager.withIsolatedTransaction(async (tx) => {
        await new Promise(resolve => setTimeout(resolve, 10))
        return 'first transaction'
      }, {
        resourceLocks: [resourceName]
      })

      // 執行第二個事務 (應該能夠獲取相同的資源鎖)
      const result = await manager.withIsolatedTransaction(async (tx) => {
        return 'second transaction'
      }, {
        resourceLocks: [resourceName]
      })

      expect(result).toBe('second transaction')
      
      await manager.disconnect()
    })
  })

  describe('Database State Reset', () => {
    it('should reset database state correctly', async () => {
      const manager = createIsolatedDatabaseTestManager()
      await manager.initializeTestDatabase()

      const factory = new (await import('../../utils/test-data-factory')).TestDataFactory()
      factory['dbManager'] = manager

      // 建立測試資料
      await factory.createProjectWithFiles(3)
      
      // 驗證資料存在
      let stats = await manager.getDatabaseStats()
      expect(stats.projectCount).toBe(1)
      expect(stats.fileCount).toBe(3)

      // 重置資料庫狀態
      await manager.forceResetDatabaseState()

      // 驗證資料已清除
      stats = await manager.getDatabaseStats()
      expect(stats.projectCount).toBe(0)
      expect(stats.fileCount).toBe(0)

      await manager.disconnect()
    })

    it('should handle force reset during active transactions', async () => {
      const manager = createIsolatedDatabaseTestManager()
      await manager.initializeTestDatabase()

      // 啟動一個長時間運行的事務
      const longTransaction = manager.withIsolatedTransaction(async (tx) => {
        await tx.project.create({
          data: { name: '長時間事務測試', description: '測試' }
        })
        
        // 模擬長時間操作
        await new Promise(resolve => setTimeout(resolve, 100))
        
        return 'long transaction completed'
      })

      // 等待事務開始
      await new Promise(resolve => setTimeout(resolve, 10))

      // 檢查隔離狀態
      const status = await manager.checkIsolationStatus()
      expect(status.activeTransactions).toBeGreaterThan(0)

      // 等待事務完成
      await longTransaction

      // 現在重置應該成功
      await manager.forceResetDatabaseState()

      const finalStats = await manager.getDatabaseStats()
      expect(finalStats.projectCount).toBe(0)
      expect(finalStats.fileCount).toBe(0)

      await manager.disconnect()
    })
  })

  describe('Isolation Status Monitoring', () => {
    it('should provide accurate isolation status information', async () => {
      const manager = createIsolatedDatabaseTestManager()
      await manager.initializeTestDatabase()

      // 檢查初始狀態
      let status = await manager.checkIsolationStatus()
      expect(status.testId).toBeDefined()
      expect(status.activeTransactions).toBe(0)
      expect(status.resourceLocks).toBe(0)
      expect(status.databaseStats.projectCount).toBe(0)
      expect(status.isolationLevel).toBe('SERIALIZABLE')

      // 執行事務並檢查狀態
      const transactionPromise = manager.withIsolatedTransaction(async (tx) => {
        // 在事務內部檢查狀態 (這需要另一個連接)
        await new Promise(resolve => setTimeout(resolve, 50))
        return 'transaction result'
      }, {
        resourceLocks: ['test-resource']
      })

      // 等待事務開始
      await new Promise(resolve => setTimeout(resolve, 10))

      // 等待事務完成
      await transactionPromise

      // 檢查最終狀態
      status = await manager.checkIsolationStatus()
      expect(status.activeTransactions).toBe(0)
      expect(status.resourceLocks).toBe(0)

      await manager.disconnect()
    })

    it('should detect isolation violations', async () => {
      const manager1 = createIsolatedDatabaseTestManager()
      const manager2 = createIsolatedDatabaseTestManager()

      await manager1.initializeTestDatabase()
      await manager2.initializeTestDatabase()

      // 建立相同的測試資料來模擬隔離違規
      const factory1 = new (await import('../../utils/test-data-factory')).TestDataFactory()
      factory1['dbManager'] = manager1
      const factory2 = new (await import('../../utils/test-data-factory')).TestDataFactory()
      factory2['dbManager'] = manager2

      await factory1.createProject({ name: '相同專案' })
      await factory2.createProject({ name: '相同專案' })

      // 驗證隔離 (應該檢測到潛在問題)
      const isolation = await manager1.validateTestIsolation(manager2)
      
      // 由於使用不同的資料庫檔案，應該仍然是隔離的
      expect(isolation.isIsolated).toBe(true)

      await manager1.disconnect()
      await manager2.disconnect()
    })
  })

  describe('Concurrent Test Execution', () => {
    it('should handle multiple concurrent isolated tests', async () => {
      const testCount = 5
      const managers = Array.from({ length: testCount }, () => createIsolatedDatabaseTestManager())

      // 並發執行測試
      const testResults = await Promise.all(
        managers.map(async (manager, index) => {
          await manager.initializeTestDatabase()
          
          const factory = new (await import('../../utils/test-data-factory')).TestDataFactory()
          factory['dbManager'] = manager

          // 每個測試建立不同數量的資料
          const { project, files } = await factory.createProjectWithFiles(
            index + 1,
            { name: `並發隔離測試-${index + 1}` }
          )

          const stats = await manager.getDatabaseStats()
          
          return {
            testId: manager.getTestId(),
            projectName: project.name,
            fileCount: files.length,
            stats
          }
        })
      )

      // 驗證每個測試都有正確的隔離資料
      testResults.forEach((result, index) => {
        expect(result.projectName).toBe(`並發隔離測試-${index + 1}`)
        expect(result.fileCount).toBe(index + 1)
        expect(result.stats.projectCount).toBe(1)
        expect(result.stats.fileCount).toBe(index + 1)
      })

      // 驗證所有測試 ID 都不同
      const testIds = testResults.map(r => r.testId)
      const uniqueTestIds = new Set(testIds)
      expect(uniqueTestIds.size).toBe(testCount)

      // 清理
      await Promise.all(managers.map(m => m.disconnect()))
    })

    it('should maintain isolation during stress testing', async () => {
      const stressTestCount = 10
      const operationsPerTest = 5

      const stressTests = Array.from({ length: stressTestCount }, async (_, testIndex) => {
        const manager = createIsolatedDatabaseTestManager()
        await manager.initializeTestDatabase()

        const factory = new (await import('../../utils/test-data-factory')).TestDataFactory()
        factory['dbManager'] = manager

        // 每個測試執行多個操作
        const operations = Array.from({ length: operationsPerTest }, async (_, opIndex) => {
          return await manager.withIsolatedTransaction(async (tx) => {
            const project = await tx.project.create({
              data: {
                name: `壓力測試-${testIndex}-${opIndex}`,
                description: `測試 ${testIndex} 操作 ${opIndex}`
              }
            })

            const file = await tx.file.create({
              data: {
                name: `stress-${testIndex}-${opIndex}.vue`,
                content: `<template><div>壓力測試 ${testIndex}-${opIndex}</div></template>`,
                projectId: project.id
              }
            })

            return { project, file }
          }, {
            resourceLocks: [`stress-resource-${testIndex}`]
          })
        })

        const results = await Promise.all(operations)
        const stats = await manager.getDatabaseStats()

        await manager.disconnect()

        return {
          testIndex,
          results,
          stats
        }
      })

      const allResults = await Promise.all(stressTests)

      // 驗證每個測試都有正確的資料量
      allResults.forEach((testResult, index) => {
        expect(testResult.testIndex).toBe(index)
        expect(testResult.results).toHaveLength(operationsPerTest)
        expect(testResult.stats.projectCount).toBe(operationsPerTest)
        expect(testResult.stats.fileCount).toBe(operationsPerTest)
      })
    })
  })

  describe('Isolation Environment Management', () => {
    it('should create and cleanup isolated test environments', async () => {
      const manager = createIsolatedDatabaseTestManager()
      
      // 建立隔離環境
      const { cleanup, testId } = await manager.createIsolatedTestEnvironment()
      
      expect(testId).toBeDefined()
      
      const factory = new (await import('../../utils/test-data-factory')).TestDataFactory()
      factory['dbManager'] = manager

      // 在隔離環境中建立資料
      await factory.createProjectWithFiles(2)
      
      // 驗證資料存在
      let stats = await manager.getDatabaseStats()
      expect(stats.projectCount).toBe(1)
      expect(stats.fileCount).toBe(2)
      
      // 清理隔離環境
      await cleanup()
      
      // 驗證清理完成 (這裡我們無法直接檢查，因為連接已斷開)
      // 但可以檢查管理器狀態
      expect(manager.checkConnection()).rejects.toThrow()
    })

    it('should handle cleanup failures gracefully', async () => {
      const manager = createIsolatedDatabaseTestManager()
      await manager.initializeTestDatabase()
      
      // 故意關閉連接來模擬清理失敗
      await manager.disconnect()
      
      // 嘗試強制重置應該處理錯誤
      await expect(manager.forceResetDatabaseState()).rejects.toThrow()
    })
  })
})