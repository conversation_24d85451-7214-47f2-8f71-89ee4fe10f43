import { describe, it, expect, beforeAll, afterAll, beforeEach } from 'vitest'
import { TestServerManager } from '../../utils/test-server-manager'
import { APITestClient } from '../../utils/api-test-client'
import { DatabaseTestManager } from '../../utils/database-test-manager'
import { TestDataFactory } from '../../utils/test-data-factory'

describe('Files API Routes', () => {
  let serverManager: TestServerManager
  let apiClient: APITestClient
  let databaseManager: DatabaseTestManager
  let testDataFactory: TestDataFactory
  let testProject: any

  beforeAll(async () => {
    // Initialize test infrastructure
    serverManager = new TestServerManager({
      database: { resetOnStart: true }
    })
    
    await serverManager.start()
    
    // Initialize API client
    apiClient = new APITestClient(serverManager.getApp())
    
    // Initialize database utilities
    databaseManager = new DatabaseTestManager()
    await databaseManager.initializeTestDatabase()
    testDataFactory = new TestDataFactory(databaseManager.getPrismaClient())
  })

  afterAll(async () => {
    await serverManager.stop()
    await databaseManager.cleanup()
  })

  beforeEach(async () => {
    await serverManager.reset()
    await databaseManager.resetDatabase()
    testDataFactory.reset()
    
    // Create a test project for file operations
    testProject = await testDataFactory.createProject({
      name: 'Test Project for Files'
    })
  })

  describe('GET /api/projects/:projectId/files', () => {
    it('should return empty array when no files exist', async () => {
      const response = await apiClient.get(`/api/projects/${testProject.id}/files`)
      
      apiClient.expectStatus(response, 200)
      expect(response.data).toEqual([])
    })

    it('should return all files for a project', async () => {
      // Create test files
      const files = await testDataFactory.createFiles(testProject.id, 3)
      
      const response = await apiClient.get(`/api/projects/${testProject.id}/files`)
      
      apiClient.expectStatus(response, 200)
      expect(response.data).toHaveLength(3)
      
      // Verify file structure
      response.data.forEach((file: any) => {
        apiClient.expectResponseStructure({ data: file }, {
          id: 'string',
          name: 'string',
          content: 'string',
          projectId: 'string',
          createdAt: 'string',
          updatedAt: 'string'
        })
        expect(file.projectId).toBe(testProject.id)
      })
    })

    it('should filter files by type', async () => {
      // Create files with different extensions
      await testDataFactory.createFile(testProject.id, { name: 'script.js' })
      await testDataFactory.createFile(testProject.id, { name: 'style.css' })
      await testDataFactory.createFile(testProject.id, { name: 'index.html' })
      
      const response = await apiClient.get(`/api/projects/${testProject.id}/files?type=js`)
      
      apiClient.expectStatus(response, 200)
      expect(response.data).toHaveLength(1)
      expect(response.data[0].name).toBe('script.js')
    })

    it('should return 404 for non-existent project', async () => {
      const response = await apiClient.get('/api/projects/non-existent-id/files')
      
      apiClient.expectErrorResponse(response, 404, 'Project not found')
    })

    it('should handle pagination for large file lists', async () => {
      // Create many files
      await testDataFactory.createFiles(testProject.id, 20)
      
      const response = await apiClient.get(`/api/projects/${testProject.id}/files?page=1&limit=10`)
      
      apiClient.expectStatus(response, 200)
      expect(response.data).toHaveLength(10)
    })
  })

  describe('GET /api/files/:id', () => {
    it('should return specific file by id', async () => {
      const file = await testDataFactory.createFile(testProject.id, {
        name: 'test.js',
        content: 'console.log("Hello World");'
      })
      
      const response = await apiClient.get(`/api/files/${file.id}`)
      
      apiClient.expectStatus(response, 200)
      apiClient.expectResponseStructure(response, {
        id: 'string',
        name: 'string',
        content: 'string',
        projectId: 'string',
        createdAt: 'string',
        updatedAt: 'string'
      })
      
      expect(response.data.id).toBe(file.id)
      expect(response.data.name).toBe(file.name)
      expect(response.data.content).toBe(file.content)
    })

    it('should return 404 for non-existent file', async () => {
      const response = await apiClient.get('/api/files/non-existent-id')
      
      apiClient.expectErrorResponse(response, 404, 'File not found')
    })

    it('should include project information when requested', async () => {
      const file = await testDataFactory.createFile(testProject.id)
      
      const response = await apiClient.get(`/api/files/${file.id}?include=project`)
      
      apiClient.expectStatus(response, 200)
      expect(response.data.project).toBeDefined()
      expect(response.data.project.id).toBe(testProject.id)
    })

    it('should handle different file types correctly', async () => {
      const testCases = [
        { name: 'script.js', content: 'console.log("test");' },
        { name: 'style.css', content: 'body { margin: 0; }' },
        { name: 'index.html', content: '<html><body>Test</body></html>' },
        { name: 'data.json', content: '{"test": true}' },
        { name: 'README.md', content: '# Test Project' }
      ]

      for (const testCase of testCases) {
        const file = await testDataFactory.createFile(testProject.id, testCase)
        const response = await apiClient.get(`/api/files/${file.id}`)
        
        apiClient.expectStatus(response, 200)
        expect(response.data.name).toBe(testCase.name)
        expect(response.data.content).toBe(testCase.content)
      }
    })
  })

  describe('POST /api/projects/:projectId/files', () => {
    it('should create new file with valid data', async () => {
      const fileData = {
        name: 'new-file.js',
        content: 'const message = "Hello World";'
      }
      
      const response = await apiClient.post(`/api/projects/${testProject.id}/files`, fileData)
      
      apiClient.expectStatus(response, 201)
      apiClient.expectResponseStructure(response, {
        id: 'string',
        name: 'string',
        content: 'string',
        projectId: 'string',
        createdAt: 'string',
        updatedAt: 'string'
      })
      
      expect(response.data.name).toBe(fileData.name)
      expect(response.data.content).toBe(fileData.content)
      expect(response.data.projectId).toBe(testProject.id)
    })

    it('should create file with empty content', async () => {
      const fileData = {
        name: 'empty-file.txt',
        content: ''
      }
      
      const response = await apiClient.post(`/api/projects/${testProject.id}/files`, fileData)
      
      apiClient.expectStatus(response, 201)
      expect(response.data.content).toBe('')
    })

    it('should return 400 for missing required fields', async () => {
      const response = await apiClient.post(`/api/projects/${testProject.id}/files`, {
        content: 'File without name'
      })
      
      apiClient.expectErrorResponse(response, 400, 'File name is required')
    })

    it('should return 404 for non-existent project', async () => {
      const fileData = {
        name: 'test.js',
        content: 'console.log("test");'
      }
      
      const response = await apiClient.post('/api/projects/non-existent-id/files', fileData)
      
      apiClient.expectErrorResponse(response, 404, 'Project not found')
    })

    it('should validate file name format', async () => {
      const invalidNames = [
        '', // Empty name
        'file with spaces.js', // Spaces (if not allowed)
        'file/with/slashes.js', // Path separators
        '.hidden-file', // Hidden files (if not allowed)
        'file-with-very-long-name'.repeat(10) + '.js' // Very long name
      ]

      for (const invalidName of invalidNames) {
        const response = await apiClient.post(`/api/projects/${testProject.id}/files`, {
          name: invalidName,
          content: 'test content'
        })
        
        // Should return 400 for invalid names (adjust based on actual validation rules)
        if (response.status === 400) {
          apiClient.expectErrorResponse(response, 400)
        }
      }
    })

    it('should handle large file content', async () => {
      const largeContent = 'x'.repeat(100000) // 100KB content
      
      const fileData = {
        name: 'large-file.txt',
        content: largeContent
      }
      
      const response = await apiClient.post(`/api/projects/${testProject.id}/files`, fileData)
      
      // Should either succeed or fail with appropriate error
      if (response.status === 413) {
        apiClient.expectErrorResponse(response, 413, 'File too large')
      } else {
        apiClient.expectStatus(response, 201)
        expect(response.data.content).toBe(largeContent)
      }
    })

    it('should prevent duplicate file names in same project', async () => {
      const fileData = {
        name: 'duplicate.js',
        content: 'First file'
      }
      
      // Create first file
      await apiClient.post(`/api/projects/${testProject.id}/files`, fileData)
      
      // Try to create second file with same name
      const response = await apiClient.post(`/api/projects/${testProject.id}/files`, {
        ...fileData,
        content: 'Second file'
      })
      
      // Should return conflict error if duplicate names are not allowed
      if (response.status === 409) {
        apiClient.expectErrorResponse(response, 409, 'File name already exists in project')
      } else {
        // If duplicates are allowed, should succeed
        apiClient.expectStatus(response, 201)
      }
    })
  })

  describe('PUT /api/files/:id', () => {
    it('should update existing file', async () => {
      const file = await testDataFactory.createFile(testProject.id, {
        name: 'original.js',
        content: 'original content'
      })
      
      const updateData = {
        name: 'updated.js',
        content: 'updated content'
      }
      
      const response = await apiClient.put(`/api/files/${file.id}`, updateData)
      
      apiClient.expectStatus(response, 200)
      expect(response.data.name).toBe(updateData.name)
      expect(response.data.content).toBe(updateData.content)
      expect(response.data.id).toBe(file.id)
    })

    it('should update only file content', async () => {
      const file = await testDataFactory.createFile(testProject.id, {
        name: 'test.js',
        content: 'original content'
      })
      
      const updateData = {
        content: 'new content only'
      }
      
      const response = await apiClient.put(`/api/files/${file.id}`, updateData)
      
      apiClient.expectStatus(response, 200)
      expect(response.data.content).toBe(updateData.content)
      expect(response.data.name).toBe(file.name) // Should remain unchanged
    })

    it('should return 404 for non-existent file', async () => {
      const updateData = {
        content: 'updated content'
      }
      
      const response = await apiClient.put('/api/files/non-existent-id', updateData)
      
      apiClient.expectErrorResponse(response, 404, 'File not found')
    })

    it('should validate update data', async () => {
      const file = await testDataFactory.createFile(testProject.id)
      
      const invalidData = {
        name: '', // Empty name
        content: 'Valid content'
      }
      
      const response = await apiClient.put(`/api/files/${file.id}`, invalidData)
      
      apiClient.expectErrorResponse(response, 400, 'File name cannot be empty')
    })

    it('should handle concurrent file updates', async () => {
      const file = await testDataFactory.createFile(testProject.id)
      
      // Simulate concurrent updates
      const update1Promise = apiClient.put(`/api/files/${file.id}`, {
        content: 'Update 1'
      })
      
      const update2Promise = apiClient.put(`/api/files/${file.id}`, {
        content: 'Update 2'
      })
      
      const [response1, response2] = await Promise.all([update1Promise, update2Promise])
      
      // Both should succeed (last write wins) or one should fail with conflict
      expect([200, 409]).toContain(response1.status)
      expect([200, 409]).toContain(response2.status)
    })

    it('should preserve file metadata during updates', async () => {
      const file = await testDataFactory.createFile(testProject.id)
      const originalCreatedAt = file.createdAt
      
      // Wait a moment to ensure updatedAt will be different
      await new Promise(resolve => setTimeout(resolve, 100))
      
      const response = await apiClient.put(`/api/files/${file.id}`, {
        content: 'updated content'
      })
      
      apiClient.expectStatus(response, 200)
      expect(response.data.createdAt).toBe(originalCreatedAt)
      expect(new Date(response.data.updatedAt).getTime()).toBeGreaterThan(
        new Date(originalCreatedAt).getTime()
      )
    })
  })

  describe('DELETE /api/files/:id', () => {
    it('should delete existing file', async () => {
      const file = await testDataFactory.createFile(testProject.id)
      
      const response = await apiClient.delete(`/api/files/${file.id}`)
      
      apiClient.expectStatus(response, 204)
      
      // Verify file was deleted
      const getResponse = await apiClient.get(`/api/files/${file.id}`)
      apiClient.expectStatus(getResponse, 404)
    })

    it('should return 404 for non-existent file', async () => {
      const response = await apiClient.delete('/api/files/non-existent-id')
      
      apiClient.expectErrorResponse(response, 404, 'File not found')
    })

    it('should not affect other files in the project', async () => {
      const files = await testDataFactory.createFiles(testProject.id, 3)
      
      // Delete one file
      const response = await apiClient.delete(`/api/files/${files[0].id}`)
      apiClient.expectStatus(response, 204)
      
      // Verify other files still exist
      const remainingFilesResponse = await apiClient.get(`/api/projects/${testProject.id}/files`)
      apiClient.expectStatus(remainingFilesResponse, 200)
      expect(remainingFilesResponse.data).toHaveLength(2)
    })

    it('should handle soft delete if implemented', async () => {
      const file = await testDataFactory.createFile(testProject.id)
      
      const response = await apiClient.delete(`/api/files/${file.id}`)
      
      apiClient.expectStatus(response, 204)
      
      // If soft delete is implemented, file should still exist but be marked as deleted
      const prisma = databaseManager.getPrismaClient()
      const deletedFile = await prisma.file.findUnique({
        where: { id: file.id }
      })
      
      // Adjust this test based on whether soft delete is implemented
      if (deletedFile) {
        // Soft delete implementation
        expect(deletedFile).toHaveProperty('deletedAt')
      } else {
        // Hard delete implementation
        expect(deletedFile).toBeNull()
      }
    })
  })

  describe('File Content Operations', () => {
    it('should handle different file encodings', async () => {
      const testCases = [
        { name: 'utf8.txt', content: 'Hello 世界 🌍' },
        { name: 'ascii.txt', content: 'Hello World' },
        { name: 'special.txt', content: 'Special chars: !@#$%^&*()' }
      ]

      for (const testCase of testCases) {
        const response = await apiClient.post(`/api/projects/${testProject.id}/files`, testCase)
        
        apiClient.expectStatus(response, 201)
        expect(response.data.content).toBe(testCase.content)
      }
    })

    it('should preserve line endings in file content', async () => {
      const contentWithLineEndings = 'Line 1\nLine 2\r\nLine 3\n'
      
      const response = await apiClient.post(`/api/projects/${testProject.id}/files`, {
        name: 'line-endings.txt',
        content: contentWithLineEndings
      })
      
      apiClient.expectStatus(response, 201)
      expect(response.data.content).toBe(contentWithLineEndings)
    })

    it('should handle binary-like content', async () => {
      // Simulate binary content as base64 string
      const binaryContent = Buffer.from('Binary data content').toString('base64')
      
      const response = await apiClient.post(`/api/projects/${testProject.id}/files`, {
        name: 'binary.dat',
        content: binaryContent
      })
      
      apiClient.expectStatus(response, 201)
      expect(response.data.content).toBe(binaryContent)
    })
  })

  describe('File Search and Filtering', () => {
    beforeEach(async () => {
      // Create files with different names and content for search testing
      await testDataFactory.createFile(testProject.id, {
        name: 'component.js',
        content: 'React component code'
      })
      await testDataFactory.createFile(testProject.id, {
        name: 'utils.js',
        content: 'Utility functions'
      })
      await testDataFactory.createFile(testProject.id, {
        name: 'style.css',
        content: 'CSS styles'
      })
    })

    it('should search files by name', async () => {
      const response = await apiClient.get(`/api/projects/${testProject.id}/files?search=component`)
      
      apiClient.expectStatus(response, 200)
      expect(response.data).toHaveLength(1)
      expect(response.data[0].name).toBe('component.js')
    })

    it('should search files by content', async () => {
      const response = await apiClient.get(`/api/projects/${testProject.id}/files?content=React`)
      
      apiClient.expectStatus(response, 200)
      expect(response.data).toHaveLength(1)
      expect(response.data[0].content).toContain('React')
    })

    it('should filter files by extension', async () => {
      const response = await apiClient.get(`/api/projects/${testProject.id}/files?extension=js`)
      
      apiClient.expectStatus(response, 200)
      expect(response.data).toHaveLength(2)
      response.data.forEach((file: any) => {
        expect(file.name).toMatch(/\.js$/)
      })
    })
  })

  describe('Error Handling and Edge Cases', () => {
    it('should handle database connection errors', async () => {
      // Simulate database connection error
      await databaseManager.cleanup()
      
      const response = await apiClient.get(`/api/projects/${testProject.id}/files`)
      
      apiClient.expectErrorResponse(response, 500, 'Database connection error')
      
      // Restore database connection
      await databaseManager.initializeTestDatabase()
    })

    it('should handle malformed file data', async () => {
      const response = await apiClient.post(`/api/projects/${testProject.id}/files`, {
        name: 123, // Invalid type
        content: ['array', 'instead', 'of', 'string'] // Invalid type
      })
      
      apiClient.expectErrorResponse(response, 400)
    })

    it('should handle extremely long file names', async () => {
      const longName = 'a'.repeat(1000) + '.txt'
      
      const response = await apiClient.post(`/api/projects/${testProject.id}/files`, {
        name: longName,
        content: 'test content'
      })
      
      // Should return appropriate error for name too long
      if (response.status === 400) {
        apiClient.expectErrorResponse(response, 400, 'File name too long')
      }
    })

    it('should handle null and undefined values', async () => {
      const testCases = [
        { name: null, content: 'test' },
        { name: 'test.txt', content: null },
        { name: undefined, content: 'test' },
        { name: 'test.txt', content: undefined }
      ]

      for (const testCase of testCases) {
        const response = await apiClient.post(`/api/projects/${testProject.id}/files`, testCase)
        
        // Should return 400 for null/undefined required fields
        apiClient.expectErrorResponse(response, 400)
      }
    })
  })

  describe('Performance Testing', () => {
    it('should handle multiple file operations concurrently', async () => {
      const operations = []
      
      // Create multiple files concurrently
      for (let i = 0; i < 10; i++) {
        operations.push(
          apiClient.post(`/api/projects/${testProject.id}/files`, {
            name: `concurrent-${i}.js`,
            content: `console.log("File ${i}");`
          })
        )
      }
      
      const startTime = Date.now()
      const responses = await Promise.all(operations)
      const endTime = Date.now()
      
      // All operations should succeed
      responses.forEach(response => {
        apiClient.expectStatus(response, 201)
      })
      
      // Should complete within reasonable time
      expect(endTime - startTime).toBeLessThan(5000) // 5 seconds
    })

    it('should efficiently retrieve large numbers of files', async () => {
      // Create many files
      await testDataFactory.createFiles(testProject.id, 50)
      
      const startTime = Date.now()
      const response = await apiClient.get(`/api/projects/${testProject.id}/files`)
      const endTime = Date.now()
      
      apiClient.expectStatus(response, 200)
      expect(response.data).toHaveLength(50)
      expect(endTime - startTime).toBeLessThan(3000) // 3 seconds
    })
  })
})