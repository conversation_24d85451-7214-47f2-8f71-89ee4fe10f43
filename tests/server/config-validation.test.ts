import { describe, it, expect } from 'vitest'
import { resolve } from 'path'
import { existsSync } from 'fs'

describe('Test Configuration Validation', () => {
  describe('Configuration Files', () => {
    it('should have all required vitest config files', () => {
      const configFiles = [
        'tests/config/vitest.config.frontend.ts',
        'tests/config/vitest.config.server.ts', 
        'tests/config/vitest.config.integration.ts'
      ]
      
      configFiles.forEach(file => {
        expect(existsSync(resolve(file))).toBe(true)
      })
    })

    it('should have all required setup files', () => {
      const setupFiles = [
        'tests/setup/frontend-setup.ts',
        'tests/setup/server-setup.ts',
        'tests/setup/integration-setup.ts'
      ]
      
      setupFiles.forEach(file => {
        expect(existsSync(resolve(file))).toBe(true)
      })
    })

    it('should have TypeScript configuration files', () => {
      const tsConfigFiles = [
        'tsconfig.json',
        'tsconfig.app.json',
        'tsconfig.node.json',
        'tsconfig.test.json',
        'tsconfig.server.json'
      ]
      
      tsConfigFiles.forEach(file => {
        expect(existsSync(resolve(file))).toBe(true)
      })
    })
  })

  describe('Environment Variables', () => {
    it('should have test environment configured', () => {
      expect(process.env.NODE_ENV).toBe('test')
    })

    it('should have database URL configured', () => {
      expect(process.env.DATABASE_URL).toBeDefined()
      expect(process.env.DATABASE_URL).toContain('test.db')
    })

    it('should have test-specific settings', () => {
      expect(process.env.TEST_TIMEOUT).toBeDefined()
      expect(process.env.MOCK_AI_ENABLED).toBe('true')
    })
  })

  describe('Module Resolution', () => {
    it('should resolve path aliases correctly', () => {
      // Test that our path aliases work
      const aliases = ['@', '@shared', '@server']
      
      aliases.forEach(alias => {
        // This test will fail if module resolution is broken
        expect(() => {
          // Just test that the alias is recognized by TypeScript
          const path = alias + '/test'
          expect(typeof path).toBe('string')
        }).not.toThrow()
      })
    })
  })

  describe('Global Test Setup', () => {
    it('should have vitest globals available', () => {
      expect(typeof describe).toBe('function')
      expect(typeof it).toBe('function') 
      expect(typeof expect).toBe('function')
      expect(typeof beforeEach).toBe('function')
      expect(typeof afterEach).toBe('function')
    })

    it('should be running in Node.js environment for server tests', () => {
      expect(typeof process).toBe('object')
      expect(typeof global).toBe('object')
      expect(typeof require).toBe('function')
    })

    it('should not have browser globals in server environment', () => {
      expect(typeof window).toBe('undefined')
      expect(typeof document).toBe('undefined')
      // Note: navigator might be polyfilled by Node.js in some environments
      // The important thing is that window and document are undefined
    })
  })
})