import { defineConfig } from 'vitest/config'
import { resolve } from 'path'

export default defineConfig({
  test: {
    globals: true,
    environment: 'node',
    setupFiles: ['./tests/setup/server-setup.ts'],
    include: [
      'tests/server/**/*.test.ts',
      'server/**/*.test.ts'
    ],
    testTimeout: 10000,
    hookTimeout: 10000,
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, '../../src'),
      '@shared': resolve(__dirname, '../../shared'),
      '@server': resolve(__dirname, '../../server'),
    },
  },
  esbuild: {
    target: 'node18'
  }
})