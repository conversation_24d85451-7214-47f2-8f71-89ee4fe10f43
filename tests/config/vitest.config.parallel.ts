import { defineConfig } from 'vitest/config'
import { resolve } from 'path'
import vue from '@vitejs/plugin-vue'

/**
 * 並行測試執行的 Vitest 配置
 * 針對高效能並行測試執行優化的配置
 */
export default defineConfig({
  plugins: [vue()],
  test: {
    globals: true,
    environment: 'jsdom', // 預設環境，會被 environmentMatchGlobs 覆蓋
    setupFiles: ['./tests/setup/parallel-setup.ts'],
    include: [
      'src/**/*.test.ts',
      'tests/unit/**/*.test.ts',
      'tests/server/**/*.test.ts',
      'tests/integration/**/*.test.ts',
      'server/**/*.test.ts'
    ],
    testTimeout: 15000,
    hookTimeout: 15000,
    // 使用環境覆蓋來區分不同類型的測試
    environmentMatchGlobs: [
      ['tests/server/**', 'node'],
      ['tests/integration/**', 'node'], 
      ['server/**/*.test.ts', 'node'],
      ['src/**', 'jsdom'],
      ['tests/unit/**', 'jsdom'],
    ],
    // 啟用並行執行
    pool: 'forks',
    poolOptions: {
      forks: {
        // 根據 CPU 核心數量自動調整並行數量
        maxForks: Math.max(2, Math.floor(require('os').cpus().length * 0.75)),
        isolate: true,
      }
    },
    // 啟用 JUnit 報告輸出
    reporters: ['default', 'junit'],
    outputFile: {
      junit: './test-results/junit.xml'
    },
    // 啟用覆蓋率報告
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html', 'lcov'],
      reportsDirectory: './coverage',
    }
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, '../../src'),
      '@shared': resolve(__dirname, '../../shared'),
      '@server': resolve(__dirname, '../../server'),
    },
  },
  esbuild: {
    target: 'node18'
  }
})