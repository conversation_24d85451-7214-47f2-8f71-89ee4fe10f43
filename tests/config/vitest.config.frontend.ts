import { defineConfig } from 'vitest/config'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue()],
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: ['./tests/setup/frontend-setup.ts'],
    include: [
      'src/**/*.test.ts',
      'tests/unit/**/*.test.ts'
    ],
    testTimeout: 10000,
    hookTimeout: 10000,
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, '../../src'),
      '@shared': resolve(__dirname, '../../shared'),
      '@server': resolve(__dirname, '../../server'),
    },
  },
})