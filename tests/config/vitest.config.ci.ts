import { defineConfig } from 'vitest/config'
import { resolve } from 'path'
import vue from '@vitejs/plugin-vue'

/**
 * CI 環境專用的 Vitest 配置
 * 針對 CI 環境優化的測試配置，支援並行執行和標準化報告輸出
 */
export default defineConfig({
  plugins: [vue()],
  test: {
    globals: true,
    environment: 'jsdom', // 預設環境，會被 environmentMatchGlobs 覆蓋
    setupFiles: ['./tests/setup/ci-setup.ts'],
    include: [
      'src/**/*.test.ts',
      'tests/unit/**/*.test.ts',
      'tests/server/**/*.test.ts',
      'tests/integration/**/*.test.ts',
      'server/**/*.test.ts'
    ],
    testTimeout: 15000, // CI 環境可能較慢，增加超時時間
    hookTimeout: 15000,
    // 使用環境覆蓋來區分不同類型的測試
    environmentMatchGlobs: [
      ['tests/server/**', 'node'],
      ['tests/integration/**', 'node'], 
      ['server/**/*.test.ts', 'node'],
      ['src/**', 'jsdom'],
      ['tests/unit/**', 'jsdom'],
    ],
    // 啟用並行執行
    pool: 'forks',
    poolOptions: {
      forks: {
        // 根據 CPU 核心數量自動調整並行數量
        maxForks: Math.max(1, Math.floor(require('os').cpus().length * 0.75)),
        isolate: true,
      }
    },
    // 啟用 JUnit 報告輸出，適合 CI 系統整合
    reporters: ['default', 'junit'],
    outputFile: {
      junit: './test-results/junit.xml'
    },
    // 啟用覆蓋率報告
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html', 'lcov'],
      reportsDirectory: './coverage',
      // 設置覆蓋率閾值
      thresholds: {
        lines: 70,
        functions: 70,
        branches: 60,
        statements: 70
      }
    }
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, '../../src'),
      '@shared': resolve(__dirname, '../../shared'),
      '@server': resolve(__dirname, '../../server'),
    },
  },
  esbuild: {
    target: 'node18'
  }
})