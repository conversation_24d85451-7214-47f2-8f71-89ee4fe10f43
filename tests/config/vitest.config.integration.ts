import { defineConfig } from 'vitest/config'
import { resolve } from 'path'

export default defineConfig({
  test: {
    globals: true,
    environment: 'node',
    setupFiles: ['./tests/setup/integration-setup.ts'],
    include: [
      'tests/integration/**/*.test.ts'
    ],
    testTimeout: 30000,
    hookTimeout: 10000,
    // Enable sequential execution for integration tests to avoid resource conflicts
    pool: 'forks',
    poolOptions: {
      forks: {
        singleFork: true
      }
    }
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, '../../src'),
      '@shared': resolve(__dirname, '../../shared'),
      '@server': resolve(__dirname, '../../server'),
    },
  },
  esbuild: {
    target: 'node18'
  }
})