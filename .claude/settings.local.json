{"permissions": {"allow": ["mcp__sequential-thinking__sequentialthinking", "mcp__playwright", "mcp__time", "mcp__fetch", "mcp__serena", "mcp__context7", "Bash(npm run build)", "Bash(npm run test)", "Bash(npm run test:frontend)", "Bash(npm run test:once)", "Bash(npm run test:files)", "Bash(npm run test:once:*)", "mcp__graphiti-memory__search_memory_nodes", "mcp__graphiti-memory__search_memory_facts", "mcp__graphiti-memory__add_memory", "mcp__graphiti-memory__clear_graph"], "deny": []}, "enableAllProjectMcpServers": true, "enabledMcpjsonServers": ["context7", "fetch", "sequential-thinking", "serena", "playwright", "time", "graphiti-memory"]}