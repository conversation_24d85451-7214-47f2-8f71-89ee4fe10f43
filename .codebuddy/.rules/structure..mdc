# 專案結構

## 整體架構

UIGen Vue 採用前後端分離架構：
- **前端**: Vue 3 SPA 應用 (port 5173)
- **後端**: Express.js API 伺服器 (port 3001)
- **通訊**: RESTful API + WebSocket

## 目錄結構

```
uigen-vue/
├── src/                          # Vue 前端應用
│   ├── components/               # Vue 組件
│   │   ├── ui/                  # 基礎 UI 組件
│   │   ├── chat/                # 聊天相關組件
│   │   ├── editor/              # 程式碼編輯器組件
│   │   ├── preview/             # 預覽相關組件
│   │   └── auth/                # 認證相關組件
│   ├── stores/                  # Pinia 狀態管理
│   ├── views/                   # 頁面組件
│   ├── router/                  # Vue Router 配置
│   ├── lib/                     # 工具函數與類別
│   ├── composables/             # Vue 組合式函數
│   └── types/                   # TypeScript 類型定義
├── server/                      # Express.js 後端
│   ├── routes/                  # API 路由
│   ├── middleware/              # 中介軟體
│   ├── lib/                     # 後端工具函數
│   │   ├── ai/                  # AI 提供者管理
│   │   └── tools/               # AI 工具
│   ├── models/                  # 資料模型
│   └── prisma/                  # 資料庫配置
├── shared/                      # 前後端共用類型
├── tests/                       # 測試檔案
│   ├── unit/                    # 單元測試
│   ├── integration/             # 整合測試
│   └── e2e/                     # 端對端測試
└── prisma/                      # Prisma 資料庫配置
```

## 核心檔案說明

### 前端核心檔案
- `src/App.vue` - 根組件
- `src/main.ts` - 應用程式入口點
- `src/lib/file-system.ts` - 虛擬檔案系統
- `src/lib/api.ts` - API 客戶端
- `src/stores/` - Pinia 狀態管理

### 後端核心檔案
- `server/index.ts` - Express 伺服器入口點
- `server/lib/ai/provider-manager.ts` - AI 提供者管理
- `server/routes/` - API 路由定義
- `prisma/schema.prisma` - 資料庫架構

### 配置檔案
- `package.json` - 專案依賴與腳本
- `vite.config.ts` - Vite 建置配置
- `tsconfig.json` - TypeScript 配置
- `.eslintrc.js` - ESLint 規則
- `.env` - 環境變數

## 命名慣例

### 檔案命名
- Vue 組件: PascalCase (例: `ChatInterface.vue`)
- TypeScript 檔案: kebab-case (例: `file-system.ts`)
- 測試檔案: `*.test.ts` 或 `*.spec.ts`

### 目錄命名
- 全部使用 kebab-case
- 複數形式用於集合 (例: `components`, `stores`)

### 變數命名
- camelCase 用於變數和函數
- PascalCase 用於類別和組件
- UPPER_SNAKE_CASE 用於常數

## 架構模式

### 1. 狀態管理 (Pinia)
- **檔案系統 Store**: 虛擬檔案系統管理
- **聊天 Store**: AI 聊天狀態與訊息
- **專案 Store**: 專案管理狀態
- **使用者 Store**: 使用者認證狀態

### 2. 組件組織
- **ui/**: 可重用的基礎組件
- **功能性目錄**: 按功能分組 (chat, editor, preview)
- **頁面組件**: 放在 `views/` 目錄

### 3. API 層級
- **RESTful API**: 標準 CRUD 操作
- **WebSocket**: 即時聊天和預覽更新
- **中介軟體**: 認證、錯誤處理、日誌

### 4. 測試結構
- **單元測試**: 測試個別組件和函數
- **整合測試**: 測試 API 端點和資料庫操作
- **端對端測試**: 測試完整使用者流程

## 開發工作流程

### 新增功能
1. 更新資料庫架構 (`prisma/schema.prisma`)
2. 建立 API 端點 (`server/routes/`)
3. 更新 Pinia store (`src/stores/`)
4. 建立 Vue 組件 (`src/components/`)
5. 新增測試檔案
6. 更新路由配置

### 組件開發
- 使用 Vue 3 Composition API
- 採用 `<script setup>` 語法
- 遵循單一職責原則
- 為每個組件編寫測試

### API 開發
- 遵循 RESTful 設計原則
- 使用適當的 HTTP 狀態碼
- 實現錯誤處理中介軟體
- 新增 API 文件 (Swagger)