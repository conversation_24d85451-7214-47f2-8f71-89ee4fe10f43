{"config": {"configFile": "/Users/<USER>/Projects/uigen-vue/playwright.config.ts", "rootDir": "/Users/<USER>/Projects/uigen-vue/tests/e2e", "forbidOnly": false, "fullyParallel": true, "globalSetup": "/Users/<USER>/Projects/uigen-vue/tests/e2e/global-setup.ts", "globalTeardown": "/Users/<USER>/Projects/uigen-vue/tests/e2e/global-teardown.ts", "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "playwright-report"}], ["json", {"outputFile": "test-results/results.json"}], ["junit", {"outputFile": "test-results/results.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "/Users/<USER>/Projects/uigen-vue/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "chromium", "name": "chromium", "testDir": "/Users/<USER>/Projects/uigen-vue/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Projects/uigen-vue/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "firefox", "name": "firefox", "testDir": "/Users/<USER>/Projects/uigen-vue/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Projects/uigen-vue/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "webkit", "name": "webkit", "testDir": "/Users/<USER>/Projects/uigen-vue/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Projects/uigen-vue/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "/Users/<USER>/Projects/uigen-vue/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Projects/uigen-vue/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "/Users/<USER>/Projects/uigen-vue/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.54.1", "workers": 5, "webServer": null}, "suites": [{"title": "user-workflows/chat-with-ai.spec.ts", "file": "user-workflows/chat-with-ai.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "AI 聊天功能測試", "file": "user-workflows/chat-with-ai.spec.ts", "line": 7, "column": 6, "specs": [{"title": "基本聊天功能", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "2f82157bb48bae10481f-69af27455272cbf43bba", "file": "user-workflows/chat-with-ai.spec.ts", "line": 31, "column": 3}, {"title": "複雜組件生成", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "2f82157bb48bae10481f-28b9ba420cfb02ac78b2", "file": "user-workflows/chat-with-ai.spec.ts", "line": 50, "column": 3}, {"title": "程式碼修改和優化", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "2f82157bb48bae10481f-f2e0a5d347015f07f5ed", "file": "user-workflows/chat-with-ai.spec.ts", "line": 88, "column": 3}, {"title": "錯誤處理和修正", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "2f82157bb48bae10481f-fcdb2dfa548d362ea9dc", "file": "user-workflows/chat-with-ai.spec.ts", "line": 119, "column": 3}, {"title": "多語言和國際化支援", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "2f82157bb48bae10481f-9277957cb35cee0ec1fe", "file": "user-workflows/chat-with-ai.spec.ts", "line": 147, "column": 3}, {"title": "響應式設計請求", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "2f82157bb48bae10481f-1277c2d0df8e73dede80", "file": "user-workflows/chat-with-ai.spec.ts", "line": 174, "column": 3}, {"title": "API 整合組件", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "2f82157bb48bae10481f-48c666ddef314c418992", "file": "user-workflows/chat-with-ai.spec.ts", "line": 200, "column": 3}, {"title": "動畫和過渡效果", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "2f82157bb48bae10481f-cdc968b21053517ef7fc", "file": "user-workflows/chat-with-ai.spec.ts", "line": 224, "column": 3}, {"title": "聊天歷史和上下文", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "2f82157bb48bae10481f-34814087af6eb2114d16", "file": "user-workflows/chat-with-ai.spec.ts", "line": 249, "column": 3}, {"title": "效能優化建議", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "2f82157bb48bae10481f-2f26c58ff4945a889fbe", "file": "user-workflows/chat-with-ai.spec.ts", "line": 278, "column": 3}, {"title": "無障礙功能支援", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "2f82157bb48bae10481f-698fc86a50f151a391e2", "file": "user-workflows/chat-with-ai.spec.ts", "line": 301, "column": 3}, {"title": "基本聊天功能", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "2f82157bb48bae10481f-ec5d56e920977945f2c8", "file": "user-workflows/chat-with-ai.spec.ts", "line": 31, "column": 3}, {"title": "複雜組件生成", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "2f82157bb48bae10481f-1d30a1e633ccefff563d", "file": "user-workflows/chat-with-ai.spec.ts", "line": 50, "column": 3}, {"title": "程式碼修改和優化", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "2f82157bb48bae10481f-6fca4ad29e61df5d6cf8", "file": "user-workflows/chat-with-ai.spec.ts", "line": 88, "column": 3}, {"title": "錯誤處理和修正", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "2f82157bb48bae10481f-f5ae50148c9410cc71dc", "file": "user-workflows/chat-with-ai.spec.ts", "line": 119, "column": 3}, {"title": "多語言和國際化支援", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "2f82157bb48bae10481f-7c507f6f5e9c00bbd2d0", "file": "user-workflows/chat-with-ai.spec.ts", "line": 147, "column": 3}, {"title": "響應式設計請求", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "2f82157bb48bae10481f-079b9f24d47bb464a73e", "file": "user-workflows/chat-with-ai.spec.ts", "line": 174, "column": 3}, {"title": "API 整合組件", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "2f82157bb48bae10481f-19c0e7c2b6e47230a9d6", "file": "user-workflows/chat-with-ai.spec.ts", "line": 200, "column": 3}, {"title": "動畫和過渡效果", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "2f82157bb48bae10481f-77f281342edd27d1783d", "file": "user-workflows/chat-with-ai.spec.ts", "line": 224, "column": 3}, {"title": "聊天歷史和上下文", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "2f82157bb48bae10481f-32419305f38148c67577", "file": "user-workflows/chat-with-ai.spec.ts", "line": 249, "column": 3}, {"title": "效能優化建議", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "2f82157bb48bae10481f-e23dc36a05fcfe975ab8", "file": "user-workflows/chat-with-ai.spec.ts", "line": 278, "column": 3}, {"title": "無障礙功能支援", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "2f82157bb48bae10481f-201910b89e7a0299842c", "file": "user-workflows/chat-with-ai.spec.ts", "line": 301, "column": 3}, {"title": "基本聊天功能", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "2f82157bb48bae10481f-9b77ab60e61d76fd0aca", "file": "user-workflows/chat-with-ai.spec.ts", "line": 31, "column": 3}, {"title": "複雜組件生成", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "2f82157bb48bae10481f-09535ad7c678472dc4a4", "file": "user-workflows/chat-with-ai.spec.ts", "line": 50, "column": 3}, {"title": "程式碼修改和優化", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "2f82157bb48bae10481f-c32f7d05cdf15e00ff06", "file": "user-workflows/chat-with-ai.spec.ts", "line": 88, "column": 3}, {"title": "錯誤處理和修正", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "2f82157bb48bae10481f-c11cfdf823d07c212453", "file": "user-workflows/chat-with-ai.spec.ts", "line": 119, "column": 3}, {"title": "多語言和國際化支援", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "2f82157bb48bae10481f-5944878d5e2c4e3142d9", "file": "user-workflows/chat-with-ai.spec.ts", "line": 147, "column": 3}, {"title": "響應式設計請求", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "2f82157bb48bae10481f-f78548eae8f7ba261fad", "file": "user-workflows/chat-with-ai.spec.ts", "line": 174, "column": 3}, {"title": "API 整合組件", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "2f82157bb48bae10481f-dde6ab7a63c5f58e01a1", "file": "user-workflows/chat-with-ai.spec.ts", "line": 200, "column": 3}, {"title": "動畫和過渡效果", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "2f82157bb48bae10481f-4e364f8ea3cd83b9904b", "file": "user-workflows/chat-with-ai.spec.ts", "line": 224, "column": 3}, {"title": "聊天歷史和上下文", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "2f82157bb48bae10481f-3cf4e350ac69c041c8ba", "file": "user-workflows/chat-with-ai.spec.ts", "line": 249, "column": 3}, {"title": "效能優化建議", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "2f82157bb48bae10481f-53967c4edb77964902d0", "file": "user-workflows/chat-with-ai.spec.ts", "line": 278, "column": 3}, {"title": "無障礙功能支援", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "2f82157bb48bae10481f-4b778ebb1d6f19a9aee3", "file": "user-workflows/chat-with-ai.spec.ts", "line": 301, "column": 3}, {"title": "基本聊天功能", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "2f82157bb48bae10481f-49f3f222ed61b02452a5", "file": "user-workflows/chat-with-ai.spec.ts", "line": 31, "column": 3}, {"title": "複雜組件生成", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "2f82157bb48bae10481f-aca43c1cd5aba24b247e", "file": "user-workflows/chat-with-ai.spec.ts", "line": 50, "column": 3}, {"title": "程式碼修改和優化", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "2f82157bb48bae10481f-249d617b09ce835d0895", "file": "user-workflows/chat-with-ai.spec.ts", "line": 88, "column": 3}, {"title": "錯誤處理和修正", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "2f82157bb48bae10481f-8d7ab9c1d10489156601", "file": "user-workflows/chat-with-ai.spec.ts", "line": 119, "column": 3}, {"title": "多語言和國際化支援", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "2f82157bb48bae10481f-35681fb2fcd2b5f46b9d", "file": "user-workflows/chat-with-ai.spec.ts", "line": 147, "column": 3}, {"title": "響應式設計請求", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "2f82157bb48bae10481f-d26708e6a6154e7e34ad", "file": "user-workflows/chat-with-ai.spec.ts", "line": 174, "column": 3}, {"title": "API 整合組件", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "2f82157bb48bae10481f-a9e6ad4518cf9ef6acb9", "file": "user-workflows/chat-with-ai.spec.ts", "line": 200, "column": 3}, {"title": "動畫和過渡效果", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "2f82157bb48bae10481f-54fd4ca25955e7a16eba", "file": "user-workflows/chat-with-ai.spec.ts", "line": 224, "column": 3}, {"title": "聊天歷史和上下文", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "2f82157bb48bae10481f-0d2ae6cf12aaa97ca750", "file": "user-workflows/chat-with-ai.spec.ts", "line": 249, "column": 3}, {"title": "效能優化建議", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "2f82157bb48bae10481f-44ea184a94d79ba002fa", "file": "user-workflows/chat-with-ai.spec.ts", "line": 278, "column": 3}, {"title": "無障礙功能支援", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "2f82157bb48bae10481f-150be9d8094c2092a82e", "file": "user-workflows/chat-with-ai.spec.ts", "line": 301, "column": 3}, {"title": "基本聊天功能", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "2f82157bb48bae10481f-4cbb25161de95e7173a0", "file": "user-workflows/chat-with-ai.spec.ts", "line": 31, "column": 3}, {"title": "複雜組件生成", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "2f82157bb48bae10481f-dc0077513c23042221f8", "file": "user-workflows/chat-with-ai.spec.ts", "line": 50, "column": 3}, {"title": "程式碼修改和優化", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "2f82157bb48bae10481f-fe63c43e396adf14dda8", "file": "user-workflows/chat-with-ai.spec.ts", "line": 88, "column": 3}, {"title": "錯誤處理和修正", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "2f82157bb48bae10481f-51ededeff1f715fe62e6", "file": "user-workflows/chat-with-ai.spec.ts", "line": 119, "column": 3}, {"title": "多語言和國際化支援", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "2f82157bb48bae10481f-129e3cda7ea8fb641c3c", "file": "user-workflows/chat-with-ai.spec.ts", "line": 147, "column": 3}, {"title": "響應式設計請求", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "2f82157bb48bae10481f-d6e8c5e286f671040290", "file": "user-workflows/chat-with-ai.spec.ts", "line": 174, "column": 3}, {"title": "API 整合組件", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "2f82157bb48bae10481f-79d70704580d0050de85", "file": "user-workflows/chat-with-ai.spec.ts", "line": 200, "column": 3}, {"title": "動畫和過渡效果", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "2f82157bb48bae10481f-ced9b6b8a88c0254bda9", "file": "user-workflows/chat-with-ai.spec.ts", "line": 224, "column": 3}, {"title": "聊天歷史和上下文", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "2f82157bb48bae10481f-b11a941b457a404d854e", "file": "user-workflows/chat-with-ai.spec.ts", "line": 249, "column": 3}, {"title": "效能優化建議", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "2f82157bb48bae10481f-a322cdad44c512881b56", "file": "user-workflows/chat-with-ai.spec.ts", "line": 278, "column": 3}, {"title": "無障礙功能支援", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "2f82157bb48bae10481f-bb33fc828522c304686f", "file": "user-workflows/chat-with-ai.spec.ts", "line": 301, "column": 3}]}]}, {"title": "user-workflows/complete-user-journey.spec.ts", "file": "user-workflows/complete-user-journey.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "完整使用者場景測試", "file": "user-workflows/complete-user-journey.spec.ts", "line": 7, "column": 6, "specs": [{"title": "完整工作流程：專案建立 → 聊天生成組件 → 編輯程式碼 → 預覽結果", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "36db533b87da1986570b-90ba269ea15ac25de76a", "file": "user-workflows/complete-user-journey.spec.ts", "line": 38, "column": 3}, {"title": "錯誤處理和恢復流程", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "36db533b87da1986570b-ea154ed7aef2bce5625c", "file": "user-workflows/complete-user-journey.spec.ts", "line": 183, "column": 3}, {"title": "多檔案專案管理流程", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "36db533b87da1986570b-d152901a80a87ba086a7", "file": "user-workflows/complete-user-journey.spec.ts", "line": 255, "column": 3}, {"title": "AI 聊天進階功能測試", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "36db533b87da1986570b-c2d304065c1a4844adcc", "file": "user-workflows/complete-user-journey.spec.ts", "line": 350, "column": 3}, {"title": "效能基準測試", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "36db533b87da1986570b-926fa9fab4c543f38c32", "file": "user-workflows/complete-user-journey.spec.ts", "line": 380, "column": 3}, {"title": "跨瀏覽器相容性基本測試", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "36db533b87da1986570b-7c4e2edfc7864a8261f9", "file": "user-workflows/complete-user-journey.spec.ts", "line": 428, "column": 3}, {"title": "完整工作流程：專案建立 → 聊天生成組件 → 編輯程式碼 → 預覽結果", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "36db533b87da1986570b-42ca8004d0c6d7d0a862", "file": "user-workflows/complete-user-journey.spec.ts", "line": 38, "column": 3}, {"title": "錯誤處理和恢復流程", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "36db533b87da1986570b-ac28c79e2c0681c07bba", "file": "user-workflows/complete-user-journey.spec.ts", "line": 183, "column": 3}, {"title": "多檔案專案管理流程", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "36db533b87da1986570b-0b7d9574def47f37e77c", "file": "user-workflows/complete-user-journey.spec.ts", "line": 255, "column": 3}, {"title": "AI 聊天進階功能測試", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "36db533b87da1986570b-aa2d22e67183fe05267b", "file": "user-workflows/complete-user-journey.spec.ts", "line": 350, "column": 3}, {"title": "效能基準測試", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "36db533b87da1986570b-476ad034ac0e94ae792e", "file": "user-workflows/complete-user-journey.spec.ts", "line": 380, "column": 3}, {"title": "跨瀏覽器相容性基本測試", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "36db533b87da1986570b-a7e7e43d8a4ce1869849", "file": "user-workflows/complete-user-journey.spec.ts", "line": 428, "column": 3}, {"title": "完整工作流程：專案建立 → 聊天生成組件 → 編輯程式碼 → 預覽結果", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "36db533b87da1986570b-374591402ae5da211040", "file": "user-workflows/complete-user-journey.spec.ts", "line": 38, "column": 3}, {"title": "錯誤處理和恢復流程", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "36db533b87da1986570b-fea9b7c72050f9a04fab", "file": "user-workflows/complete-user-journey.spec.ts", "line": 183, "column": 3}, {"title": "多檔案專案管理流程", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "36db533b87da1986570b-68ec0996a60539e27e0f", "file": "user-workflows/complete-user-journey.spec.ts", "line": 255, "column": 3}, {"title": "AI 聊天進階功能測試", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "36db533b87da1986570b-4c15d5e2313b191b45db", "file": "user-workflows/complete-user-journey.spec.ts", "line": 350, "column": 3}, {"title": "效能基準測試", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "36db533b87da1986570b-bf8d21c12cd9a42c8a2f", "file": "user-workflows/complete-user-journey.spec.ts", "line": 380, "column": 3}, {"title": "跨瀏覽器相容性基本測試", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "36db533b87da1986570b-22c95fcda79cdd741adb", "file": "user-workflows/complete-user-journey.spec.ts", "line": 428, "column": 3}, {"title": "完整工作流程：專案建立 → 聊天生成組件 → 編輯程式碼 → 預覽結果", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "36db533b87da1986570b-e6be808b6eceb54d3a30", "file": "user-workflows/complete-user-journey.spec.ts", "line": 38, "column": 3}, {"title": "錯誤處理和恢復流程", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "36db533b87da1986570b-64ddc0880c78b18ab8d2", "file": "user-workflows/complete-user-journey.spec.ts", "line": 183, "column": 3}, {"title": "多檔案專案管理流程", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "36db533b87da1986570b-0e781cce93d7f56a5026", "file": "user-workflows/complete-user-journey.spec.ts", "line": 255, "column": 3}, {"title": "AI 聊天進階功能測試", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "36db533b87da1986570b-6793e980bc9d67cfffbb", "file": "user-workflows/complete-user-journey.spec.ts", "line": 350, "column": 3}, {"title": "效能基準測試", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "36db533b87da1986570b-ffd8bd7dc9d629d977ad", "file": "user-workflows/complete-user-journey.spec.ts", "line": 380, "column": 3}, {"title": "跨瀏覽器相容性基本測試", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "36db533b87da1986570b-2b810406d60f5915f15c", "file": "user-workflows/complete-user-journey.spec.ts", "line": 428, "column": 3}, {"title": "完整工作流程：專案建立 → 聊天生成組件 → 編輯程式碼 → 預覽結果", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "36db533b87da1986570b-20d088c99f9a188da7d2", "file": "user-workflows/complete-user-journey.spec.ts", "line": 38, "column": 3}, {"title": "錯誤處理和恢復流程", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "36db533b87da1986570b-04a52ab7338bdc15a075", "file": "user-workflows/complete-user-journey.spec.ts", "line": 183, "column": 3}, {"title": "多檔案專案管理流程", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "36db533b87da1986570b-cfce6e84a5179eff4cfd", "file": "user-workflows/complete-user-journey.spec.ts", "line": 255, "column": 3}, {"title": "AI 聊天進階功能測試", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "36db533b87da1986570b-b34add7748058ed5da64", "file": "user-workflows/complete-user-journey.spec.ts", "line": 350, "column": 3}, {"title": "效能基準測試", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "36db533b87da1986570b-0d053dbef8efdc88327b", "file": "user-workflows/complete-user-journey.spec.ts", "line": 380, "column": 3}, {"title": "跨瀏覽器相容性基本測試", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "36db533b87da1986570b-bc9957a0f80936303012", "file": "user-workflows/complete-user-journey.spec.ts", "line": 428, "column": 3}]}]}, {"title": "user-workflows/create-project.spec.ts", "file": "user-workflows/create-project.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "專案建立工作流程", "file": "user-workflows/create-project.spec.ts", "line": 4, "column": 6, "specs": [{"title": "應該能夠建立新專案", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "c173e5f6870c9738a1aa-d4dd370b0b594019d84e", "file": "user-workflows/create-project.spec.ts", "line": 16, "column": 3}, {"title": "應該能夠開啟現有專案", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "c173e5f6870c9738a1aa-c62adcbc5b5a0bc49079", "file": "user-workflows/create-project.spec.ts", "line": 48, "column": 3}, {"title": "應該能夠刪除專案", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "c173e5f6870c9738a1aa-5eb5903e4053f78dadf6", "file": "user-workflows/create-project.spec.ts", "line": 69, "column": 3}, {"title": "應該能夠複製專案", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "c173e5f6870c9738a1aa-8335e9c7583e4da86d7a", "file": "user-workflows/create-project.spec.ts", "line": 94, "column": 3}, {"title": "應該能夠搜尋專案", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "c173e5f6870c9738a1aa-7f561697ecdf5b7359bb", "file": "user-workflows/create-project.spec.ts", "line": 123, "column": 3}, {"title": "應該能夠排序專案", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "c173e5f6870c9738a1aa-4264a6f00f83cc2f72bf", "file": "user-workflows/create-project.spec.ts", "line": 152, "column": 3}, {"title": "應該處理專案建立錯誤", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "c173e5f6870c9738a1aa-5725fd428c9c6c208b34", "file": "user-workflows/create-project.spec.ts", "line": 185, "column": 3}, {"title": "應該支援專案匯入和匯出", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "c173e5f6870c9738a1aa-22cb309117abfaade0d6", "file": "user-workflows/create-project.spec.ts", "line": 208, "column": 3}, {"title": "應該能夠建立新專案", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "c173e5f6870c9738a1aa-9b3ff6d59faa9c55d31e", "file": "user-workflows/create-project.spec.ts", "line": 16, "column": 3}, {"title": "應該能夠開啟現有專案", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "c173e5f6870c9738a1aa-d9be114d559c2bfbd0a2", "file": "user-workflows/create-project.spec.ts", "line": 48, "column": 3}, {"title": "應該能夠刪除專案", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "c173e5f6870c9738a1aa-a8d3f07cfc78c54f0a6c", "file": "user-workflows/create-project.spec.ts", "line": 69, "column": 3}, {"title": "應該能夠複製專案", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "c173e5f6870c9738a1aa-89b43727f63ec6e1307c", "file": "user-workflows/create-project.spec.ts", "line": 94, "column": 3}, {"title": "應該能夠搜尋專案", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "c173e5f6870c9738a1aa-12b0c557a085dadf965b", "file": "user-workflows/create-project.spec.ts", "line": 123, "column": 3}, {"title": "應該能夠排序專案", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "c173e5f6870c9738a1aa-08cec5083af111eea4d6", "file": "user-workflows/create-project.spec.ts", "line": 152, "column": 3}, {"title": "應該處理專案建立錯誤", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "c173e5f6870c9738a1aa-2b2a472f066001f0b6c3", "file": "user-workflows/create-project.spec.ts", "line": 185, "column": 3}, {"title": "應該支援專案匯入和匯出", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "c173e5f6870c9738a1aa-1da07ad937bd886af056", "file": "user-workflows/create-project.spec.ts", "line": 208, "column": 3}, {"title": "應該能夠建立新專案", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "c173e5f6870c9738a1aa-17b24e7cd08865b0d636", "file": "user-workflows/create-project.spec.ts", "line": 16, "column": 3}, {"title": "應該能夠開啟現有專案", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "c173e5f6870c9738a1aa-26cb44f2de07614a38c0", "file": "user-workflows/create-project.spec.ts", "line": 48, "column": 3}, {"title": "應該能夠刪除專案", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "c173e5f6870c9738a1aa-1d07fe082f0b4a309b50", "file": "user-workflows/create-project.spec.ts", "line": 69, "column": 3}, {"title": "應該能夠複製專案", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "c173e5f6870c9738a1aa-a8ab9a54c7e0d365494e", "file": "user-workflows/create-project.spec.ts", "line": 94, "column": 3}, {"title": "應該能夠搜尋專案", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "c173e5f6870c9738a1aa-b2bf7cbf1b27eb43fc5d", "file": "user-workflows/create-project.spec.ts", "line": 123, "column": 3}, {"title": "應該能夠排序專案", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "c173e5f6870c9738a1aa-44478b9822b0182ab7cc", "file": "user-workflows/create-project.spec.ts", "line": 152, "column": 3}, {"title": "應該處理專案建立錯誤", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "c173e5f6870c9738a1aa-f2ece0a9f783d35cf1bc", "file": "user-workflows/create-project.spec.ts", "line": 185, "column": 3}, {"title": "應該支援專案匯入和匯出", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "c173e5f6870c9738a1aa-7775ce40d440bd06ec7a", "file": "user-workflows/create-project.spec.ts", "line": 208, "column": 3}, {"title": "應該能夠建立新專案", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "c173e5f6870c9738a1aa-ac99a43291e773e53c41", "file": "user-workflows/create-project.spec.ts", "line": 16, "column": 3}, {"title": "應該能夠開啟現有專案", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "c173e5f6870c9738a1aa-de42ce77ee8143c191f0", "file": "user-workflows/create-project.spec.ts", "line": 48, "column": 3}, {"title": "應該能夠刪除專案", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "c173e5f6870c9738a1aa-dcdc55accfc043b7f4d2", "file": "user-workflows/create-project.spec.ts", "line": 69, "column": 3}, {"title": "應該能夠複製專案", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "c173e5f6870c9738a1aa-15982716692682e00c15", "file": "user-workflows/create-project.spec.ts", "line": 94, "column": 3}, {"title": "應該能夠搜尋專案", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "c173e5f6870c9738a1aa-e0e5d73e8973e4c187a0", "file": "user-workflows/create-project.spec.ts", "line": 123, "column": 3}, {"title": "應該能夠排序專案", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "c173e5f6870c9738a1aa-7f237ba7f52f03d0e40f", "file": "user-workflows/create-project.spec.ts", "line": 152, "column": 3}, {"title": "應該處理專案建立錯誤", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "c173e5f6870c9738a1aa-40c6f128c546df7889a2", "file": "user-workflows/create-project.spec.ts", "line": 185, "column": 3}, {"title": "應該支援專案匯入和匯出", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [], "status": "skipped"}], "id": "c173e5f6870c9738a1aa-dfab611872189c02dbdb", "file": "user-workflows/create-project.spec.ts", "line": 208, "column": 3}, {"title": "應該能夠建立新專案", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "c173e5f6870c9738a1aa-547d368911658b85e485", "file": "user-workflows/create-project.spec.ts", "line": 16, "column": 3}, {"title": "應該能夠開啟現有專案", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "c173e5f6870c9738a1aa-2168b5d091f05d1675f0", "file": "user-workflows/create-project.spec.ts", "line": 48, "column": 3}, {"title": "應該能夠刪除專案", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "c173e5f6870c9738a1aa-c76d5e847b3826b71c95", "file": "user-workflows/create-project.spec.ts", "line": 69, "column": 3}, {"title": "應該能夠複製專案", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "c173e5f6870c9738a1aa-b5f9294173cd3b0e9e8d", "file": "user-workflows/create-project.spec.ts", "line": 94, "column": 3}, {"title": "應該能夠搜尋專案", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "c173e5f6870c9738a1aa-d5663df996b5b2d673da", "file": "user-workflows/create-project.spec.ts", "line": 123, "column": 3}, {"title": "應該能夠排序專案", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "c173e5f6870c9738a1aa-2671de9cbefe53a06e90", "file": "user-workflows/create-project.spec.ts", "line": 152, "column": 3}, {"title": "應該處理專案建立錯誤", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "c173e5f6870c9738a1aa-9ac98e200c6193930900", "file": "user-workflows/create-project.spec.ts", "line": 185, "column": 3}, {"title": "應該支援專案匯入和匯出", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [], "status": "skipped"}], "id": "c173e5f6870c9738a1aa-977b8cba5952b622c039", "file": "user-workflows/create-project.spec.ts", "line": 208, "column": 3}]}]}], "errors": [], "stats": {"startTime": "2025-07-27T01:20:25.284Z", "duration": 237.50599999999997, "expected": 0, "skipped": 125, "unexpected": 0, "flaky": 0}}