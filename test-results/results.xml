<testsuites id="" name="" tests="125" failures="0" skipped="125" errors="0" time="0.23750599999999997">
<testsuite name="user-workflows/chat-with-ai.spec.ts" timestamp="2025-07-27T01:20:25.521Z" hostname="chromium" tests="11" failures="0" skipped="11" time="0" errors="0">
<testcase name="AI 聊天功能測試 › 基本聊天功能" classname="user-workflows/chat-with-ai.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="AI 聊天功能測試 › 複雜組件生成" classname="user-workflows/chat-with-ai.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="AI 聊天功能測試 › 程式碼修改和優化" classname="user-workflows/chat-with-ai.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="AI 聊天功能測試 › 錯誤處理和修正" classname="user-workflows/chat-with-ai.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="AI 聊天功能測試 › 多語言和國際化支援" classname="user-workflows/chat-with-ai.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="AI 聊天功能測試 › 響應式設計請求" classname="user-workflows/chat-with-ai.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="AI 聊天功能測試 › API 整合組件" classname="user-workflows/chat-with-ai.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="AI 聊天功能測試 › 動畫和過渡效果" classname="user-workflows/chat-with-ai.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="AI 聊天功能測試 › 聊天歷史和上下文" classname="user-workflows/chat-with-ai.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="AI 聊天功能測試 › 效能優化建議" classname="user-workflows/chat-with-ai.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="AI 聊天功能測試 › 無障礙功能支援" classname="user-workflows/chat-with-ai.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="user-workflows/complete-user-journey.spec.ts" timestamp="2025-07-27T01:20:25.521Z" hostname="chromium" tests="6" failures="0" skipped="6" time="0" errors="0">
<testcase name="完整使用者場景測試 › 完整工作流程：專案建立 → 聊天生成組件 → 編輯程式碼 → 預覽結果" classname="user-workflows/complete-user-journey.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="完整使用者場景測試 › 錯誤處理和恢復流程" classname="user-workflows/complete-user-journey.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="完整使用者場景測試 › 多檔案專案管理流程" classname="user-workflows/complete-user-journey.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="完整使用者場景測試 › AI 聊天進階功能測試" classname="user-workflows/complete-user-journey.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="完整使用者場景測試 › 效能基準測試" classname="user-workflows/complete-user-journey.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="完整使用者場景測試 › 跨瀏覽器相容性基本測試" classname="user-workflows/complete-user-journey.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="user-workflows/create-project.spec.ts" timestamp="2025-07-27T01:20:25.521Z" hostname="chromium" tests="8" failures="0" skipped="8" time="0" errors="0">
<testcase name="專案建立工作流程 › 應該能夠建立新專案" classname="user-workflows/create-project.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="專案建立工作流程 › 應該能夠開啟現有專案" classname="user-workflows/create-project.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="專案建立工作流程 › 應該能夠刪除專案" classname="user-workflows/create-project.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="專案建立工作流程 › 應該能夠複製專案" classname="user-workflows/create-project.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="專案建立工作流程 › 應該能夠搜尋專案" classname="user-workflows/create-project.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="專案建立工作流程 › 應該能夠排序專案" classname="user-workflows/create-project.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="專案建立工作流程 › 應該處理專案建立錯誤" classname="user-workflows/create-project.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="專案建立工作流程 › 應該支援專案匯入和匯出" classname="user-workflows/create-project.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="user-workflows/chat-with-ai.spec.ts" timestamp="2025-07-27T01:20:25.521Z" hostname="firefox" tests="11" failures="0" skipped="11" time="0" errors="0">
<testcase name="AI 聊天功能測試 › 基本聊天功能" classname="user-workflows/chat-with-ai.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="AI 聊天功能測試 › 複雜組件生成" classname="user-workflows/chat-with-ai.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="AI 聊天功能測試 › 程式碼修改和優化" classname="user-workflows/chat-with-ai.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="AI 聊天功能測試 › 錯誤處理和修正" classname="user-workflows/chat-with-ai.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="AI 聊天功能測試 › 多語言和國際化支援" classname="user-workflows/chat-with-ai.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="AI 聊天功能測試 › 響應式設計請求" classname="user-workflows/chat-with-ai.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="AI 聊天功能測試 › API 整合組件" classname="user-workflows/chat-with-ai.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="AI 聊天功能測試 › 動畫和過渡效果" classname="user-workflows/chat-with-ai.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="AI 聊天功能測試 › 聊天歷史和上下文" classname="user-workflows/chat-with-ai.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="AI 聊天功能測試 › 效能優化建議" classname="user-workflows/chat-with-ai.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="AI 聊天功能測試 › 無障礙功能支援" classname="user-workflows/chat-with-ai.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="user-workflows/complete-user-journey.spec.ts" timestamp="2025-07-27T01:20:25.521Z" hostname="firefox" tests="6" failures="0" skipped="6" time="0" errors="0">
<testcase name="完整使用者場景測試 › 完整工作流程：專案建立 → 聊天生成組件 → 編輯程式碼 → 預覽結果" classname="user-workflows/complete-user-journey.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="完整使用者場景測試 › 錯誤處理和恢復流程" classname="user-workflows/complete-user-journey.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="完整使用者場景測試 › 多檔案專案管理流程" classname="user-workflows/complete-user-journey.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="完整使用者場景測試 › AI 聊天進階功能測試" classname="user-workflows/complete-user-journey.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="完整使用者場景測試 › 效能基準測試" classname="user-workflows/complete-user-journey.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="完整使用者場景測試 › 跨瀏覽器相容性基本測試" classname="user-workflows/complete-user-journey.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="user-workflows/create-project.spec.ts" timestamp="2025-07-27T01:20:25.521Z" hostname="firefox" tests="8" failures="0" skipped="8" time="0" errors="0">
<testcase name="專案建立工作流程 › 應該能夠建立新專案" classname="user-workflows/create-project.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="專案建立工作流程 › 應該能夠開啟現有專案" classname="user-workflows/create-project.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="專案建立工作流程 › 應該能夠刪除專案" classname="user-workflows/create-project.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="專案建立工作流程 › 應該能夠複製專案" classname="user-workflows/create-project.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="專案建立工作流程 › 應該能夠搜尋專案" classname="user-workflows/create-project.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="專案建立工作流程 › 應該能夠排序專案" classname="user-workflows/create-project.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="專案建立工作流程 › 應該處理專案建立錯誤" classname="user-workflows/create-project.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="專案建立工作流程 › 應該支援專案匯入和匯出" classname="user-workflows/create-project.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="user-workflows/chat-with-ai.spec.ts" timestamp="2025-07-27T01:20:25.521Z" hostname="webkit" tests="11" failures="0" skipped="11" time="0" errors="0">
<testcase name="AI 聊天功能測試 › 基本聊天功能" classname="user-workflows/chat-with-ai.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="AI 聊天功能測試 › 複雜組件生成" classname="user-workflows/chat-with-ai.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="AI 聊天功能測試 › 程式碼修改和優化" classname="user-workflows/chat-with-ai.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="AI 聊天功能測試 › 錯誤處理和修正" classname="user-workflows/chat-with-ai.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="AI 聊天功能測試 › 多語言和國際化支援" classname="user-workflows/chat-with-ai.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="AI 聊天功能測試 › 響應式設計請求" classname="user-workflows/chat-with-ai.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="AI 聊天功能測試 › API 整合組件" classname="user-workflows/chat-with-ai.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="AI 聊天功能測試 › 動畫和過渡效果" classname="user-workflows/chat-with-ai.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="AI 聊天功能測試 › 聊天歷史和上下文" classname="user-workflows/chat-with-ai.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="AI 聊天功能測試 › 效能優化建議" classname="user-workflows/chat-with-ai.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="AI 聊天功能測試 › 無障礙功能支援" classname="user-workflows/chat-with-ai.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="user-workflows/complete-user-journey.spec.ts" timestamp="2025-07-27T01:20:25.521Z" hostname="webkit" tests="6" failures="0" skipped="6" time="0" errors="0">
<testcase name="完整使用者場景測試 › 完整工作流程：專案建立 → 聊天生成組件 → 編輯程式碼 → 預覽結果" classname="user-workflows/complete-user-journey.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="完整使用者場景測試 › 錯誤處理和恢復流程" classname="user-workflows/complete-user-journey.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="完整使用者場景測試 › 多檔案專案管理流程" classname="user-workflows/complete-user-journey.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="完整使用者場景測試 › AI 聊天進階功能測試" classname="user-workflows/complete-user-journey.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="完整使用者場景測試 › 效能基準測試" classname="user-workflows/complete-user-journey.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="完整使用者場景測試 › 跨瀏覽器相容性基本測試" classname="user-workflows/complete-user-journey.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="user-workflows/create-project.spec.ts" timestamp="2025-07-27T01:20:25.521Z" hostname="webkit" tests="8" failures="0" skipped="8" time="0" errors="0">
<testcase name="專案建立工作流程 › 應該能夠建立新專案" classname="user-workflows/create-project.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="專案建立工作流程 › 應該能夠開啟現有專案" classname="user-workflows/create-project.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="專案建立工作流程 › 應該能夠刪除專案" classname="user-workflows/create-project.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="專案建立工作流程 › 應該能夠複製專案" classname="user-workflows/create-project.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="專案建立工作流程 › 應該能夠搜尋專案" classname="user-workflows/create-project.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="專案建立工作流程 › 應該能夠排序專案" classname="user-workflows/create-project.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="專案建立工作流程 › 應該處理專案建立錯誤" classname="user-workflows/create-project.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="專案建立工作流程 › 應該支援專案匯入和匯出" classname="user-workflows/create-project.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="user-workflows/chat-with-ai.spec.ts" timestamp="2025-07-27T01:20:25.521Z" hostname="Mobile Chrome" tests="11" failures="0" skipped="11" time="0" errors="0">
<testcase name="AI 聊天功能測試 › 基本聊天功能" classname="user-workflows/chat-with-ai.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="AI 聊天功能測試 › 複雜組件生成" classname="user-workflows/chat-with-ai.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="AI 聊天功能測試 › 程式碼修改和優化" classname="user-workflows/chat-with-ai.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="AI 聊天功能測試 › 錯誤處理和修正" classname="user-workflows/chat-with-ai.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="AI 聊天功能測試 › 多語言和國際化支援" classname="user-workflows/chat-with-ai.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="AI 聊天功能測試 › 響應式設計請求" classname="user-workflows/chat-with-ai.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="AI 聊天功能測試 › API 整合組件" classname="user-workflows/chat-with-ai.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="AI 聊天功能測試 › 動畫和過渡效果" classname="user-workflows/chat-with-ai.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="AI 聊天功能測試 › 聊天歷史和上下文" classname="user-workflows/chat-with-ai.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="AI 聊天功能測試 › 效能優化建議" classname="user-workflows/chat-with-ai.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="AI 聊天功能測試 › 無障礙功能支援" classname="user-workflows/chat-with-ai.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="user-workflows/complete-user-journey.spec.ts" timestamp="2025-07-27T01:20:25.521Z" hostname="Mobile Chrome" tests="6" failures="0" skipped="6" time="0" errors="0">
<testcase name="完整使用者場景測試 › 完整工作流程：專案建立 → 聊天生成組件 → 編輯程式碼 → 預覽結果" classname="user-workflows/complete-user-journey.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="完整使用者場景測試 › 錯誤處理和恢復流程" classname="user-workflows/complete-user-journey.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="完整使用者場景測試 › 多檔案專案管理流程" classname="user-workflows/complete-user-journey.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="完整使用者場景測試 › AI 聊天進階功能測試" classname="user-workflows/complete-user-journey.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="完整使用者場景測試 › 效能基準測試" classname="user-workflows/complete-user-journey.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="完整使用者場景測試 › 跨瀏覽器相容性基本測試" classname="user-workflows/complete-user-journey.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="user-workflows/create-project.spec.ts" timestamp="2025-07-27T01:20:25.521Z" hostname="Mobile Chrome" tests="8" failures="0" skipped="8" time="0" errors="0">
<testcase name="專案建立工作流程 › 應該能夠建立新專案" classname="user-workflows/create-project.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="專案建立工作流程 › 應該能夠開啟現有專案" classname="user-workflows/create-project.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="專案建立工作流程 › 應該能夠刪除專案" classname="user-workflows/create-project.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="專案建立工作流程 › 應該能夠複製專案" classname="user-workflows/create-project.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="專案建立工作流程 › 應該能夠搜尋專案" classname="user-workflows/create-project.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="專案建立工作流程 › 應該能夠排序專案" classname="user-workflows/create-project.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="專案建立工作流程 › 應該處理專案建立錯誤" classname="user-workflows/create-project.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="專案建立工作流程 › 應該支援專案匯入和匯出" classname="user-workflows/create-project.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="user-workflows/chat-with-ai.spec.ts" timestamp="2025-07-27T01:20:25.521Z" hostname="Mobile Safari" tests="11" failures="0" skipped="11" time="0" errors="0">
<testcase name="AI 聊天功能測試 › 基本聊天功能" classname="user-workflows/chat-with-ai.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="AI 聊天功能測試 › 複雜組件生成" classname="user-workflows/chat-with-ai.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="AI 聊天功能測試 › 程式碼修改和優化" classname="user-workflows/chat-with-ai.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="AI 聊天功能測試 › 錯誤處理和修正" classname="user-workflows/chat-with-ai.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="AI 聊天功能測試 › 多語言和國際化支援" classname="user-workflows/chat-with-ai.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="AI 聊天功能測試 › 響應式設計請求" classname="user-workflows/chat-with-ai.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="AI 聊天功能測試 › API 整合組件" classname="user-workflows/chat-with-ai.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="AI 聊天功能測試 › 動畫和過渡效果" classname="user-workflows/chat-with-ai.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="AI 聊天功能測試 › 聊天歷史和上下文" classname="user-workflows/chat-with-ai.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="AI 聊天功能測試 › 效能優化建議" classname="user-workflows/chat-with-ai.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="AI 聊天功能測試 › 無障礙功能支援" classname="user-workflows/chat-with-ai.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="user-workflows/complete-user-journey.spec.ts" timestamp="2025-07-27T01:20:25.521Z" hostname="Mobile Safari" tests="6" failures="0" skipped="6" time="0" errors="0">
<testcase name="完整使用者場景測試 › 完整工作流程：專案建立 → 聊天生成組件 → 編輯程式碼 → 預覽結果" classname="user-workflows/complete-user-journey.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="完整使用者場景測試 › 錯誤處理和恢復流程" classname="user-workflows/complete-user-journey.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="完整使用者場景測試 › 多檔案專案管理流程" classname="user-workflows/complete-user-journey.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="完整使用者場景測試 › AI 聊天進階功能測試" classname="user-workflows/complete-user-journey.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="完整使用者場景測試 › 效能基準測試" classname="user-workflows/complete-user-journey.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="完整使用者場景測試 › 跨瀏覽器相容性基本測試" classname="user-workflows/complete-user-journey.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
<testsuite name="user-workflows/create-project.spec.ts" timestamp="2025-07-27T01:20:25.521Z" hostname="Mobile Safari" tests="8" failures="0" skipped="8" time="0" errors="0">
<testcase name="專案建立工作流程 › 應該能夠建立新專案" classname="user-workflows/create-project.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="專案建立工作流程 › 應該能夠開啟現有專案" classname="user-workflows/create-project.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="專案建立工作流程 › 應該能夠刪除專案" classname="user-workflows/create-project.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="專案建立工作流程 › 應該能夠複製專案" classname="user-workflows/create-project.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="專案建立工作流程 › 應該能夠搜尋專案" classname="user-workflows/create-project.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="專案建立工作流程 › 應該能夠排序專案" classname="user-workflows/create-project.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="專案建立工作流程 › 應該處理專案建立錯誤" classname="user-workflows/create-project.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
<testcase name="專案建立工作流程 › 應該支援專案匯入和匯出" classname="user-workflows/create-project.spec.ts" time="0">
<skipped>
</skipped>
</testcase>
</testsuite>
</testsuites>